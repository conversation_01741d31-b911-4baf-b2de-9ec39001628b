before_script:
  # Github upload variables
  - export GH_OWNER=praja
  - export GH_REPO_NAME=app
  - export GH_API=https://api.github.com
  - export GH_REPO=$GH_API/repos/$GH_OWNER/$GH_REPO_NAME
  - export PATH="$PATH:/Users/<USER>/flutter/bin"
  - export PATH="$PATH:/usr/local/Cellar/cocoapods/1.9.1/bin"

stages:
#  - test
#  - update
  - build
  - tag
  - deploy
  - postdeploy

#tests:
#  stage: test
#  only:
#    - master
#  tags:
#    - praja_app
#  script:
#    - flutter test
#  interruptible: true

#update:
#  stage: update
#  only:
#    - master
#  tags:
#    - praja_app
#  script:
#    - flutter packages get
#    - flutter packages upgrade
#  interruptible: true

android:build:
  stage: build
  tags:
    - praja_app
  script:
    # Flutter local configuration
    - echo sdk.dir=$ANDROID_SDK_PATH >> android/local.properties
    - echo flutter.sdk=$FLUTTER_PATH > android/local.properties
    - echo flutter.buildMode=release >> android/local.properties
    # Android signing
    - echo storePassword=$ANDROID_KEY_STORE_PASSWORD > android/key.properties
    - echo keyPassword=$ANDROID_KEY_PASSWORD >> android/key.properties
    - echo keyAlias=$ANDROID_KEY_ALIAS >> android/key.properties
    - echo storeFile=$ANDROID_KEYSTORE_PATH >> android/key.properties
    - cp /Users/<USER>/Projects/praja_app/lib/services/endpoint.dart lib/services/endpoint.dart
    - cd android
    - bundle exec fastlane build_android production:true
    - rm -f android/local.properties
    - rm -f android/key.properties
  artifacts:
    paths:
      - ./build/app/outputs/apk/release/app-release.apk
      - ./build/app/outputs/bundle/release/app-release.aab
    expire_in: 1 week
  interruptible: true

#ios:build:
#  stage: build
#  tags:
#    - praja_app
#  script:
#    - cd ios
#    - gem install cocoapods
#    - export TEMP_KEYCHAIN_NAME=fastlane_$(cat /dev/urandom | LC_ALL=C tr -dc 'a-zA-Z0-9' | fold -w ${1:-16} | head -n 1)
#    - export TEMP_KEYCHAIN_PASSWORD=$(cat /dev/urandom | LC_ALL=C tr -dc 'a-zA-Z0-9' | fold -w ${1:-64} | head -n 1)
#    - bundle exec fastlane build_ios
#  artifacts:
#    paths:
#      - ios/Runner.ipa
#    expire_in: 1 week
#  interruptible: true
  
#prod:android:deploy:
#  stage: deploy
#  tags:
#    - praja_app
#  script:
#    - cd android
#    - bundle exec fastlane deploy_android production:true
#  when: manual
#  dependencies:
#    - android:build

#prod:ios:deploy:
#  stage: deploy
#  tags:
#    - praja_app
#  script:
#    - cd ios
#    - bundle exec fastlane deploy_ios
#  when: manual
#  dependencies:
#    - ios:build

z:internal:android:deploy:
  stage: deploy
  tags:
    - praja_app
  script:
    - cd android
    - bundle exec fastlane beta
  when: manual
  dependencies:
    - android:build

#z:tflight:ios:deploy:
#  stage: deploy
#  tags:
#    - praja_app
#  script:
#    - cd ios
#    - bundle exec fastlane deploy_ios testflight:true
#  when: manual
#  dependencies:
#    - android:build

tag:create:
  stage: tag
  tags:
    - praja_app
  script:
    - ./ci/tag_version.sh
  when: manual

upload:apk:github_release:
  stage: postdeploy
  tags:
    - praja_app
  script:
    ./ci/upload_android_github.sh
  needs: ["android:build"]
  when: manual
  dependencies:
    - android:build