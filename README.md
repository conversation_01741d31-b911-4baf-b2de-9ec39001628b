# Praja App

Local social network app for knowing your local political trends.

## Setup
1. Install [fvm](https://fvm.app/docs/getting_started/installation/)
2. `fvm install 3.19.6`
3. `<NAME_EMAIL>:praja/app.git praja-app && cd praja-app`
4. `fvm use 3.19.0`
5. (Optional) Add `alias flutter=".fvm/flutter_sdk/bin/flutter"` to your shell profile - `.zshrc` / `.zprofile`
6. `flutter doctor` / `fvm flutter doctor`

## IDE Setup
- Install Android Studio
- Preferences > Languages & Frameworks > Flutter > Make sure the flutter SDK is pointing to 3.7.10

## Useful Commands:
- `flutter pub get` - Get all dependencies (to be run post repo cloning)
- `flutter pub run build_runner build` - To regenerate dependency injection, json serialization code
- `flutter doctor` - To identify any issues
- `flutter upgrade` - To upgrade flutter version
- `flutter devices list` - Get all available devices
- `flutter run -d <device_id>` - Run on a device
- `flutter pub run flutter_launcher_icons:main` - To generate app icons
- `dart run build_runner build --delete-conflicting-outputs` - To build generator files
- `adb root; adb pull /data/user/0/buzz.praja.app/app_flutter/debug-logs.txt` to pull logs from a debug app running in an Android device

## Run Integration Test
- `flutter drive --driver ./test_driver/android.dart --target ./integration_test/<test_name>.dart`
> **Note**: tests are currently not working on ios simulator, but we have written a `./test_driver/ios_simulator.dart` if you want to give it a shot

## Test Environment
- file name should contain `test` in it, eg: `your_widget_test.dart`
- `flutter test` - Run all tests
- `flutter test <file_name>` - Run tests in a file, eg: `flutter test test/widget_test.dart`
- `flutter test <file_name> --name <test_name>` - Run a specific test in a file
- `flutter test --coverage` - Generate coverage report
- Note: On macOS, We need generate HTML report
  - For that install `lcov` using `brew install lcov`
  - Run `genhtml coverage/lcov.info -o coverage/html` to generate HTML report
  - Open `coverage/html/index.html` in browser

## Architecture
Refer [Architecture](./docs/architecture.md)

## Connecting to local backend
`flutter run --dart-define LOCAL_IP=<your_local_backend_ip> --dart-define ENVIRONMENT=<local | staging | production>` - Run on your device

## Common Issues:
- Execution failed for task ":xyz:verifyReleaseResources" - [Solution](https://github.com/flutter/flutter/issues/32595#issuecomment-491639786)
- `lintVitalRelease` - [Solution](https://github.com/flutter/flutter/issues/58247#issuecomment-636500680)

### A few resources to get you started if this is your first Flutter project:
- [Lab: Write your first Flutter app](https://flutter.io/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.io/docs/cookbook)

For help getting started with Flutter, view our 
[online documentation](https://flutter.io/docs), which offers tutorials, 
samples, guidance on mobile development, and a full API reference.

## Versioning Scheme 🏷️
`YYMM.DD.N`

`N` ➡️ patch number

Ex: `2301.25.0`, `2302.01.4`

You never have to manually modify the version name or build number. They are automatically updated using Github workflows 🤖

[Why use a date based versioning scheme?](https://github.com/okmanideep/app-branching-strategy#why-use-a-date-based-versioning-scheme)

## Permanent Branches 🛤️
* `master` -> Latest and greatest Dev Tested Code

No other permanent trunks / branches.

### Other branches that come and go ⏳
* `feature/<feature_name>`
* `fix/<bug_title>`
* `release/v-<version.major>.<version.minor>` 
	* Temporary branch that lives while a release is being _prepared_
	* No patch number in branch name because patch number is subject to change as fixes are merged after Testing
* `auto-backmerge/<pr_number>` Used by github-actions 🤖 to automatically raise PRs to master for every change pushed to release branches

## Feature  Development⭐ & Bug Fixing🪲
```mermaid
%%{init: { 'gitGraph': {'mainBranchName': 'master', 'showCommitLabel': true}} }%%

gitGraph
	commit id: "🤖 2301.25.0"
	branch fix/frames-bug
	commit id: "Fix Logic"
	commit id: "Fix UI"
	checkout master
	branch feature/dm
	commit id: "Add DB"
	commit id: "Add Socket"
	checkout master
	merge fix/frames-bug id: "PR: Frames Bug"
	commit id: "🤖 2301.25.1"
	checkout feature/dm
	merge master id: "Pull master"
	commit id: "Add UI"
	checkout master
	merge feature/dm id: "PR: Feature DM"
	commit id: "🤖 2301.25.2"
```

## Releases ⛳
### Preparation ⚒️
**Actions ▶️ > Cut a release > Run Workflow (from `master`)**

This 👆 creates a new release branch based on the current version and uploads the release build to Play Store **Internal Testing track**

To fix bugs that are reported in testing, branch out from the `release/v-<major.minor>` branch and raise a PR to the same. 

Upon merging the PR to the release branch:
* The changes are automatically back merged to `master`
* Patch Version, Version Number are incremented
* A new build is uploaded to Play Store **Internal Testing Track**

Testers can **UPDATE** on their devices and confirm that the bug is fixed.

```mermaid
%%{init: { 'gitGraph': {'mainBranchName': 'master', 'showCommitLabel': true}} }%%

gitGraph
	commit id: "🤖 2301.25.2"
	branch release/v-2301.25
	checkout master
	commit id: "PR: New Feature" type:HIGHLIGHT
	commit id: "🤖 2301.26.0"
	checkout release/v-2301.25
	branch fix/release-bug
	commit id: "Fix bug"
	checkout release/v-2301.25
	merge fix/release-bug id: "PR: Release bug"
	checkout release/v-2301.25
	branch auto-backmerge/release-bug
	merge master id: "Update branch"
	checkout release/v-2301.25
	commit id: "🤖 2301.25.3" tag: "v-2301.25.3"
	checkout master
	merge auto-backmerge/release-bug id: "backmerge"
	checkout master
	commit id: "🤖 2301.26.1"
```

### Making the release 🏌️
Actions ▶️ > Create Tagged Release > Run Workflow (select release branch)

Once we are 🟢 on all tests, just run the above workflow from the release branch.

> The workflow tags the latest commit with the **correct version tag** from the release branch and creates a Github release with automated release notes.

#### Android
Promote the latest release in **Internal Testing** track in Play Store to Production and release for 10%

#### iOS
TBA

### Hotfix 🧯
Fixing an issue in current production build (`v-2301.16.1`)
1. Create a branch from the release tag (tag: `v-2301.16.1` -> branch: `release/v-2301.16`)

The rest is same as normal release flow

```mermaid
%%{init: { 'gitGraph': {'mainBranchName': 'release/v-2301.16', 'showCommitLabel': true}} }%%

gitGraph
	commit id: "2301.16.4" tag: "v-2301.16.4"
	branch hotfix/crash
	commit id: "Fix crash"
	checkout release/v-2301.16
	merge hotfix/crash id: "PR Crash"
	commit id: "🤖 2301.16.5"
	branch hotfix/bug
	commit id: "Fix bug"
	checkout release/v-2301.16
	merge hotfix/bug id: "PR Bug"
	commit id: "🤖 2301.16.6" tag: "v-2301.16.6"

```
