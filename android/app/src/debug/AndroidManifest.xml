<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
	xmlns:tools="http://schemas.android.com/tools"
	package="buzz.praja.app">
		<!-- Flutter needs it to communicate with the running application
			to allow setting breakpoints, to provide hot reload, etc.
		-->
	<uses-permission android:name="android.permission.INTERNET"/>


	<meta-data android:name="io.branch.sdk.BranchKey.test" android:value="key_test_dhIXmBVjSzm1lszhKLjb8maesroFCaWE" />
	<meta-data android:name="io.branch.sdk.TestMode" android:value="true" />
	<application
		android:name=".MainApp">
		<activity
			android:name=".MainActivity">
			<!-- App Links -->
			<intent-filter android:autoVerify="true">
				<action android:name="android.intent.action.VIEW" />

				<category android:name="android.intent.category.DEFAULT" />
				<category android:name="android.intent.category.BROWSABLE" />
				<data
					android:host="praja.test-app.link"
					android:scheme="https" />
				<data
					android:host="praja-alternate.test-app.link"
					android:scheme="https" />
			</intent-filter>
		</activity>
	</application>
</manifest>
