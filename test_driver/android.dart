import 'dart:io';

import 'package:integration_test/integration_test_driver.dart';

Future<void> main() async {
  [
    'android.permission.READ_CONTACTS',
    'android.permission.WRITE_CONTACTS',
    'android.permission.GET_ACCOUNTS',
    'android.permission.POST_NOTIFICATIONS',
  ].forEach(grantPermission);
  await integrationDriver();
}

Future<void> grantPermission(permission) async {
  await Process.run(
    'adb',
    ['shell', 'pm', 'grant', 'buzz.praja.app', permission],
  );
}
