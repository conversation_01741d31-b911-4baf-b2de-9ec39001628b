# frozen_string_literal: true

# Usage:
# From the root of this project
# $ ruby scripts/translation_coverage_check.rb

require 'yaml'

def parse_enum_values(dart_file_path, enum_name)
  enum_values = []
  enum_started = false
  File.open(dart_file_path).each_line do |line|
    if !enum_started and line.include?("enum $enum_name")
      enum_started = true
      next
    end

    matches = line.scan(/\w+\('([a-z0-9_]+)'\)/)
    if matches.any?
      enum_values.concat(matches.flatten)
    end

    # for multi-line enum values
    matches = line.scan(/\s+'([a-z0-9_]+)'\)/)
    if matches.any?
      enum_values.concat(matches[0])
    end
  end
  enum_values
end

string_keys = parse_enum_values('lib/features/localization/string_key.dart', 'StringKey')
string_keys = string_keys.sort

lang_codes = parse_enum_values('lib/features/localization/lang_code.dart', 'LangCode')

exit_with_error = false
lang_codes.each do |lang_code|
  yaml_file_path = "assets/strings/#{lang_code}.yaml"
  yaml_data = YAML.load_file(yaml_file_path)
  yaml_keys = yaml_data.keys.sort

  if yaml_keys != string_keys
    missing_keys = string_keys - yaml_keys
    extra_keys = yaml_keys - string_keys

    if missing_keys.any?
      exit_with_error = true
      puts "Missing keys in #{lang_code}.yaml:"
      missing_keys.each do |key|
        puts key
      end
    end

    if missing_keys.any? and extra_keys.any?
      puts ''
    end

    if extra_keys.any?
      exit_with_error = true
      puts "Unused translations in #{lang_code}.yaml:"
      extra_keys.each do |key|
        puts key
      end
    end
  end
end

if exit_with_error
  exit(1)
else
  puts 'All translations are in sync'
end
