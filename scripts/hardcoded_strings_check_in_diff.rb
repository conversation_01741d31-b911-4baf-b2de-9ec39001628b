# frozen_string_literal: true

# Usage:
# From the root of this project
# $ ruby scripts/hardcoded_strings_check_in_diff.rb

BASE_REF = ENV['GITHUB_BASE_REF'] || 'master'

def is_excluded(file)
  file.start_with?('lib/test/') || file.end_with?('.g.dart')
end

# return the list of file names that have changed from the base ref
def get_changed_dart_files(base_ref)
  `git diff --name-only #{base_ref}`.split("\n").select { |file| file.end_with?('.dart') && !is_excluded(file) }
end

# echo the list of files that have changed one file per line
changed_files = get_changed_dart_files(BASE_REF)

def files_with_hardcoded_strings
  output_lines = `flutter pub run custom_lint`.split("\n")
  files = []
  output_lines.each do |line|
    if line.include?('avoid_hardcoded_strings_in_ui')
      files << line.split(':')[0].strip
    end
  end
  files
end

files_containing_hardcoded_strings = files_with_hardcoded_strings

# check if any of the changed files contain hardcoded strings
changed_files_with_hardcoded_strings = []
changed_files.each do |file|
  if files_containing_hardcoded_strings.include?(file)
    changed_files_with_hardcoded_strings << file
  end
end

if changed_files_with_hardcoded_strings.empty?
  puts 'No changed files contain hardcoded strings'
else
  puts 'Changed files that contain hardcoded strings:'
  changed_files_with_hardcoded_strings.each do |file|
    puts file
  end
  puts 'Please refer docs/localization.md to move strings to their respective locale YAML files'
  exit(1)
end
