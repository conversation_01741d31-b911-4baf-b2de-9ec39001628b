# frozen_string_literal: true

# Usage:
# From the root of this project
# $ ruby scripts/null_safe_reportt.rb ./lib
require 'find'

# This class is used to sort the files by line count
class FileToConvert
  def initialize(file_name, line_count)
    @file_name = file_name
    @line_count = line_count
  end

  attr_reader :line_count

  def to_s
    "#{@file_name}: #{@line_count} lines"
  end
end

ignore_patterns = [
  /.*\.g\.dart/
]

def file_ignored?(file_name, ignore_patterns)
  ignore_patterns.any? { |pattern| file_name.match?(pattern) }
end

path = ARGV[0]

file_count = 0
ignored_count = 0
files_to_convert = []
Dir.glob("#{path}/**/*.dart") do |file_name|
  file_count += 1
  first_line = File.open(file_name, &:readline)
  is_non_null_safe = first_line.start_with?('// @dart = 2.9')
  is_ignored = file_ignored?(file_name, ignore_patterns)
  ignored_count += 1 if is_ignored
  files_to_convert << FileToConvert.new(file_name, File.foreach(file_name).count) if is_non_null_safe && !is_ignored
end

files_to_convert.sort_by!(&:line_count)

puts "Null safe percent: #{((file_count - files_to_convert.count) / file_count.to_f * 100).round(2)}%"
puts "Number of files left to convert: #{files_to_convert.count}"
puts ''
puts '25 Smallest files left to convert:'
files_to_convert[0..24].each do |file|
  puts file
end
