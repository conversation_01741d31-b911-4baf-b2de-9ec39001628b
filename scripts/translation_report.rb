# frozen_string_literal: true

require 'yaml'

def files_with_hardcoded_strings
  output_lines = `flutter pub run custom_lint`.split("\n")
  hard_coded_strings_count = output_lines.count

  yaml_file_path = 'assets/strings/en.yaml'
  yaml_data = YAML.load_file(yaml_file_path)
  yaml_keys_count = yaml_data.keys.count

  file_counts = Hash.new(0)

  output_lines.each do |line|
    if line.include?('avoid_hardcoded_strings_in_ui')
      file = line.split(':')[0].strip
      file_counts[file] += 1
    end
  end

  puts "Top 10 files with more hardcoded strings:"
  file_counts.sort_by { |_, count| count }.last(10).each do |file, count|
    puts "#{file} - #{count}"
  end

  puts "************************************"

  puts "Total Files with hardcoded strings: #{file_counts.keys.count}"

  percentage = (yaml_keys_count.to_f / (yaml_keys_count + hard_coded_strings_count).to_f) * 100
  puts "Localisation Progress: #{percentage.round(2)}%"
end

files_with_hardcoded_strings