# frozen_string_literal: true

# Usage:
# From the root of this project
# $ ruby scripts/warnings_check_in_diff.rb

BASE_REF = ENV['GITHUB_BASE_REF'] || 'master'

def excluded?(_filename)
  # placeholder for future exclusion logics
  false
end

# return the list of file names that have changed from the base ref
def changed_dart_files(base_ref)
  `git diff --name-only #{base_ref}`.split("\n").select { |file| file.end_with?('.dart') && !excluded?(file) }
end

# Simple class to encapsulate information present in a lint message
class LintMessage
  attr_reader :level, :message, :file_path, :line_number, :column_number, :rule_id

  def initialize(level, message, file_path, line_number, column_number, rule_id) # rubocop: disable Metrics/ParameterLists
    @level = level
    @message = message
    @file_path = file_path
    @line_number = line_number
    @column_number = column_number
    @rule_id = rule_id
  end

  def self.parse(line)
    # Sample output line
    # info • Convert 'key' to a super parameter • lib/common/az_list_view/index_bar.dart:224:3 • use_super_parameters
    #
    # Format:
    # #{level} • #{message} • #{file_path}:#{line_number}:#{column_number} • #{rule_id}
    parts = line.split(' • ')
    level = parts[0].strip
    message = parts[1].strip
    file_path, line_number, column_number = parts[2].split(':')
    rule_id = parts[3].strip
    LintMessage.new(level, message, file_path, line_number, column_number, rule_id)
  end

  def self.matches?(line)
    line.match?(/^(\s+)?(info|warning|error) • .+ • .+:\d+:\d+ • .+$/)
  end

  def warning?
    level == 'warning'
  end

  def to_s
    "#{level} • #{message} • #{file_path}:#{line_number}:#{column_number} • #{rule_id}"
  end
end

def lint_messages
  lint_output_lines = `flutter analyze --no-fatal-warnings --no-fatal-infos`.split("\n")
  ret = []
  lint_output_lines.each do |line|
    ret << LintMessage.parse(line) if LintMessage.matches?(line)
  end
  ret
end

changed_files = changed_dart_files(BASE_REF)
exit(0) if changed_files.empty?
messages = lint_messages

relevant_messages = messages.select { |message| message.warning? && changed_files.include?(message.file_path) }

if relevant_messages.empty?
  puts 'No warnings in changed dart files'
else
  relevant_messages.each do |lint_message|
    puts lint_message
  end
  abort '⚠️ Changed files have warnings'
end
