import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:praja/main.dart' as app;

import 'robots/home_robot.dart';
import 'robots/login_robot.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('hashtag page test', () {
    testWidgets('create post from hashtag page should pre-populate the hashtag',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();
      await LoginRobot(tester).performLoginWithAutoFilledOTP();

      final homeRobot = HomeRobot(tester);
      await homeRobot.expectMyFeedIsVisible();

      await homeRobot.tapTrendingFeedTab();
      await homeRobot.expectHashtagsVisible();
    });
  });
}
