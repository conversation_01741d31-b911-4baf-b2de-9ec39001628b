import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

class HomeRobot {
  final WidgetTester _tester;

  HomeRobot(this._tester);

  final _myFeedTab = find.text('మీ కోసం');

  Future<void> expectMyFeedIsVisible() async {
    await _tester.pumpAndSettle();
    expect(_myFeedTab, findsOneWidget);
  }

  Future<void> closePostersIfVisible() async {
    await _tester.pumpAndSettle();
    if (find.byIcon(Icons.close).evaluate().isNotEmpty) {
      await _tester.tap(find.byIcon(Icons.close));
      await _tester.pumpAndSettle();
    }
  }

  Future<void> tapTrendingFeedTab() async {
    await _tester.tap(find.text('ట్రెండింగ్'));
    await _tester.pumpAndSettle();
  }

  Future<void> expectHashtagsVisible() async {
    await _tester.pumpAndSettle();
    expect(find.byIcon(Icons.trending_up), findsWidgets);
  }
}
