import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

class LoginRobot {
  final WidgetTester _tester;

  LoginRobot(this._tester);

  static const _autofillPhoneNumber = '3210987654';
  final _loginOrJoinButton = find.text('లాగిన్ / జాయిన్');
  final _loginButton = find.text('లాగిన్');

  Future<void> performLoginWithAutoFilledOTP() async {
    await expectVisible();
    await enterPhoneNumber(_autofillPhoneNumber);
    await tapLoginOrJoinButton();
    // otp is auto filled, so we can directly tap login button
    await tapLoginButton();
  }

  Future<void> expectVisible() async {
    await _tester.pumpAndSettle();
    expect(_loginOrJoinButton, findsOneWidget);
  }

  Future<void> enterPhoneNumber(String phoneNumber) async {
    await _tester.pumpAndSettle();
    await _tester.enterText(find.byType(TextFormField), phoneNumber);
  }

  Future<void> tapLoginOrJoinButton() async {
    await _tester.pumpAndSettle();
    await _tester.tap(_loginOrJoinButton);
  }

  Future<void> tapLoginButton() async {
    await _tester.pumpAndSettle();
    await _tester.tap(_loginButton);
  }
}
