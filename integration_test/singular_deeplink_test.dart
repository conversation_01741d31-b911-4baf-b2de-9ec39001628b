import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:url_launcher/url_launcher.dart';

import 'package:praja/main.dart' as app;
import 'robots/login_robot.dart';
import 'robots/profile_robot.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('singular deeplink test', () {
    testWidgets(
        'should open user profile from singular deeplink of the profile',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();
      await LoginRobot(tester).performLoginWithAutoFilledOTP();

      // link to user profile
      launchUrl(Uri.parse('praja://prajaapp.sng.link/B3x5b/82yd/r_9120bc0c4d'),
          mode: LaunchMode.externalApplication);

      final profileRobot = ProfileRobot(tester);
      await profileRobot.expectProfileInformationVisible();
    });
  });
}
