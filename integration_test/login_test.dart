import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:praja/main.dart' as app;

import 'robots/home_robot.dart';
import 'robots/login_robot.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('login test', () {
    testWidgets('login with autofill phone number',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      final loginRobot = LoginRobot(tester);

      // Verify the login screen is visible
      await loginRobot.performLoginWithAutoFilledOTP();

      final homeRobot = HomeRobot(tester);
      await homeRobot.closePostersIfVisible();
      await homeRobot.expectMyFeedIsVisible();
    });
  });
}
