import 'dart:ui' as ui show ImageByteFormat;

import 'package:flutter_quick_video_encoder/flutter_quick_video_encoder.dart';
import 'frame.dart';

class Exporter {
  final List<Frame> _frames = [];

  List<Frame> get frames => _frames;

  void onNewFrame(Frame frame) {
    _frames.add(frame);
  }

  void clear() {
    _frames.clear();
  }

  void dispose() {
    for (Frame frame in frames) {
      frame.image.dispose();
    }

    clear();
  }

  Future<void> exportGif(String outPath,
      {int fps = 30, int videoBitrate = 10000000}) async {
    if (frames.isEmpty) return;

    var maxWidth = 0;
    var maxHeight = 0;
    for (Frame frame in frames) {
      if (frame.image.width > maxWidth) {
        maxWidth = frame.image.width;
      }

      if (frame.image.height > maxHeight) {
        maxHeight = frame.image.height;
      }
    }

    await FlutterQuickVideoEncoder.setup(
      width: maxWidth,
      height: maxHeight,
      videoBitrate: videoBitrate,
      profileLevel: ProfileLevel.any,
      fps: fps,
      filepath: outPath,
      audioBitrate: 0,
      audioChannels: 0,
      sampleRate: 0,
    );

    for (Frame frame in frames) {
      final byteData =
          await frame.image.toByteData(format: ui.ImageByteFormat.rawRgba);
      if (byteData == null) continue;

      final bytes = byteData.buffer.asUint8List();
      await FlutterQuickVideoEncoder.appendVideoFrame(bytes);
    }

    await FlutterQuickVideoEncoder.finish();
  }
}
