import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

const double _partyIconHeight = 58;
const double _partyIconWidth = 58;

class StrokedBorderIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? badgeBannerGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String? partyIcon;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const StrokedBorderIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.badgeBannerGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.partyIcon,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? PosterFlatBadgeRibbon(
            text: badge.description,
            outlineType: badge.badgeBanner,
            backgroundGradient: badgeBannerGradients,
            minBadgeTextFontSize: minBadgeTextFontSize,
            maxBadgeTextFontSize: maxBadgeTextFontSize,
            badgeTextColor: badgeTextColor,
            badgeFontConfig: badgeFontConfig,
          )
        : const SizedBox();
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return AutoSizeText(
      badge.description,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minBadgeTextFontSize,
      maxFontSize: maxBadgeTextFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Color(badgeTextColor),
        fontSize: maxBadgeTextFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
      ),
    );
  }

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        shadows: [
          Shadow(
            color: nameBrightness == Brightness.light
                ? Colors.black
                : Colors.white,
            offset: const Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Color(nameTextColor),
        fontSize: maxNameFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  Widget _getPartyIcon() {
    final partyIcon = this.partyIcon;
    if (partyIcon == null) {
      return const SizedBox();
    }
    return Container(
      height: _partyIconHeight,
      width: _partyIconWidth,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
      ),
      child: PrajaPosterImage(
        imageUrl: partyIcon,
        placeholder: (_, url) => const SizedBox(),
        fadeInDuration: const Duration(milliseconds: 100),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final badge = this.badge;
    final hasBadge =
        badge != null && badge.active && badge.description.isNotEmpty;
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: 110,
          width: double.infinity,
          padding: const EdgeInsets.only(right: 20, left: 20),
          decoration: BoxDecoration(
            gradient: footerGradients.toGradient(),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
              width: 7,
            ),
            borderRadius: BorderRadius.circular(20),
            shape: BoxShape.rectangle,
            boxShadow: const [
              BoxShadow(
                color: Color(0x3F000000),
                blurRadius: 60,
                offset: Offset(0, 30),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Color(0x19000000),
                blurRadius: 8,
                offset: Offset(0, 4),
                spreadRadius: 0,
              )
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                flex: hasBadge ? 65 : 100,
                child: Container(
                  alignment: Alignment.center,
                  child: _buildNameWidget(),
                ),
              ),
              Expanded(
                flex: hasBadge ? 35 : 0,
                child: Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(bottom: hasBadge ? 8 : 0),
                  child: showBadgeRibbon
                      ? _buildRibbonWidget()
                      : _buildBadgeRoleWidget(),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: -_partyIconHeight / 2,
          left: 0,
          child: _getPartyIcon(),
        )
      ],
    );
  }
}
