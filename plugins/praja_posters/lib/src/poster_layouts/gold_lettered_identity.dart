import 'package:flutter/material.dart';
import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:praja_posters/src/constants.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

const letterSpacing = 1.5;

class GoldLetteredIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? badgeBannerGradients;
  final PosterGradient? badgeRibbonBackgroundGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String? partyIcon;
  final bool fullWidth;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const GoldLetteredIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.badgeBannerGradients,
    this.badgeRibbonBackgroundGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.partyIcon,
    this.fullWidth = true,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? Column(children: [
            const SizedBox(height: 8),
            PosterFlatBadgeRibbon(
              text: badge.description,
              outlineType: badge.badgeBanner,
              backgroundGradient: badgeBannerGradients,
              minBadgeTextFontSize: minBadgeTextFontSize,
              maxBadgeTextFontSize: maxBadgeTextFontSize,
              badgeTextColor: badgeTextColor,
              badgeFontConfig: badgeFontConfig,
            )
          ])
        : const SizedBox();
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.only(top: 6),
      child: AutoSizeText(
        badge.description,
        maxLines: 1,
        textAlign: TextAlign.center,
        minFontSize: minBadgeTextFontSize,
        maxFontSize: maxBadgeTextFontSize,
        textScaler: const TextScaler.linear(1.0),
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          color: Color(badgeTextColor),
          fontSize: maxBadgeTextFontSize,
          fontWeight: FontWeight.bold,
          fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
        ),
      ),
    );
  }

  Widget _buildNameWidget() {
    const availableNamePlateWidth = posterWidth -
        posterUserImageWidth -
        spaceBetweenNameAndPartyIcon -
        emptySizedBoxWidth -
        (2 * frameBorderWidth) -
        contentLeftPadding -
        letterSpacing;
    const fullWidthNameWithPartyIcon =
        fullWidthNameSize - (partyIconWidth + spaceBetweenNameAndPartyIcon);
    return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: fullWidth
            ? MainAxisAlignment.center
            : partyIcon == null
                ? MainAxisAlignment.center
                : MainAxisAlignment.end,
        children: [
          if (partyIcon != null) ...[
            const Flexible(
              child: SizedBox(),
            ),
            PrajaPosterImage(
              imageUrl: partyIcon!,
              width: 50,
              height: 50,
              fit: BoxFit.fitWidth,
              alignment: Alignment.topCenter,
            ),
            const SizedBox(
              width: 16,
            ),
          ],
          Container(
            constraints: fullWidth
                ? BoxConstraints(
                    maxWidth: partyIcon != null
                        ? fullWidthNameWithPartyIcon
                        : fullWidthNameSize)
                : BoxConstraints(
                    maxWidth: partyIcon != null
                        ? (availableNamePlateWidth -
                            (partyIconHeight + spaceBetweenNameAndPartyIcon))
                        : availableNamePlateWidth,
                  ),
            child: Stack(
              children: [
                AutoSizeText(
                  name, // Test Name : సత్యనారాయణ
                  maxLines: 1,
                  textAlign: TextAlign.center,
                  minFontSize: minNameFontSize,
                  maxFontSize: maxNameFontSize,
                  overflow: TextOverflow.ellipsis,
                  textScaler: const TextScaler.linear(1.0),
                  style: TextStyle(
                    letterSpacing: letterSpacing,
                    foreground: Paint()
                      ..style = PaintingStyle.stroke
                      ..strokeWidth = 3
                      ..color = Colors.black,
                    fontSize: !fullWidth
                        ? maxNameFontSize
                        : minNameFontSize +
                            (maxNameFontSize - minNameFontSize) / 2,
                    fontWeight: FontWeight.bold,
                    fontFamily:
                        FontUtils.getFontFamily(fontConfig: nameFontConfig),
                  ),
                ),
                ShaderMask(
                  shaderCallback: (bounds) => const LinearGradient(
                    colors: [
                      Color(0xffD5AF4A),
                      Color(0xffE9C156),
                      Color(0xffE7D06E),
                      Color(0xffE9C156),
                      Color(0xffD5B14B),
                    ],
                  ).createShader(bounds),
                  child: AutoSizeText(
                    name,
                    maxLines: 1,
                    textAlign: TextAlign.center,
                    minFontSize: minNameFontSize,
                    maxFontSize: maxNameFontSize,
                    overflow: TextOverflow.ellipsis,
                    textScaler: const TextScaler.linear(1.0),
                    style: TextStyle(
                      letterSpacing: letterSpacing,
                      foreground: Paint()..color = Colors.white,
                      fontSize: !fullWidth
                          ? maxNameFontSize
                          : minNameFontSize +
                              (maxNameFontSize - minNameFontSize) / 2,
                      fontWeight: FontWeight.bold,
                      fontFamily:
                          FontUtils.getFontFamily(fontConfig: nameFontConfig),
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.25),
                          blurRadius: 4,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (partyIcon != null) ...[
            // const Expanded(child: SizedBox()),
            SizedBox(
              width: fullWidth ? 0 : 16,
            ),
          ],
        ]);
  }

  @override
  Widget build(BuildContext context) {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    return SizedBox(
      height: showBadgeStrip ? 120 : 90,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned.fill(
              child: GoldLetteredIdentityBg(
            backgroundGradient: footerGradients,
          )),
          if (showBadgeStrip && showBadgeRibbon) ...[
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: badgeRibbonBackgroundContainerHeight,
                decoration: BoxDecoration(
                  gradient: badgeRibbonBackgroundGradients?.toGradient(),
                ),
                transform: Matrix4.rotationZ(0),
              ),
            ),
          ],
          Positioned.fill(
            bottom: 0,
            child: Padding(
              padding: EdgeInsets.only(
                bottom: showBadgeStrip ? 10 : 0,
                left: 12,
                right: 12,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: showBadgeStrip
                    ? MainAxisAlignment.end
                    : MainAxisAlignment.center,
                children: [
                  Expanded(
                    flex: showBadgeStrip ? 60 : 100,
                    child: Row(
                      children: [
                        Expanded(child: _buildNameWidget()),
                        SizedBox(
                          width: fullWidth ? 0 : posterUserImageWidth,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: showBadgeStrip ? 28 : 0,
                    child: Padding(
                      padding: EdgeInsets.only(left: partyIcon != null ? 4 : 0),
                      child: showBadgeRibbon
                          ? _buildRibbonWidget()
                          : _buildBadgeRoleWidget(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class GoldLetteredIdentityBg extends StatelessWidget {
  final PosterGradient backgroundGradient;
  const GoldLetteredIdentityBg({
    super.key,
    required this.backgroundGradient,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 109,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: backgroundGradient.toGradient(),
      ),
      child: Stack(
        children: [
          Positioned(
            top: 4,
            left: 0,
            right: 0,
            height: 04,
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFFD5AF4A),
                    Color(0xFFF2E983),
                    Color(0xFFF2E983),
                    Color(0xFFD5B14B),
                  ],
                  stops: [0.0, 0.33, 0.67, 1.0],
                  begin: Alignment.topLeft,
                  end: Alignment.topRight,
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 4,
            left: 0,
            right: 0,
            height: 04,
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFFD5AF4A),
                    Color(0xFFF2E983),
                    Color(0xFFF2E983),
                    Color(0xFFD5B14B),
                  ],
                  stops: [0.0, 0.33, 0.67, 1.0],
                  begin: Alignment.bottomLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
