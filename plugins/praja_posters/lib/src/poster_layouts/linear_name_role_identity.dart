import 'package:flutter/material.dart';
import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/ui_widgets/trapezoid_shaped_bg_texture.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

const double _partyIconWidth = 62;
const double _partyIconHeight = 62;
const double _trapezoidHeight = 20;

/// This Supports [PosterIdentityType.linearNameAndRoleIdentity] [PosterIdentityType.trapezoidalIdentity] [PosterIdentityType.topTrapezoidalIdentity] [PosterIdentityType.bottomTrapezoidalIdentity]
class LinearNameRoleIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String? partyIcon;
  final int? primaryHighlightColor;
  final int? secondaryHighlightColor;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;

  const LinearNameRoleIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.partyIcon,
    this.primaryHighlightColor,
    this.secondaryHighlightColor,
    this.nameFontConfig,
    this.badgeFontConfig,
  });

  Widget _partyIconWidget() {
    final partyIcon = this.partyIcon;
    if (partyIcon == null) {
      return const SizedBox();
    }
    return PrajaPosterImage(
      imageUrl: partyIcon,
      width: _partyIconWidth,
      height: _partyIconHeight,
      fadeInDuration: const Duration(milliseconds: 200),
      placeholder: (context, url) => const SizedBox(),
    );
  }

  Widget _nameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        shadows: [
          Shadow(
            color: nameBrightness == Brightness.light
                ? Colors.black
                : Colors.white,
            offset: const Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Color(nameTextColor),
        fontSize: maxNameFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  Widget _badgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return AutoSizeText(
      ", ${badge.description}",
      maxLines: 1,
      textAlign: TextAlign.left,
      minFontSize: minBadgeTextFontSize,
      maxFontSize: maxBadgeTextFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Color(badgeTextColor),
        fontSize: minNameFontSize + (maxNameFontSize - minNameFontSize) / 2,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
      ),
    );
  }

  Widget _rightTrapezoidShapedBg() {
    final primaryHighlightColor = this.primaryHighlightColor;
    if (primaryHighlightColor == null) {
      return const SizedBox();
    }
    return SizedBox(
      height: _trapezoidHeight,
      width: 250,
      child: TrapezoidShapedBG(
        clipperAlignment: TrapezoidShapedBgEnum.right,
        color: Color(primaryHighlightColor),
      ),
    );
  }

  Widget _leftTrapezoidShapedBg() {
    final secondaryHighlightColor = this.secondaryHighlightColor;
    if (secondaryHighlightColor == null) {
      return const SizedBox();
    }
    return SizedBox(
      height: _trapezoidHeight,
      width: 250,
      child: TrapezoidShapedBG(
        clipperAlignment: TrapezoidShapedBgEnum.left,
        color: Color(secondaryHighlightColor),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: 94,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 14),
          decoration: BoxDecoration(
            gradient: footerGradients.toGradient(),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                flex: partyIcon != null ? 10 : 0,
                child: _partyIconWidget(),
              ),
              if (partyIcon != null) const SizedBox(width: 10),
              Expanded(
                flex: partyIcon == null ? 100 : 90,
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Row(
                    children: [
                      _nameWidget(),
                      _badgeRoleWidget(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: -_trapezoidHeight,
          left: 0,
          child: _rightTrapezoidShapedBg(),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: _leftTrapezoidShapedBg(),
        ),
      ],
    );
  }
}
