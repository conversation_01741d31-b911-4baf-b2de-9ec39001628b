import 'package:flutter/material.dart';
import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/ui_widgets/profile_type_widgets/polygonal_profile.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

/// Supports [PosterIdentityType.polygonalProfileIdentity],[PosterIdentityType.polygonalProfileIdentityWithoutBadgeStrip]
class PolygonalProfileIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? upperFooterGradients;
  final PosterGradient? badgeBannerGradients;
  final PosterGradient? borderGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String imageUrl;
  final bool isGoldenFrame;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const PolygonalProfileIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.upperFooterGradients,
    this.badgeBannerGradients,
    this.borderGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    required this.imageUrl,
    required this.isGoldenFrame,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? PosterFlatBadgeRibbon(
            text: badge.description,
            outlineType: badge.badgeBanner,
            backgroundGradient: badgeBannerGradients,
            minBadgeTextFontSize: minBadgeTextFontSize,
            maxBadgeTextFontSize: maxBadgeTextFontSize,
            badgeTextColor: badgeTextColor,
            badgeFontConfig: badgeFontConfig,
          )
        : const SizedBox();
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return AutoSizeText(
      badge.description,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minBadgeTextFontSize,
      maxFontSize: maxBadgeTextFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Color(badgeTextColor),
        fontSize: maxBadgeTextFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
      ),
    );
  }

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        shadows: [
          Shadow(
            color: nameBrightness == Brightness.light
                ? Colors.black
                : Colors.white,
            offset: const Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Color(nameTextColor),
        fontSize: maxNameFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  Widget _getBody() {
    final upperFooterGradients = this.upperFooterGradients;
    final badge = this.badge;
    final hasBadge =
        badge != null && badge.active && badge.description.isNotEmpty;
    if (upperFooterGradients == null) {
      return const SizedBox();
    }
    return Container(
      height: 108,
      decoration: BoxDecoration(
          gradient: footerGradients.toGradient(),
          borderRadius: const BorderRadius.only(
            bottomRight: Radius.circular(110),
          )),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Row(
            children: [
              Expanded(
                flex: hasBadge ? 70 : 100,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: hasBadge ? 55 : 100,
                      child: Container(
                          width: double.infinity,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              gradient: hasBadge
                                  ? upperFooterGradients.toGradient()
                                  : footerGradients.toGradient(),
                              borderRadius: BorderRadius.only(
                                bottomRight: !hasBadge
                                    ? const Radius.circular(110)
                                    : Radius.zero,
                              )),
                          child: Padding(
                            padding: EdgeInsets.only(
                                left: 12.0, right: hasBadge ? 28.0 : 218),
                            child: _buildNameWidget(),
                          )),
                    ),
                    if (hasBadge) ...[
                      Expanded(
                        flex: 45,
                        child: Padding(
                          padding: const EdgeInsets.only(
                            top: 10,
                            bottom: 8.0,
                            left: 12.0,
                            right: 12.0,
                          ),
                          child: showBadgeRibbon
                              ? _buildRibbonWidget()
                              : _buildBadgeRoleWidget(),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Expanded(
                flex: hasBadge ? 30 : 0,
                child: const SizedBox(),
              )
            ],
          ),
          Positioned(
            top: -110, //50% of height
            right: -10,
            child: PolygonalProfileWidget(
              imageUrl: imageUrl,
              polygonHeight: 220,
              polygonWidth: 220,
              footerGradients: footerGradients,
              badgeBannerGradients: badgeBannerGradients,
              upperFooterGradients: upperFooterGradients,
              borderGradients: borderGradients,
              isGoldenFrame: isGoldenFrame,
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _getBody();
  }
}
