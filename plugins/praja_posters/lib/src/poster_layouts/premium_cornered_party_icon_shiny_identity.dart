import 'package:flutter/material.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

const double _partyIconHeight = 60;
const double _partyIconWidth = 60;

class PremiumCorneredPartyIconShinyIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? badgeBannerGradients;
  final PosterGradient? borderGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String? partyIcon;
  final int? primaryHighlightColor;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;
  final bool isFullWidthIdentity;

  const PremiumCorneredPartyIconShinyIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.badgeBannerGradients,
    this.borderGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.partyIcon,
    this.primaryHighlightColor,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
    this.isFullWidthIdentity = true,
  });

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? Padding(
            padding: const EdgeInsets.only(top: 2.0),
            child: PosterFlatBadgeRibbon(
              text: badge.description,
              outlineType: badge.badgeBanner,
              backgroundGradient: badgeBannerGradients,
              minBadgeTextFontSize: minBadgeTextFontSize,
              maxBadgeTextFontSize: maxBadgeTextFontSize,
              badgeTextColor: badgeTextColor,
              badgeFontConfig: badgeFontConfig,
            ),
          )
        : const SizedBox();
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.only(top: 2.0),
      child: AutoSizeText(
        badge.description,
        maxLines: 1,
        textAlign: TextAlign.center,
        minFontSize: minBadgeTextFontSize,
        maxFontSize: maxBadgeTextFontSize,
        textScaler: const TextScaler.linear(1.0),
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          color: Color(badgeTextColor),
          fontSize: maxBadgeTextFontSize,
          fontWeight: FontWeight.bold,
          fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
        ),
      ),
    );
  }

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        shadows: [
          Shadow(
            color: nameBrightness == Brightness.light
                ? Colors.black
                : Colors.white,
            offset: const Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Color(nameTextColor),
        fontSize: maxNameFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  Widget _getPartyIcon() {
    final partyIcon = this.partyIcon;
    final primaryHighlightColor = this.primaryHighlightColor;
    if (partyIcon == null) {
      return const SizedBox();
    }
    return Container(
      height: 80,
      width: 80,
      margin: const EdgeInsets.only(right: 17),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: primaryHighlightColor == null
            ? null
            : [
                BoxShadow(
                  color: Color(primaryHighlightColor).withOpacity(0.2),
                  spreadRadius: 20,
                  blurRadius: 10,
                  offset: const Offset(0, 0), // changes position of shadow
                ),
                BoxShadow(
                  color: Color(primaryHighlightColor).withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 12,
                  offset: const Offset(0, 0), // changes position of shadow
                ),
              ],
      ),
      child: Container(
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2)),
        child: PrajaPosterImage(
          imageUrl: partyIcon,
          fit: BoxFit.cover,
          alignment: Alignment.topCenter,
          height: _partyIconHeight,
          width: _partyIconWidth,
          placeholder: (_, url) => const SizedBox(),
          fadeInDuration: const Duration(milliseconds: 0),
        ),
      ),
    );
  }

  BoxBorder? _getBorder() {
    final borderGradients = this.borderGradients;
    if (borderGradients == null) {
      return null;
    }
    return GradientBoxBorder(
      gradient: borderGradients.toGradient(),
      width: 5,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      padding: const EdgeInsets.only(left: 20, right: 43),
      decoration: BoxDecoration(
        gradient: footerGradients.toGradient(),
        border: _getBorder(),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(80),
          bottomRight: Radius.circular(80),
        ),
      ),
      child: Row(
        children: [
          _getPartyIcon(),
          Expanded(
            flex: isFullWidthIdentity ? 100 : 60,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildNameWidget(),
                showBadgeRibbon
                    ? _buildRibbonWidget()
                    : _buildBadgeRoleWidget(),
              ],
            ),
          ),
          Expanded(
            flex: isFullWidthIdentity ? 0 : 40,
            child: const SizedBox(),
          )
        ],
      ),
    );
  }
}
