import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

class SemiCircularIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? badgeBannerGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const SemiCircularIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.badgeBannerGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? PosterFlatBadgeRibbon(
            text: badge.description,
            outlineType: badge.badgeBanner,
            backgroundGradient: badgeBannerGradients,
            minBadgeTextFontSize: minBadgeTextFontSize,
            maxBadgeTextFontSize: maxBadgeTextFontSize,
            badgeTextColor: badgeTextColor,
            badgeFontConfig: badgeFontConfig,
          )
        : const SizedBox();
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return AutoSizeText(
      badge.description,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minBadgeTextFontSize,
      maxFontSize: maxBadgeTextFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Color(badgeTextColor),
        fontSize: maxBadgeTextFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
      ),
    );
  }

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        shadows: [
          Shadow(
            color: nameBrightness == Brightness.light
                ? Colors.black
                : Colors.white,
            offset: const Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Color(nameTextColor),
        fontSize: maxNameFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final badge = this.badge;
    final hasBadge =
        badge != null && badge.active && badge.description.isNotEmpty;
    return SizedBox(
      height: 148,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned.fill(
            child: _SemiCircularIdentityBG(gradient: footerGradients),
          ),
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.only(top: 44, left: 40, right: 40),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    flex: hasBadge ? 65 : 100,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const Expanded(flex: 10, child: SizedBox()),
                        Expanded(
                          flex: 80,
                          child: Container(
                            alignment: Alignment.center,
                            child: _buildNameWidget(),
                          ),
                        ),
                        const Expanded(flex: 10, child: SizedBox()),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: hasBadge ? 35 : 0,
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.only(bottom: 8),
                      child: showBadgeRibbon
                          ? _buildRibbonWidget()
                          : _buildBadgeRoleWidget(),
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}

class _SemiCircularIdentityBG extends StatelessWidget {
  final PosterGradient gradient;
  const _SemiCircularIdentityBG({required this.gradient});

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: _CustomSemiCircularPathClipper(),
      child: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: gradient.toGradient(),
        ),
      ),
    );
  }
}

class _CustomSemiCircularPathClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.moveTo(0, size.height / 2);
    path.quadraticBezierTo(size.width / 2, 0, size.width, size.height / 2);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
