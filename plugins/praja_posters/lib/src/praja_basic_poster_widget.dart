import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:praja_posters/praja_posters.dart';
import 'package:praja_posters/src/constants.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';

class PrajaBasicPosterWidget extends StatefulWidget {
  final PosterCreative creative;
  final PosterLayout layout;
  final PosterBadge? badge;
  final String name;
  final Function(String url, String error)? onPosterPhotoFail;
  final Widget userProfileWidget;
  final Widget prajaAppLogoWidget;
  final Widget animatedUploadMessageWidget;

  ///Creative Dominant Color
  final Color? creativeDominantColor;

  const PrajaBasicPosterWidget({
    Key? key,
    required this.creative,
    required this.layout,
    this.creativeDominantColor,
    this.badge,
    required this.name,
    this.onPosterPhotoFail,
    required this.userProfileWidget,
    required this.prajaAppLogoWidget,
    this.animatedUploadMessageWidget = const SizedBox(),
  }) : super(key: key);

  @override
  State<PrajaBasicPosterWidget> createState() => _PrajaBasicPosterWidget();
}

class _PrajaBasicPosterWidget extends State<PrajaBasicPosterWidget> {
  double footerHeight = 102;
  double avatarRadius = 84.37;
  bool showImageUploadMsg = false;
  bool isPosterPhotoFailed = false;
  Map<String, bool> leaderPhotoFails = {};

  late PosterIdentityType? identityType;

  static const List<PosterIdentityType> _bottomBorderIdentities = [
    PosterIdentityType.basicTransparentIdentity,
    PosterIdentityType.basicNoProfilePicIdentity,
    PosterIdentityType.basicFullTransparentIdentity,
  ];

  posterPhotoFailCheck(String url) {
    return leaderPhotoFails.containsKey(url) && leaderPhotoFails[url]!;
  }

  @override
  void initState() {
    super.initState();
    identityType = widget.layout.identity?.type;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          showImageUploadMsg = true;
        });
      }
    });
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        setState(() {
          showImageUploadMsg = false;
        });
      }
    });
  }

  getPosterPhoto() {
    return CachedNetworkImage(
        imageUrl: widget.creative.url,
        imageBuilder: (context, imageProvider) {
          return Container(
              decoration: BoxDecoration(
            image: DecorationImage(
              image: imageProvider,
              fit: BoxFit.cover,
            ),
          ));
        },
        progressIndicatorBuilder: (context, url, downloadProgress) => Center(
            child: CircularProgressIndicator(
                value: downloadProgress.progress, color: Colors.black)),
        errorWidget: (context, url, error) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              logPhotoLoadFail(url,
                  isLeaderPhoto: false, error: error.toString());
              isPosterPhotoFailed = true;
            });
          });
          return const Icon(Icons.error);
        });
  }

  Widget getUserNameWidget() {
    Color nameTextColor = Color(widget.layout.nameTextColor);

    if (_bottomBorderIdentities.contains(identityType) &&
        widget.layout.autoAdaptTextColor) {
      final dominantColor = widget.creativeDominantColor;
      if (dominantColor != null) {
        final Brightness brightness =
            ThemeData.estimateBrightnessForColor(dominantColor);
        Color textColor =
            brightness == Brightness.light ? Colors.black : Colors.white;
        nameTextColor = textColor;
      }
    }

    return AutoSizeText(
      widget.name,
      minFontSize: 22,
      maxFontSize: 64,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      textScaler: const TextScaler.linear(1.0),
      textAlign: TextAlign.center,
      style: TextStyle(
          color: nameTextColor, fontSize: 36, fontWeight: FontWeight.w600),
    );
  }

  Widget getUserBadgeStrip() {
    final userBadge = widget.badge;
    const double minBadgeTextFontSize = 12;
    const double maxBadgeTextFontSize = 18;
    final badgeBannerGradient = widget.layout.gradients?.badgeBannerGradients;
    if (userBadge == null || badgeBannerGradient == null) {
      return const SizedBox();
    }
    final bool isUserHaveBadgeBanner =
        userBadge.badgeBanner != PosterBadgeBanner.none;
    if (userBadge.active && isUserHaveBadgeBanner) {
      return Padding(
        padding: const EdgeInsets.only(top: 5, bottom: 8, left: 20, right: 20),
        child: PosterFlatBadgeRibbon(
          text: userBadge.description,
          outlineType: userBadge.badgeBanner,
          backgroundGradient: badgeBannerGradient,
          badgeTextColor: widget.layout.badgeTextColor,
          minBadgeTextFontSize: minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
        ),
      );
    }
    return const SizedBox();
  }

  Widget getAppIcon() {
    final layout = widget.layout;
    if (!layout.showPrajaLogo) {
      return const SizedBox();
    }
    return Positioned(
      left: 0,
      top: 0,
      child: SizedBox(
        width: 163,
        height: 163,
        child: widget.prajaAppLogoWidget,
      ),
    );
  }

  Widget animatedUploadMessageWidget() {
    const double rightSidePositionOfUserAvatar = 12.0;
    return Positioned(
      top: -avatarRadius / 2,
      right: avatarRadius / 2 -
          rightSidePositionOfUserAvatar, // Centering the message
      child: widget.animatedUploadMessageWidget,
    );
  }

  logPhotoLoadFail(String url,
      {bool isLeaderPhoto = true, String error = "UN_KNOWN"}) {
    if (!posterPhotoFailCheck(url) || !isPosterPhotoFailed) {
      widget.onPosterPhotoFail?.call(url, error);
      if (mounted) {
        setState(() {
          leaderPhotoFails[url] = true;
        });
      }
    }
  }

  getUserAvatar() {
    const List<PosterIdentityType> noAvatarIdentities = [
      PosterIdentityType.basicNoProfilePicIdentity,
      PosterIdentityType.basicFullTransparentIdentity,
    ];
    if (noAvatarIdentities.contains(identityType)) {
      return const SizedBox();
    }
    return Positioned(
      bottom: _bottomBorderIdentities.contains(identityType)
          ? widget.layout.enableOuterFrame
              ? 26
              : 10
          : 113,
      right: 12,
      child: Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: const EdgeInsets.only(right: 15),
          child: InkWell(
            onTap: () {
              if (mounted) {
                setState(() {
                  showImageUploadMsg = false;
                });
              }
            },
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                CircleAvatar(
                  backgroundColor: const Color(0xffffffff).withOpacity(0.2),
                  radius: avatarRadius,
                ),
                widget.userProfileWidget,
                animatedUploadMessageWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  static const LinearGradient _defaultBackgroundGradient = LinearGradient(
    begin: Alignment(0.0, 0.0),
    end: Alignment(0.0, 1.0),
    colors: [Colors.blue, Colors.blue],
    stops: null,
  );

  getBackGroundGradients() {
    final backgroundGradients = widget.layout.gradients?.backgroundGradients;
    return backgroundGradients != null
        ? backgroundGradients.toGradient()
        : _defaultBackgroundGradient;
  }

  getFooterGradients() {
    final footerGradients = widget.layout.gradients?.footerGradients;
    return footerGradients != null
        ? footerGradients.toGradient()
        : _defaultBackgroundGradient;
  }

  Widget _userBadgeRoleWidget() {
    final badge = widget.badge;
    Color badgeTextColor = Color(widget.layout.badgeTextColor);

    if (_bottomBorderIdentities.contains(identityType) &&
        widget.layout.autoAdaptTextColor) {
      final dominantColor = widget.creativeDominantColor;
      if (dominantColor != null) {
        final Brightness brightness =
            ThemeData.estimateBrightnessForColor(dominantColor);
        Color textColor =
            brightness == Brightness.light ? Colors.black : Colors.white;
        badgeTextColor = textColor;
      }
    }

    if (badge == null) {
      return const SizedBox();
    }

    return AutoSizeText(
      badge.description,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: 12,
      maxFontSize: 22,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: badgeTextColor,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _getUserBadge() {
    final identity = widget.layout.identity;
    final showBadgeRibbon = identity?.showBadgeRibbon ?? false;
    if (_bottomBorderIdentities.contains(identityType) || !showBadgeRibbon) {
      return _userBadgeRoleWidget();
    } else {
      return getUserBadgeStrip();
    }
  }

  getFooter() {
    if (identityType == PosterIdentityType.basicFullTransparentIdentity) {
      return const SizedBox();
    }
    return Positioned(
      bottom: _bottomBorderIdentities.contains(identityType) ? 16 : 0,
      child: Container(
        width: posterWidth,
        height: footerHeight,
        padding: _bottomBorderIdentities.contains(identityType)
            ? const EdgeInsets.only(left: 18, right: 18)
            : EdgeInsets.zero,
        decoration: BoxDecoration(gradient: getFooterGradients()),
        child: Row(
          children: [
            Expanded(
              flex:
                  (identityType == PosterIdentityType.basicTransparentIdentity)
                      ? 70
                      : 100,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  getUserNameWidget(),
                  const SizedBox(
                    height: 1,
                  ),
                  _getUserBadge(),
                ],
              ),
            ),
            Expanded(
                flex: (identityType ==
                        PosterIdentityType.basicTransparentIdentity)
                    ? 30
                    : 0,
                child: const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  List<Widget> getHeader1Photos() {
    List<Widget> headerPhotos = [];
    for (int i = 0; i < widget.layout.header1Photos.length; i++) {
      final double positionX = widget.layout.header1Photos[i].positionX;
      final double positionY = widget.layout.header1Photos[i].positionY;
      headerPhotos.add(
        Positioned(
            left: positionX,
            top: positionY,
            child: HeaderPhotoWidget.fromHeaderPhoto(
              widget.layout.header1Photos[i],
              backgroundType: widget.creative.h1BackgroundType,
            )),
      );
    }
    return headerPhotos;
  }

  List<Widget> getHeader2Photos() {
    List<Widget> headerPhotos = [];
    for (int i = 0; i < widget.layout.header2Photos.length; i++) {
      final double positionX = widget.layout.header2Photos[i].positionX;
      final double positionY = widget.layout.header2Photos[i].positionY;
      headerPhotos.add(
        Positioned(
            left: positionX,
            top: positionY,
            child: HeaderPhotoWidget.fromHeaderPhoto(
              widget.layout.header2Photos[i],
              backgroundType: widget.creative.h2BackgroundType,
              backgroundGradients:
                  widget.layout.gradients?.h2BackgroundGradients,
            )),
      );
    }
    return headerPhotos;
  }

  @override
  Widget build(BuildContext context_) {
    return SizedBox(
      width: posterWidth,
      height: posterHeight,
      child: InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          if (mounted) {
            setState(() {
              showImageUploadMsg = false;
            });
          }
        },
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned.fill(
              child: Container(
                color: Colors.transparent,
                child: Container(
                    padding: widget.layout.enableOuterFrame
                        ? EdgeInsets.only(
                            top: 16,
                            left: 16,
                            right: 16,
                            bottom:
                                _bottomBorderIdentities.contains(identityType)
                                    ? 16
                                    : 0,
                          )
                        : EdgeInsets.zero,
                    decoration: BoxDecoration(
                      gradient: getBackGroundGradients(),
                    ),
                    child: getPosterPhoto()),
              ),
            ),
            getFooter(),
            getUserAvatar(),
            ...getHeader1Photos(),
            ...getHeader2Photos(),
            getAppIcon(),
          ],
        ),
      ),
    );
  }
}
