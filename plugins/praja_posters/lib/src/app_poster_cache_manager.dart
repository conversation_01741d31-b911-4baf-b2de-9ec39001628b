import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

//An instance of AppCacheManager has stale duration of 30 days
class AppPosterCacheManager extends CacheManager {
  static const key = "postersCachedImageDataV3";

  static AppPosterCacheManager? _instance;
  static const Duration _duration = Duration(days: 30);
  static AppPosterCacheManager get instance {
    _instance ??= AppPosterCacheManager._();
    return _instance!;
  }

  AppPosterCacheManager._()
      : super(Config(key, stalePeriod: _duration, maxNrOfCacheObjects: 100));

  Future<String> getFilePath() async {
    var directory = await getTemporaryDirectory();
    return p.join(directory.path, key);
  }
}
