import 'package:flutter/material.dart';
import 'package:praja_posters/praja_posters.dart';
import 'package:praja_posters/src/constants.dart';
import 'package:praja_posters/src/poster_layouts/flat_user_badge_circle.dart';
import 'package:praja_posters/src/poster_layouts/gold_frame.dart';
import 'package:praja_posters/src/ui_widgets/party_tag_widget.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';

class PrajaPremiumPosterWidget extends StatelessWidget {
  final PosterLayout layout;
  final PosterCreative creative;
  final VoidCallback? onCreativeLoaded;
  final bool showCameraIcon;
  final VoidCallback? onCameraIconClicked;
  final VoidCallback? onUserPhotoTapped;

  const PrajaPremiumPosterWidget({
    super.key,
    required this.layout,
    required this.creative,
    this.onCreativeLoaded,
    this.showCameraIcon = false,
    this.onCameraIconClicked,
    this.onUserPhotoTapped,
  });

  // Images in Poster
  Widget getImage(PosterUser user) {
    return GestureDetector(
      onTap: onUserPhotoTapped,
      child: Stack(
        children: [
          ClipRRect(
            borderRadius:
                _premiumCorneredIdentities.contains(layout.identity?.type)
                    ? const BorderRadius.only(
                        bottomRight: Radius.circular(60),
                      )
                    : BorderRadius.zero,
            child: ShaderMask(
              shaderCallback: (bounds) {
                return const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.white, Colors.transparent],
                  stops: [0.92, 1.0],
                ).createShader(bounds);
              },
              blendMode: BlendMode.dstIn,
              child: SizedBox(
                  width: posterUserImageWidth,
                  height: posterUserImageHeight,
                  child: PrajaPosterImage(
                    fit: BoxFit.contain,
                    alignment: Alignment.bottomRight,
                    imageUrl: user.photoUrl,
                  )),
            ),
          ),
          Positioned(
            left: posterUserImageWidth / 3,
            top: posterUserImageHeight / 2,
            child: AnimatedOpacity(
                duration: const Duration(milliseconds: 500),
                opacity: showCameraIcon ? 1 : 0,
                child: getCameraIcon()),
          ),
        ],
      ),
    );
  }

  Widget getCameraIcon() {
    if (!showCameraIcon) {
      return const SizedBox();
    }
    return InkWell(
      onTap: onCameraIconClicked,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF000000).withOpacity(0.5),
          borderRadius: BorderRadius.circular(50),
        ),
        padding: const EdgeInsets.all(12),
        child: const Icon(
          Icons.camera_alt,
          color: Colors.white,
          size: 48,
        ),
      ),
    );
  }

  Widget getCreativeImage() {
    return ClipRRect(
      borderRadius: getImageBorderRadius(),
      child: PrajaPosterImage(
        imageUrl: creative.url,
        fit: BoxFit.cover,
        imageBuilder: (context, imageProvider) {
          onCreativeLoaded?.call();
          return Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
              ),
            ),
          );
        },
        fadeInDuration: const Duration(milliseconds: 200),
        placeholder: (context, url) => const Center(
          child: CircularProgressIndicator(),
        ),
        errorWidget: (context, url, error) => const Icon(Icons.error),
      ),
    );
  }

  List<Widget> getHeader1Photos() {
    List<Widget> headerPhotos = [];
    for (int i = 0; i < layout.header1Photos.length; i++) {
      final double positionX = layout.header1Photos[i].positionX;
      final double positionY = layout.header1Photos[i].positionY;
      headerPhotos.add(
        Positioned(
            left: positionX,
            top: positionY,
            child: HeaderPhotoWidget.fromHeaderPhoto(
              layout.header1Photos[i],
              backgroundType: creative.h1BackgroundType,
            )),
      );
    }
    return headerPhotos;
  }

  List<Widget> getHeader2Photos() {
    List<Widget> headerPhotos = [];
    for (int i = 0; i < layout.header2Photos.length; i++) {
      final double positionX = layout.header2Photos[i].positionX;
      final double positionY = layout.header2Photos[i].positionY;
      headerPhotos.add(
        Positioned(
            left: positionX,
            top: positionY,
            child: HeaderPhotoWidget.fromHeaderPhoto(
              layout.header2Photos[i],
              backgroundType: creative.h2BackgroundType,
              backgroundGradients: layout.gradients?.h2BackgroundGradients,
            )),
      );
    }
    return headerPhotos;
  }

  Widget getPartyTag() {
    final PosterPartyIcon? partyIcon = layout.partyIcon;
    if (partyIcon == null) {
      return const SizedBox();
    }
    return Positioned(
      top: partyIcon.position == PosterPartyIconAlignment.left ? 182 : 0,
      left: partyIcon.position == PosterPartyIconAlignment.left
          ? 0
          : layout.goldenFrame
              ? 24
              : 14,
      child: PartyTagWidget(
        iconUrl: partyIcon.url,
        alignment: partyIcon.position,
        gradients: partyIcon.gradients,
      ),
    );
  }

  // Footer Part (aka Identity) of Poster
  Widget getIdentity() {
    final identity = layout.identity;
    final gradients = layout.gradients;
    if (identity == null || gradients == null) {
      return const SizedBox();
    }

    List<PosterIdentityType> noFrameIdentityTypes = [
      PosterIdentityType.shinyIdentity,
      PosterIdentityType.multiColorIdentity,
      PosterIdentityType.semiCircularIdentity,
      PosterIdentityType.premiumCorneredPartyIconShinyIdentity,
      PosterIdentityType.premiumCorneredPartyIconGradientIdentity,
      PosterIdentityType.partyTagIdentity,
      PosterIdentityType.polygonalProfileIdentity,
    ];

    double getLeftAndRightPositionForIdentity() {
      if (identity.type == PosterIdentityType.glassyUser &&
          layout.enableOuterFrame) {
        return 10;
      } else if (identity.type == PosterIdentityType.glassyUser &&
          !layout.enableOuterFrame) {
        return 0;
      } else if (!layout.enableOuterFrame ||
          noFrameIdentityTypes.contains(identity.type)) {
        return 0;
      } else {
        return 16;
      }
    }

    final List<PosterIdentityType> noBottomFrameIdentities = [
      PosterIdentityType.premiumCorneredPartyIconShinyIdentity,
      PosterIdentityType.premiumCorneredPartyIconGradientIdentity,
    ];

    double getBottomPositionForIdentity() {
      if (layout.enableOuterFrame &&
          noBottomFrameIdentities.contains(identity.type)) {
        return 0;
      } else if (identity.type == PosterIdentityType.glassyUser) {
        return -26;
      } else if (layout.shadowColor != null && layout.enableOuterFrame) {
        return 16;
      } else if (layout.shadowColor != null && !layout.enableOuterFrame) {
        return 0;
      } else {
        return 0;
      }
    }

    return Positioned(
      bottom: getBottomPositionForIdentity(),
      left: getLeftAndRightPositionForIdentity(),
      right: getLeftAndRightPositionForIdentity(),
      child: PosterIdentityWidget(
        identity: identity,
        gradients: gradients,
        nameTextColor: layout.nameTextColor,
        badgeTextColor: layout.badgeTextColor,
        isGoldenFrame: layout.goldenFrame,
        nameFontConfig: layout.posterFontsConfig?.name,
        badgeFontConfig: layout.posterFontsConfig?.badge,
      ),
    );
  }

  static const List<PosterIdentityType> _noUserImageIdentities = [
    PosterIdentityType.polygonalProfileIdentity,
  ];

  static const List<PosterIdentityType> _userOnlyBackSideIdentityTypes = [
    PosterIdentityType.plainIdentityWithPartyIcon,
    PosterIdentityType.partyTagIdentity,
    PosterIdentityType.semiCircularIdentity,
    PosterIdentityType.shinyIdentity,
    PosterIdentityType.shinyIdentityWithLowShadow,
    PosterIdentityType.strokedBorderIdentity,
    PosterIdentityType.partySloganIdentityWithPartyIcon,
    PosterIdentityType.partySloganIdentity,
    PosterIdentityType.linearNameAndRoleIdentity,
    PosterIdentityType.trapezoidalIdentity,
    PosterIdentityType.topTrapezoidalIdentity,
    PosterIdentityType.bottomTrapezoidalIdentity,
  ];

  static const List<PosterIdentityType> _premiumCorneredIdentities = [
    PosterIdentityType.premiumCorneredPartyIconGradientIdentity,
    PosterIdentityType.premiumCorneredPartyIconShinyIdentity,
  ];

  Widget getPosterUserAvatarBackSideIdentity() {
    final identity = layout.identity;
    final List<PosterIdentityType> noBackIdentities = [
      PosterIdentityType.flatUserBadgeCircle,
    ];

    if (identity == null ||
        noBackIdentities.contains(identity.type) ||
        _noUserImageIdentities.contains(identity.type)) {
      return const SizedBox();
    }

    double getRightPosition() {
      return layout.gradients?.innerBackgroundGradients == null
          ? layout.enableOuterFrame
              ? 16
              : 0
          : layout.enableOuterFrame
              ? 26
              : 10;
    }

    final List<PosterIdentityType> extraBottomPositionIdentities = [
      PosterIdentityType.shinyIdentityWithLowShadow,
      PosterIdentityType.shinyIdentity,
    ];

    double getBottomPosition() {
      if (identity.type == PosterIdentityType.glassyUser) {
        return identity.user.badge != null ? 62 : 42;
      } else if (extraBottomPositionIdentities.contains(identity.type)) {
        return 82;
      } else {
        return 70;
      }
    }

    if (identity.isUserPositionBack ||
        _userOnlyBackSideIdentityTypes.contains(identity.type)) {
      return Positioned(
          right: getRightPosition(),
          bottom: getBottomPosition(),
          child: getImage(identity.user));
    } else {
      return const SizedBox();
    }
  }

  Widget getPosterUserAvatarFrontSideIdentity() {
    final identity = layout.identity;
    if (identity == null ||
        _userOnlyBackSideIdentityTypes.contains(identity.type) ||
        (identity.type != PosterIdentityType.flatUserBadgeCircle &&
            identity.isUserPositionBack) ||
        _noUserImageIdentities.contains(identity.type)) {
      return const SizedBox();
    }

    final List<PosterIdentityType> bottom0IdentityTypes = [
      PosterIdentityType.plainIdentity,
    ];

    double getRightPosition() {
      if (identity.type == PosterIdentityType.flatUserBadgeCircle) {
        return layout.gradients?.innerBackgroundGradients == null
            ? layout.enableOuterFrame
                ? 20
                : 4
            : layout.enableOuterFrame
                ? 30
                : 14;
      }
      return layout.gradients?.innerBackgroundGradients == null
          ? layout.enableOuterFrame
              ? 16
              : _premiumCorneredIdentities.contains(layout.identity?.type)
                  ? 5 //border stroke
                  : 0
          : layout.enableOuterFrame
              ? 26
              : 10;
    }

    double getFlatUserBadgeCircleBottomPosition() {
      final badge = identity.user.badge;
      final userHasBadgeRibbon =
          badge != null && badge.active && badge.description.isNotEmpty;
      if (identity.isUserPositionBack && userHasBadgeRibbon) {
        return (layout.shadowColor != null && layout.enableOuterFrame)
            ? 130
            : 114;
      } else if (userHasBadgeRibbon) {
        return (layout.shadowColor != null && layout.enableOuterFrame)
            ? 82
            : 66;
      } else if (!userHasBadgeRibbon && identity.isUserPositionBack) {
        return (layout.shadowColor != null && layout.enableOuterFrame)
            ? 110
            : 94;
      } else {
        return 36;
      }
    }

    double getBottomPosition() {
      final badge = identity.user.badge;
      final userHasBadgeRibbon =
          badge != null && badge.active && badge.description.isNotEmpty;
      if (bottom0IdentityTypes.contains(identity.type)) {
        return layout.shadowColor != null ? 16 : 0;
      } else if (_premiumCorneredIdentities.contains(identity.type)) {
        return 10;
      } else if (!userHasBadgeRibbon && !identity.isUserPositionBack) {
        if (identity.type == PosterIdentityType.glassyUser) {
          return -26;
        } else if (identity.type == PosterIdentityType.flatUserBadgeCircle) {
          return getFlatUserBadgeCircleBottomPosition();
        }
        return layout.shadowColor != null
            ? layout.enableOuterFrame
                ? 16
                : 0
            : 0;
      }
      switch (identity.type) {
        case PosterIdentityType.glassyUser:
          return 10;
        case PosterIdentityType.flatUserBadgeCircle:
          return getFlatUserBadgeCircleBottomPosition();
        default:
          return layout.shadowColor != null
              ? layout.enableOuterFrame
                  ? 63
                  : 47
              : 47;
      }
    }

    if (identity.type == PosterIdentityType.flatUserBadgeCircle) {
      return Positioned(
          right: getRightPosition(),
          bottom: getBottomPosition(),
          child: FlatUserBadgeCircle(
            user: identity.user,
            isUserPositionBack: identity.isUserPositionBack,
          ));
    } else {
      return Positioned(
          right: getRightPosition(),
          bottom: getBottomPosition(),
          child: getImage(identity.user));
    }
  }

  //Frame Gradients and Border Radius for Poster
  Gradient? getOuterFrameGradient() {
    return layout.shadowColor != null
        ? null
        : layout.gradients?.backgroundGradients.toGradient();
  }

  BorderRadius getFrameBorderRadius() {
    final List<PosterIdentityType> heavyBorderedIdentityTypes = [
      PosterIdentityType.premiumCorneredPartyIconShinyIdentity,
      PosterIdentityType.premiumCorneredPartyIconGradientIdentity,
    ];
    final identity = layout.identity;
    if (identity != null &&
        heavyBorderedIdentityTypes.contains(identity.type)) {
      return const BorderRadius.only(
        bottomRight: Radius.circular(80),
      );
    } else if (layout.isBorderedLayout) {
      return const BorderRadius.all(Radius.circular(16));
    } else if (identity != null &&
        identity.type == PosterIdentityType.polygonalProfileIdentity) {
      return const BorderRadius.only(
        bottomRight: Radius.circular(110),
      );
    }
    //0 default
    return BorderRadius.circular(0);
  }

  BorderRadius getImageBorderRadius() {
    final List<PosterIdentityType> heavyBorderedIdentityTypes = [
      PosterIdentityType.premiumCorneredPartyIconShinyIdentity,
      PosterIdentityType.premiumCorneredPartyIconGradientIdentity,
    ];
    final identity = layout.identity;
    if (identity != null &&
        heavyBorderedIdentityTypes.contains(identity.type)) {
      return const BorderRadius.only(
        topLeft: Radius.circular(80),
        topRight: Radius.circular(80),
        bottomRight: Radius.circular(80),
      );
    } else if (identity != null &&
        identity.type == PosterIdentityType.polygonalProfileIdentity) {
      return const BorderRadius.only(
        bottomRight: Radius.circular(110),
      );
    }
    //0 default
    return BorderRadius.circular(0);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: posterWidth,
      height: posterHeight,
      decoration: layout.shadowColor != null
          ? BoxDecoration(borderRadius: getFrameBorderRadius(), boxShadow: [
              BoxShadow(
                color: Color(layout.shadowColor!),
              ),
              BoxShadow(
                color: Color(layout.shadowColor!),
                blurRadius: 20,
                spreadRadius: 15,
              ),
              const BoxShadow(
                color: Colors.white,
                blurRadius: 5,
                spreadRadius: -6,
              ),
            ])
          : null,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: getOuterFrameGradient(),
                borderRadius: getFrameBorderRadius(),
              ),
              padding: EdgeInsets.all(layout.enableOuterFrame ? 16 : 0),
              child: layout.gradients?.innerBackgroundGradients != null
                  ? ClipRRect(
                      borderRadius: getFrameBorderRadius(),
                      child: GoldFrame(
                        innerBackgroundGradient:
                            layout.gradients!.innerBackgroundGradients!,
                        backgroundGradient:
                            layout.gradients!.backgroundGradients,
                        child: getCreativeImage(),
                      ),
                    )
                  : getCreativeImage(),
            ),
          ),
          ...getHeader1Photos(),
          ...getHeader2Photos(),
          getPartyTag(),
          getPosterUserAvatarBackSideIdentity(),
          getIdentity(),
          getPosterUserAvatarFrontSideIdentity(),
        ],
      ),
    );
  }
}
