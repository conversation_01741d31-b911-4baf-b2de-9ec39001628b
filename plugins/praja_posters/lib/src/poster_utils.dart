import 'package:flutter/material.dart';
import 'package:praja_posters/src/models/poster_badge.dart';

class PosterUtils {
  static LinearGradient getGoldAndSilverGradients(
      PosterBadgeBanner badgeBanner) {
    LinearGradient goldGradient = const LinearGradient(colors: [
      Color(0xffD27209),
      Color(0xffDB9E00),
      Color(0xffFEF2DC),
      Color(0xffDB9F01),
      Color(0xffBF5E17),
      Color(0xffD89703),
      Color(0xffFDF1D7),
      Color(0xffDA9C01),
      Color(0xffBD5B18),
      Color(0xffFEEC95),
      Color(0xffDEA60E),
      Color(0xffFDEB93),
    ]);
    LinearGradient silverGradient = const LinearGradient(colors: [
      Color(0xff8D8985),
      Color(0xffB1B1B0),
      Color(0xffF3F1EC),
      Color(0xffDAD9D6),
      Color(0xffB6B0AD),
      Color(0xffAFADA9),
      Color(0xffF1F0EE),
      Color(0xffA1A1A1),
      Color(0xff808080),
      Color(0xffC6C6C6),
      Color(0xffACACAC),
      Color(0xffBBBBBB),
    ]);
    LinearGradient whiteGradient = const LinearGradient(colors: [
      Color(0xff8899A6),
      Color(0xffAFC1D9),
      Color(0xffCADBF2),
      Color(0xffE4EEFC),
      Color(0xffCADBF2),
      Color(0xffC2D7F8),
      Color(0xffCADBF2),
      Color(0xffCADBF2),
      Color(0xffE4EEFC),
      Color(0xffCADBF2),
      Color(0xffC2D7F8),
      Color(0xffC2D7F8),
      Color(0xffE4EEFC),
      Color(0xffCADBF2),
      Color(0xffF1F0EE),
      Color(0xffE4EEFC),
      Color(0xffCADBF2),
      Color(0xffAFC1D9),
    ], stops: [
      0.0,
      0.02,
      0.13,
      0.15,
      0.17,
      0.23,
      0.33,
      0.37,
      0.42,
      0.46,
      0.55,
      0.69,
      0.72,
      0.85,
      0.89,
      0.9,
      0.96,
      1
    ]);
    switch (badgeBanner) {
      case PosterBadgeBanner.gold:
        return goldGradient;
      case PosterBadgeBanner.silver:
        return silverGradient;
      case PosterBadgeBanner.white:
        return whiteGradient;
      case PosterBadgeBanner.none:
        return const LinearGradient(
            colors: [Colors.transparent, Colors.transparent]);
    }
  }
}
