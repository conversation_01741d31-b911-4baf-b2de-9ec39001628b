import 'package:analyzer/dart/ast/ast.dart';
import 'package:analyzer/error/listener.dart';
import 'package:custom_lint_builder/custom_lint_builder.dart';

PluginBase createPlugin() => HardcodedStringsLinter();

class HardcodedStringsLinter extends PluginBase {
  @override
  List<LintRule> getLintRules(CustomLintConfigs configs) {
    return [
      const HardcodedStringsLintRule(),
    ];
  }
}

class HardcodedStringsLintRule extends DartLintRule {
  const HardcodedStringsLintRule() : super(code: _code);

  static const _code = LintCode(
      name: 'avoid_hardcoded_strings_in_ui',
      problemMessage: 'Avoid hardcoded strings in UI');

  @override
  void run(CustomLintResolver resolver, ErrorReporter reporter,
      CustomLintContext context) {
    context.registry.addStringLiteral((node) {
      final excludeRegex = RegExp(r'[a-zA-Z0-9\-_*\^\.\~\/\\]');
      if (node is SimpleStringLiteral) {
        if (node.value.trim().isNotEmpty &&
            !excludeRegex.hasMatch(node.value) &&
            _isInUiComponent(node)) {
          reporter.reportErrorForNode(_code, node, []);
        }
      } else if (node is StringInterpolation) {
        final shouldExclude = node.elements.every((e) {
          if (e is InterpolationString) {
            return e.value.isEmpty || excludeRegex.hasMatch(e.value);
          } else {
            return true;
          }
        });

        if (!shouldExclude && _isInUiComponent(node)) {
          reporter.reportErrorForNode(_code, node, []);
        }
      } else if (node is AdjacentStrings) {
        final shouldExclude = node.strings.every((e) {
          if (e is SimpleStringLiteral) {
            return e.value.isEmpty || excludeRegex.hasMatch(e.value);
          } else {
            return true;
          }
        });

        if (!shouldExclude && _isInUiComponent(node)) {
          reporter.reportErrorForNode(_code, node, []);
        }
      }
    });
  }

  bool _isInUiComponent(AstNode node) {
    var parent = node.parent;
    while (parent != null) {
      if (parent is MethodInvocation) {
        final methodName = parent.methodName.name;
        final returnTypeName = parent.staticType?.element?.name;
        if ('showToast'.presentIn(methodName) ||
            'showDialog'.presentIn(methodName) ||
            'showModalBottomSheet'.presentIn(methodName) ||
            'Widget'.presentIn(returnTypeName)) {
          return true;
        } else if ('logNonFatal'.presentIn(methodName)) {
          return false;
        }
      } else if (parent is ClassDeclaration) {
        final superClassName =
            parent.extendsClause?.superclass.type?.element?.name;
        if (superClassName == 'StatelessWidget' ||
            superClassName == 'StatefulWidget' ||
            superClassName == 'State' ||
            superClassName == 'ViewModel' ||
            'Widget'.presentIn(superClassName) ||
            'Screen'.presentIn(superClassName) ||
            'Page'.presentIn(superClassName)) {
          return true;
        }
      }
      parent = parent.parent;
    }
    return false;
  }
}

extension on String {
  bool presentIn(String? str) => str?.contains(this) ?? false;
}
