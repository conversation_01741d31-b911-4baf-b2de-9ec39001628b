// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'users_batch_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UsersBatchResponse _$UsersBatchResponseFromJson(Map<String, dynamic> json) =>
    UsersBatchResponse(
      users: (json['users'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, UserIdentity.fromJson(e as Map<String, dynamic>)),
      ),
      errors: (json['errors'] as List<dynamic>)
          .map((e) => BatchError.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UsersBatchResponseToJson(UsersBatchResponse instance) =>
    <String, dynamic>{
      'users': instance.users.map((k, e) => MapEntry(k, e.toJson())),
      'errors': instance.errors.map((e) => e.toJson()).toList(),
    };
