import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:marquee/marquee.dart';
import 'package:praja/features/posters/widgets/poster_constants.dart';
import 'package:praja/models/badge.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja_posters/praja_posters.dart';
import 'package:shimmer/shimmer.dart';

class SponsorBannerWidget extends StatefulWidget {
  final PosterSponsorBanner? sponsorBanner;
  final bool shimmer;
  const SponsorBannerWidget({
    super.key,
    this.sponsorBanner,
    this.shimmer = false,
  });

  @override
  State<SponsorBannerWidget> createState() => _SponsorBannerWidgetState();

  static const containerHeight = 36.0;
  static const labelHeight = bannerHeight - containerHeight;
}

class _SponsorBannerWidgetState extends State<SponsorBannerWidget> {
  @override
  void initState() {
    super.initState();
  }

  /// We are Using Marquee Widget for scrolling text, if text is overflow
  Widget _getTextWidget(PosterSponsorBanner sponsorBanner) {
    final textStyle = TextStyle(
      color: Color(sponsorBanner.line2TextColor),
      fontSize: 16,
      fontWeight: FontWeight.bold,
    );
    return Flexible(
      child: LayoutBuilder(builder: (context, constraints) {
        final sponsorBannerText = sponsorBanner.line2;

        bool isTextOverflow = false;
        final textPainter = TextPainter(
          text: TextSpan(
            text: sponsorBannerText,
            style: textStyle,
          ),
          textDirection: TextDirection.ltr,
          maxLines: 1,
        );
        textPainter.layout(maxWidth: constraints.maxWidth);
        if (textPainter.didExceedMaxLines) {
          isTextOverflow = true;
        }
        return isTextOverflow
            ? Marquee(
                text: sponsorBanner.line2,
                textScaleFactor: 1.0,
                style: textStyle,
                startAfter: const Duration(seconds: 2),
                blankSpace: 40,
                velocity: 50,
                scrollAxis: Axis.horizontal,
                crossAxisAlignment: CrossAxisAlignment.center,
                pauseAfterRound: const Duration(seconds: 5),
                accelerationCurve: Curves.linear,
                decelerationCurve: Curves.easeOut,
              )
            : Text(
                sponsorBanner.line2,
                overflow: TextOverflow.ellipsis,
                softWrap: true,
                textAlign: TextAlign.center,
                textScaleFactor: 1,
                maxLines: 1,
                style: textStyle,
              );
      }),
    );
  }

  Widget _getBody() {
    final sponsorBanner = widget.sponsorBanner;
    const containerHeight = SponsorBannerWidget.containerHeight;
    const containerWidth = double.infinity;
    const labelHeight = SponsorBannerWidget.labelHeight;

    if (sponsorBanner == null) {
      return const SizedBox(
        height: containerHeight,
      );
    }
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: containerHeight,
          width: containerWidth,
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          margin: const EdgeInsets.only(top: labelHeight),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(10),
              bottomLeft: Radius.circular(10),
              bottomRight: Radius.circular(10),
            ),
            gradient: sponsorBanner.gradients.toGradient(),
            border: GradientBoxBorder(
              gradient: Utils.getGoldAndSilverGradients(BadgeBanner.gold),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular((containerHeight - 10) / 4),
                child: CachedNetworkImage(
                  imageUrl: sponsorBanner.iconUrl,
                  height: containerHeight - 10,
                  width: containerHeight - 10,
                  fit: BoxFit.contain,
                  fadeInDuration: const Duration(milliseconds: 0),
                  fadeOutDuration: const Duration(milliseconds: 0),
                  imageBuilder: (context, imageProvider) {
                    return Container(
                      height: containerHeight - 10,
                      width: containerHeight - 10,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.contain,
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 4),
              _getTextWidget(sponsorBanner),
            ],
          ),
        ),
        Shimmer.fromColors(
          loop: 1,
          enabled: widget.shimmer,
          baseColor: Colors.transparent,
          highlightColor: Colors.amber.shade200,
          child: Container(
            margin: const EdgeInsets.only(top: labelHeight),
            height: containerHeight,
            width: containerWidth,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: GradientBoxBorder(
                gradient: Utils.getGoldAndSilverGradients(BadgeBanner.gold),
              ),
            ),
          ),
        ),
        Positioned(
          top: -(1.05 * containerHeight) / 2.5 + labelHeight,
          right: 0,
          child: SizedBox(
            height: labelHeight,
            width: 80,
            child: _SponsorClipperWidget(
              text: sponsorBanner.line1,
            ),
          ),
        ),
        Positioned(
          top: -(1.05 * containerHeight) / 2.5 + labelHeight,
          right: 0,
          child: SizedBox(
            height: labelHeight,
            width: 80,
            child: Shimmer.fromColors(
              loop: 1,
              enabled: widget.shimmer,
              baseColor: Colors.transparent,
              highlightColor: Colors.amber.shade200,
              child: _SponsorClipperWidget(
                text: sponsorBanner.line1,
              ),
            ),
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return _getBody();
  }
}

class _SponsorClipperWidget extends StatelessWidget {
  final String text;
  const _SponsorClipperWidget({this.text = 'Sponsor'});

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: _SponsorClipper(),
      child: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(topRight: Radius.circular(8)),
          gradient: Styles.backgroundGoldGradient(),
        ),
        child: Center(
          child: Text(
            text,
            textScaleFactor: 1.0,
            style: const TextStyle(
                color: Colors.black, fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }
}

class _SponsorClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.moveTo(0, size.height / 2);
    path.lineTo(10, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width, size.height);
    path.lineTo(10, size.height);
    path.lineTo(0, size.height / 2);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(_SponsorClipper oldClipper) => false;
}
