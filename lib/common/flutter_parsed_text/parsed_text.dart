import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:praja/common/flutter_parsed_text/match_text.dart';
import 'package:praja/common/flutter_parsed_text/regex_options.dart';
import 'package:extended_text/extended_text.dart';

/// Parse text and make them into multiple Flutter Text widgets
class ParsedText extends StatelessWidget {
  final TextStyle? style;
  final TextOverflowWidget? overflowWidget;

  /// Takes a list of [MatchText] object.
  ///
  /// This list is used to find patterns in the String and assign onTap [Function] when its
  /// tapped and also to provide custom styling to the linkify text
  final List<MatchText> parse;
  final String text;
  final TextAlign alignment;
  final TextDirection? textDirection;
  final bool softWrap;
  final TextOverflow overflow;
  final double textScaleFactor;
  final int? maxLines;
  final StrutStyle? strutStyle;
  final TextWidthBasis textWidthBasis;

  /// Make this text selectable.
  ///
  /// SelectableText does not support softWrap, overflow, textScaleFactor
  final bool selectable;
  final Function? onTap;

  /// Global regex options for the whole string,
  ///
  /// Note: Removed support for regexOptions for MatchText and now it uses global regex options.
  final RegexOptions regexOptions;

  /// Creates a parsedText widget
  ///
  /// [text] parameter should not be null and is always required.
  /// If the [style] argument is null, the text will use the style from the
  /// closest enclosing [DefaultTextStyle].
  const ParsedText({
    super.key,
    required this.text,
    this.parse = const <MatchText>[],
    this.style,
    this.alignment = TextAlign.start,
    this.textDirection,
    this.softWrap = true,
    this.overflow = TextOverflow.clip,
    this.textScaleFactor = 1.0,
    this.strutStyle,
    this.textWidthBasis = TextWidthBasis.parent,
    this.maxLines,
    this.onTap,
    this.selectable = false,
    this.overflowWidget,
    this.regexOptions = const RegexOptions(),
  });

  @override
  Widget build(BuildContext context) {
    // Separate each word and create a new Array
    String newString = text;

    Map<String, MatchText> mapping0 = <String, MatchText>{};

    for (var e in parse) {
      if (e.type == ParsedType.EMAIL) {
        mapping0[emailPattern] = e;
      } else if (e.type == ParsedType.PHONE) {
        mapping0[phonePattern] = e;
      } else if (e.type == ParsedType.URL) {
        mapping0[urlPattern] = e;
      } else {
        mapping0[e.pattern!] = e;
      }
    }

    // ignore: avoid_hardcoded_strings_in_ui
    final pattern = '(${mapping0.keys.toList().join('|')})';

    List<InlineSpan> widgets = [];

    newString.splitMapJoin(
      RegExp(
        pattern,
        multiLine: regexOptions.multiLine,
        caseSensitive: regexOptions.caseSensitive,
        dotAll: regexOptions.dotAll,
        unicode: regexOptions.unicode,
      ),
      onMatch: (Match match) {
        final matchText = match[0];

        final mapping = mapping0[matchText!] ??
            mapping0[mapping0.keys.firstWhere((element) {
              final reg = RegExp(
                element,
                multiLine: regexOptions.multiLine,
                caseSensitive: regexOptions.caseSensitive,
                dotAll: regexOptions.dotAll,
                unicode: regexOptions.unicode,
              );
              return reg.hasMatch(matchText);
            }, orElse: () {
              return '';
            })];

        InlineSpan widget;

        if (mapping != null) {
          if (mapping.renderText != null) {
            Map<String, String> result =
                mapping.renderText!(str: matchText, pattern: pattern);

            widget = TextSpan(
              text: "${result['display']}",
              style: mapping.style ?? style,
              recognizer: TapGestureRecognizer()
                ..onTap = () => mapping.onTap!(matchText),
            );
          } else if (mapping.renderWidget != null) {
            widget = WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: GestureDetector(
                onTap: () => mapping.onTap!(matchText),
                child: mapping.renderWidget!(
                    text: matchText, pattern: mapping.pattern!),
              ),
            );
          } else {
            widget = TextSpan(
              text: matchText,
              style: mapping.style ?? style,
              recognizer: TapGestureRecognizer()
                ..onTap = () => mapping.onTap!(matchText),
            );
          }
        } else {
          widget = TextSpan(
            text: matchText,
            style: style,
          );
        }

        widgets.add(widget);

        return '';
      },
      onNonMatch: (String text) {
        widgets.add(TextSpan(
          text: text,
          style: style,
        ));

        return '';
      },
    );

    if (selectable) {
      return SelectableText.rich(
        TextSpan(children: <InlineSpan>[...widgets], style: style),
        maxLines: maxLines,
        strutStyle: strutStyle,
        textWidthBasis: textWidthBasis,
        textAlign: alignment,
        textDirection: textDirection,
        onTap: onTap as void Function()?,
      );
    }

    return ExtendedText.rich(
      TextSpan(children: <InlineSpan>[...widgets], style: style),
      softWrap: softWrap,
      overflow: overflow,
      textScaler: TextScaler.linear(textScaleFactor),
      maxLines: maxLines,
      strutStyle: strutStyle,
      textWidthBasis: textWidthBasis,
      textAlign: alignment,
      textDirection: textDirection,
      overflowWidget: overflowWidget,
    );
  }
}
