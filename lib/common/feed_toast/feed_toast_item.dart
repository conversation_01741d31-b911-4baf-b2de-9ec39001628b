import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/common/feed_toast/feed_toast_service.dart';
import 'package:praja/common/feed_toast/feed_toast_share_card.dart';
import 'package:praja/common/feed_toast/feed_toast_type_enum.dart';
import 'package:praja/common/widgets/live_indicator_widget.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/capture/capture_widget.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/whatsapp_share/whatsapp_share.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/feed_toast.dart';
import 'package:praja/models/live_config.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/presentation/view_detector.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/services/post/post_service.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class FeedToastItem extends StatefulWidget {
  final FeedToast feedToast;
  final String source;

  const FeedToastItem({
    super.key,
    required this.feedToast,
    required this.source,
  });

  @override
  State<FeedToastItem> createState() => _FeedToastItemState();
}

class _FeedToastItemState extends State<FeedToastItem>
    with AutomaticKeepAliveClientMixin {
  late FeedToast feedToast;
  DateTime _lastPollTime = DateTime.now();

  static const double _borderRadius = 10;

  bool _hideToast = false;

  final PostService _postService = GetIt.I.get<PostService>();
  final FeedToastService _feedToastService = GetIt.I.get<FeedToastService>();

  final Map<String, dynamic> _feedToastParams = {};

  StreamSubscription? _feedToastPollingSubscription;

  @override
  void initState() {
    super.initState();
    feedToast = widget.feedToast;
    _feedToastParams.addAll(
      {
        "source": widget.source,
        "feed_toast_type": feedToast.feedToastType.name,
        "has_image": feedToast.imageUrl != null,
        "has_header": feedToast.header.isNotEmpty,
        "has_message": feedToast.message.isNotEmpty,
        "redirect_to": feedToast.ctaUrl ?? "NA",
        "is_live_toast": feedToast.liveConfig != null,
      },
    );
    AppAnalytics.logEvent(
        name: "feed_toast_shown", parameters: _feedToastParams);
  }

  void _startPolling() {
    if (feedToast.liveConfig != null) {
      _feedToastPollingSubscription?.cancel();
      final currentTime = DateTime.now();
      if (currentTime.difference(_lastPollTime).inSeconds >
          feedToast.liveConfig!.refreshInterval) {
        _fetchLiveFeedToast(feedToast.liveConfig!.pollingUrl);
      }
      final liveConfig = feedToast.liveConfig!;
      final pollingUrl = liveConfig.pollingUrl;
      final refreshInterval = liveConfig.refreshInterval;
      _feedToastPollingSubscription =
          Stream.periodic(Duration(seconds: refreshInterval), (_) {
        _fetchLiveFeedToast(pollingUrl);
      }).listen((_) {});
    }
  }

  void _stopPolling() {
    _feedToastPollingSubscription?.cancel();
    _feedToastPollingSubscription = null;
  }

  @override
  void dispose() {
    _stopPolling();
    super.dispose();
  }

  void _fetchLiveFeedToast(String pollingUrl) async {
    try {
      final fetchedFeedToast =
          await _feedToastService.fetchFeedToast(url: pollingUrl);
      setState(() {
        feedToast = fetchedFeedToast;
        _lastPollTime = DateTime.now();
      });
    } catch (e) {
      logNonFatalIfAppError('Error while fetching live feed toast', e);
    }
  }

  void _onCloseClick() async {
    AppAnalytics.logEvent(
        name: "feed_toast_close_click", parameters: _feedToastParams);
    setState(() {
      _hideToast = true;
    });
    final feedItemId = feedToast.feedItemId;
    try {
      if (feedItemId != null) {
        await _postService.closeToast(feedItemId);
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  void _onToastClick() {
    AppAnalytics.logEvent(
        name: "feed_toast_clicked", parameters: _feedToastParams);
    final ctaUrl = feedToast.ctaUrl;
    if (ctaUrl == null) return;
    launchUrl(Uri.parse(ctaUrl));
  }

  Future<CaptureResult> _shareCapture(BuildContext context) async {
    return await context.captureWidget((ctx, readyForCapture) {
      return FeedToastShareCard(
          feedToast: feedToast, onLoaded: readyForCapture);
    });
  }

  void _onOtherAppsShareClick(BuildContext context) async {
    AppAnalytics.logShare(
        contentType: 'feed_toast',
        itemId: feedToast.feedItemId.toString(),
        method: 'external');
    if (!feedToast.shareImage) {
      await Share.share(feedToast.shareText);
      return;
    }

    final capturedResult = await _shareCapture(context);

    if (capturedResult is CaptureSuccess) {
      final imagePath = capturedResult.imagePath;
      await _shareToOtherApps(
          imagePath: imagePath, message: feedToast.shareText);
    } else if (capturedResult is CaptureFailure) {
      Utils.showToast(localisedErrorMessage(capturedResult.error));
      logNonFatalIfAppError(
          'Error while capturing feedToast for share', capturedResult.error,
          stackTrace: capturedResult.stackTrace);
    }
  }

  void _onWhatsappShareClick(BuildContext context) async {
    AppAnalytics.logShare(
        contentType: 'feed_toast',
        itemId: feedToast.feedItemId.toString(),
        method: 'whatsapp');
    if (!feedToast.shareImage) {
      if (Platform.isAndroid) {
        await WhatsappShareAndroid.shareText(feedToast.shareText);
      } else {
        await Share.share(feedToast.shareText);
      }
      return;
    }

    final capturedResult = await _shareCapture(context);

    if (capturedResult is CaptureSuccess) {
      final imagePath = capturedResult.imagePath;
      if (Platform.isAndroid) {
        await WhatsappShareAndroid.shareFiles([XFile(imagePath)],
            text: feedToast.shareText);
      } else {
        await _shareToOtherApps(
            imagePath: imagePath, message: feedToast.shareText);
      }
    } else if (capturedResult is CaptureFailure) {
      Utils.showToast(localisedErrorMessage(capturedResult.error));
      logNonFatalIfAppError(
          'Error while capturing feedToast for share', capturedResult.error,
          stackTrace: capturedResult.stackTrace);
    }
  }

  Future<void> _shareToOtherApps(
      {String? message, required String imagePath}) async {
    await Share.shareXFiles([XFile(imagePath)], text: message);
  }

  Widget _getBody() {
    final isImageToast = feedToast.imageUrl != null;
    final containsHeader = feedToast.header.isNotEmpty;
    final containsMessage = feedToast.message.isNotEmpty;
    final containsCta =
        feedToast.ctaText.isNotEmpty && feedToast.ctaUrl != null;

    if (!isImageToast && !containsHeader && !containsMessage) {
      return const SizedBox();
    } else {
      return _hideToast
          ? const SizedBox()
          : InkWell(
              onTap: _onToastClick,
              child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                  child: Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(_borderRadius),
                      color: Color(feedToast.toastBgColor),
                    ),
                    child: Column(
                      children: [
                        if (isImageToast) ...[
                          AspectRatio(
                              aspectRatio:
                                  feedToast.imageWidth / feedToast.imageHeight,
                              child: Stack(
                                children: [
                                  FeedToastImage.fromFeedToast(feedToast),
                                  if (feedToast.isRemovable)
                                    Positioned.fill(
                                        child: Container(
                                      width: double.infinity,
                                      height: double.infinity,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          transform:
                                              const GradientRotation(-0.5),
                                          begin: Alignment.topRight,
                                          end: Alignment.bottomLeft,
                                          colors: [
                                            Colors.black.withOpacity(0.15),
                                            Colors.white.withOpacity(0.0),
                                            Colors.white.withOpacity(0.0),
                                            Colors.transparent,
                                          ],
                                          stops: const [0.11, 0.2, 0.1, 1.0],
                                        ),
                                      ),
                                    )),
                                  feedToast.isRemovable
                                      ? Positioned(
                                          top: 5,
                                          right: 5,
                                          child: InkWell(
                                            onTap: _onCloseClick,
                                            child: const Icon(
                                              Icons.close,
                                              color: Colors.white,
                                              size: 18,
                                            ),
                                          ),
                                        )
                                      : const SizedBox(),
                                ],
                              ))
                        ],
                        (isImageToast && (!containsHeader && !containsMessage))
                            ? const SizedBox()
                            : FeedToastTextSection.fromFeedToast(feedToast,
                                onCloseClick: _onCloseClick),
                        if (feedToast.isShareable)
                          Row(
                            children: [
                              Expanded(
                                  child: InkWell(
                                      onTap: () {
                                        _onOtherAppsShareClick(context);
                                      },
                                      child: Container(
                                          color: Colors.grey.shade100,
                                          height: 48,
                                          child: Center(
                                              child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                const Icon(
                                                    PrajaIcons.circle_share),
                                                const SizedBox(width: 12),
                                                Text(
                                                    context.getString(
                                                        StringKey.shareLabel),
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.w500))
                                              ]))))),
                              Expanded(
                                  child: InkWell(
                                      onTap: () {
                                        _onWhatsappShareClick(context);
                                      },
                                      child: Container(
                                          color: Styles.whatsappColor,
                                          height: 48,
                                          child: Center(
                                              child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                const Icon(
                                                    PrajaIcons
                                                        .circle_whatsapp_fill,
                                                    color: Colors.white),
                                                const SizedBox(width: 12),
                                                Text(
                                                    context.getString(StringKey
                                                        .whatsappLabel),
                                                    style: const TextStyle(
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.w500))
                                              ]))))),
                            ],
                          ),
                        if (containsCta) ...[
                          InkWell(
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: _onToastClick,
                            child: Container(
                              width: double.infinity,
                              height: 40,
                              color: Color(feedToast.iconColor),
                              alignment: Alignment.center,
                              child: Text(
                                feedToast.ctaText,
                                textScaler: const TextScaler.linear(1),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: Color(feedToast.ctaTextColor),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  )),
            );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ViewDetector(
      uniqueId: "feed_toast_${feedToast.feedItemId}",
      onView: (_) {
        _startPolling();
      },
      onOutOfView: () {
        _stopPolling();
      },
      builder: (ctx, _) => _getBody(),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class FeedToastImage extends StatelessWidget {
  final String imageUrl;
  final double width;
  final double height;
  final VoidCallback? onImageLoaded;
  final LiveConfig? liveConfig;

  const FeedToastImage({
    super.key,
    required this.imageUrl,
    required this.width,
    required this.height,
    this.onImageLoaded,
    this.liveConfig,
  });

  factory FeedToastImage.fromFeedToast(FeedToast feedToast,
      {VoidCallback? onImageLoaded}) {
    return FeedToastImage(
      imageUrl: feedToast.imageUrl!,
      width: feedToast.imageWidth,
      height: feedToast.imageHeight,
      onImageLoaded: onImageLoaded,
      liveConfig: feedToast.liveConfig,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: width / height,
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        imageBuilder: (context, imageProvider) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            onImageLoaded?.call();
          });
          return Stack(
            children: [
              Positioned.fill(
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              if (liveConfig != null)
                const Positioned(
                  top: 10,
                  left: 10,
                  child: LiveIndicatorWidget(),
                )
            ],
          );
        },
        placeholder: (context, url) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.grey[300],
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        },
        cacheManager: AppCacheManager.instance,
      ),
    );
  }
}

class FeedToastTextSection extends StatelessWidget {
  final FeedToastTypeEnum type;
  final String header;
  final String message;
  final Color iconColor;
  final double headerFontSize;
  final Color headerFontColor;
  final double messageFontSize;
  final Color messageFontColor;
  final bool showCloseButton;
  final VoidCallback? onCloseClick;

  const FeedToastTextSection({
    super.key,
    required this.type,
    required this.header,
    required this.message,
    required this.iconColor,
    required this.headerFontSize,
    required this.headerFontColor,
    required this.messageFontSize,
    required this.messageFontColor,
    required this.showCloseButton,
    this.onCloseClick,
  });

  factory FeedToastTextSection.fromFeedToast(FeedToast feedToast,
      {VoidCallback? onCloseClick}) {
    return FeedToastTextSection(
      type: feedToast.feedToastType,
      header: feedToast.header,
      message: feedToast.message,
      iconColor: Color(feedToast.iconColor),
      headerFontSize: feedToast.headerFontSize,
      headerFontColor: Color(feedToast.headerFontColor),
      messageFontSize: feedToast.messageFontSize,
      messageFontColor: Color(feedToast.messageFontColor),
      showCloseButton: feedToast.imageUrl == null && feedToast.isRemovable,
      onCloseClick: onCloseClick,
    );
  }

  Widget _getIcon() {
    const iconSize = 24.0;
    Icon? icon;
    switch (type) {
      case FeedToastTypeEnum.none:
        return const SizedBox();
      case FeedToastTypeEnum.announcement:
        icon = Icon(Icons.campaign, color: iconColor, size: iconSize);
      case FeedToastTypeEnum.info:
        icon = Icon(Icons.info, color: iconColor, size: iconSize);
      case FeedToastTypeEnum.warning:
        icon = Icon(Icons.warning, color: iconColor, size: iconSize);
      case FeedToastTypeEnum.error:
        icon = Icon(Icons.error, color: iconColor, size: iconSize);
      case FeedToastTypeEnum.tip:
        icon = Icon(Icons.lightbulb, color: iconColor, size: iconSize);
    }
    return Padding(
      padding: const EdgeInsets.only(right: 10.0),
      child: icon,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          _getIcon(),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      if (header.isNotEmpty) ...[
                        Text(
                          header,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: headerFontSize,
                            fontWeight: FontWeight.bold,
                            color: headerFontColor,
                          ),
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                      ],
                      if (message.isNotEmpty) ...[
                        Text(
                          message,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: messageFontSize,
                            color: messageFontColor,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                showCloseButton
                    ? IconButton(
                        onPressed: onCloseClick,
                        icon: const Icon(
                          Icons.close,
                          color: Colors.black,
                        ),
                      )
                    : const SizedBox()
              ],
            ),
          )
        ],
      ),
    );
  }
}
