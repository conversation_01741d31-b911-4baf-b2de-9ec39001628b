import 'package:flutter/material.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/utils/logger.dart';

/// A widget that waits until the route is current route
/// before including child widget in the widget tree.
class LazyLoadWidget extends StatefulWidget {
  final Widget child;

  const LazyLoadWidget({super.key, required this.child});

  @override
  State<LazyLoadWidget> createState() => _LazyLoadWidgetState();
}

class _LazyLoadWidgetState extends State<LazyLoadWidget> with RouteAware {
  bool _hasThisRouteBeenAtTop = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route == null) return;

    final isCurrentRoute = route.isCurrent;
    if (isCurrentRoute) {
      setState(() {
        _hasThisRouteBeenAtTop = isCurrentRoute;
      });
    } else {
      logDebug(
          "LazyLoadWidget: Not in current route, subscribing to route changes");
      PageViewReporter.routeObserver.subscribe(this, route);
    }
  }

  @override
  void didPopNext() {
    super.didPopNext();

    if (_hasThisRouteBeenAtTop) return;

    logDebug("LazyLoadWidget: Route appeared at top, showing the child widget");
    setState(() {
      _hasThisRouteBeenAtTop = true;
    });
    PageViewReporter.routeObserver.unsubscribe(this);
  }

  @override
  void dispose() {
    if (!_hasThisRouteBeenAtTop) {
      PageViewReporter.routeObserver.unsubscribe(this);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _hasThisRouteBeenAtTop ? widget.child : const SizedBox();
  }
}
