import 'dart:convert';

import 'package:floor/floor.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:praja/features/direct_messaging/api/models/conversation.dart';

@Entity(tableName: 'conversations', indices: [
  Index(value: ['id'], unique: true),
])
class DbConversation {
  @PrimaryKey(autoGenerate: false)
  final String id;

  @ColumnInfo(name: 'type')
  final String serialisedType;

  @ColumnInfo(name: 'created_at')
  final int createdAt;

  @ColumnInfo(name: 'updated_at')
  final int updatedAt;

  @ColumnInfo(name: 'last_active_at')
  final int lastActiveAt;

  @ColumnInfo(name: 'is_primary')
  final bool isPrimary;

  @ColumnInfo(name: 'is_muted')
  final bool isMuted;

  @ColumnInfo(name: 'status')
  final String status;

  @ColumnInfo(name: 'other_participant')
  final String? otherParticipant;

  @ColumnInfo(name: 'analytics_params')
  final String? backingFieldAnalyticsParams;

  @ColumnInfo(name: 'circle_id')
  final String? circleId;

  @ColumnInfo(name: 'permissions')
  final String? permissions;

  @ColumnInfo(name: 'is_participant')
  final bool isParticipant;

  List<String> get permissionsList =>
      permissions != null ? permissions!.split(',') : [];

  Map<String, dynamic> get analyticsParams =>
      backingFieldAnalyticsParams != null
          ? jsonDecode(backingFieldAnalyticsParams!)
          : {};

  DbConversation({
    required this.id,
    required this.serialisedType,
    required this.createdAt,
    required this.updatedAt,
    required this.lastActiveAt,
    required this.isPrimary,
    required this.isMuted,
    required this.status,
    required this.otherParticipant,
    required this.backingFieldAnalyticsParams,
    required this.permissions,
    required this.circleId,
    required this.isParticipant,
  });

  DbConversation copyWith({
    String? id,
    ConversationType? type,
    int? createdAt,
    int? updatedAt,
    int? lastActiveAt,
    String? serialisedType,
    bool? isPrimary,
    bool? isMuted,
    ConversationStatus? status,
    String? otherParticipant,
    String? circleId,
    String? permissions,
    bool? isParticipant,
  }) {
    return DbConversation(
      id: id ?? this.id,
      serialisedType: type?.value ?? this.serialisedType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      isPrimary: isPrimary ?? this.isPrimary,
      isMuted: isMuted ?? this.isMuted,
      status: status?.value ?? this.status,
      otherParticipant: otherParticipant ?? this.otherParticipant,
      backingFieldAnalyticsParams: backingFieldAnalyticsParams,
      circleId: circleId ?? this.circleId,
      permissions: permissions ?? this.permissions,
      isParticipant: isParticipant ?? this.isParticipant,
    );
  }

  //from ApiConversationResponse
  factory DbConversation.fromConversationModel(
    Conversation conversation,
    final String currentUserId,
  ) {
    return DbConversation(
        id: conversation.id,
        serialisedType: conversation.type.value,
        createdAt: conversation.createdAt.millisecondsSinceEpoch,
        updatedAt: conversation.updatedAt.millisecondsSinceEpoch,
        lastActiveAt: conversation.lastActiveAt.millisecondsSinceEpoch,
        isPrimary: conversation.isPrimary,
        isMuted: conversation.isMuted,
        status: conversation.status.value,
        otherParticipant: conversation.type == ConversationType.oneToOne
            ? conversation.participants
                .where((element) => element != currentUserId)
                .toList()
                .first
            : null,
        circleId: conversation.circleId,
        backingFieldAnalyticsParams: conversation.analyticsParams != null
            ? json.encode(conversation.analyticsParams)
            : null,
        permissions: conversation.permissions.join(','),
        isParticipant: conversation.isParticipant);
  }

  ConversationType get type {
    return ConversationType.from(serialisedType);
  }
}

enum ConversationType {
  @JsonValue('ONE-TO-ONE')
  oneToOne('ONE-TO-ONE'),
  @JsonValue('PRIVATE-GROUP')
  privateGroup('PRIVATE-GROUP'),
  @JsonValue('CHANNEL')
  channel('CHANNEL'),
  @JsonValue('UNKNOWN')
  unknown('UNKNOWN');

  final String value;

  const ConversationType(this.value);

  factory ConversationType.from(String value) {
    return ConversationType.values.firstWhere(
      (e) => e.value == value,
      orElse: () => ConversationType.unknown,
    );
  }
}

enum ConversationStatus {
  @JsonValue('ACTIVE')
  active('ACTIVE'),
  @JsonValue('SELF_BLOCKED')
  selfBlocked('SELF_BLOCKED'),
  @JsonValue('RECEIVER_BLOCKED')
  receiverBlocked('RECEIVER_BLOCKED');

  final String value;

  const ConversationStatus(this.value);

  factory ConversationStatus.from(String value) {
    return ConversationStatus.values.firstWhere(
      (e) => e.value == value,
    );
  }
}
