// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_database.dart';

// **************************************************************************
// FloorGenerator
// **************************************************************************

abstract class $MessagingDatabaseBuilderContract {
  /// Adds migrations to the builder.
  $MessagingDatabaseBuilderContract addMigrations(List<Migration> migrations);

  /// Adds a database [Callback] to the builder.
  $MessagingDatabaseBuilderContract addCallback(Callback callback);

  /// Creates the database and initializes it.
  Future<MessagingDatabase> build();
}

// ignore: avoid_classes_with_only_static_members
class $FloorMessagingDatabase {
  /// Creates a database builder for a persistent database.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $MessagingDatabaseBuilderContract databaseBuilder(String name) =>
      _$MessagingDatabaseBuilder(name);

  /// Creates a database builder for an in memory database.
  /// Information stored in an in memory database disappears when the process is killed.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $MessagingDatabaseBuilderContract inMemoryDatabaseBuilder() =>
      _$MessagingDatabaseBuilder(null);
}

class _$MessagingDatabaseBuilder implements $MessagingDatabaseBuilderContract {
  _$MessagingDatabaseBuilder(this.name);

  final String? name;

  final List<Migration> _migrations = [];

  Callback? _callback;

  @override
  $MessagingDatabaseBuilderContract addMigrations(List<Migration> migrations) {
    _migrations.addAll(migrations);
    return this;
  }

  @override
  $MessagingDatabaseBuilderContract addCallback(Callback callback) {
    _callback = callback;
    return this;
  }

  @override
  Future<MessagingDatabase> build() async {
    final path = name != null
        ? await sqfliteDatabaseFactory.getDatabasePath(name!)
        : ':memory:';
    final database = _$MessagingDatabase();
    database.database = await database.open(
      path,
      _migrations,
      _callback,
    );
    return database;
  }
}

class _$MessagingDatabase extends MessagingDatabase {
  _$MessagingDatabase([StreamController<String>? listener]) {
    changeListener = listener ?? StreamController<String>.broadcast();
  }

  MessageDao? _messageDaoInstance;

  Future<sqflite.Database> open(
    String path,
    List<Migration> migrations, [
    Callback? callback,
  ]) async {
    final databaseOptions = sqflite.OpenDatabaseOptions(
      version: 1,
      onConfigure: (database) async {
        await database.execute('PRAGMA foreign_keys = ON');
        await callback?.onConfigure?.call(database);
      },
      onOpen: (database) async {
        await callback?.onOpen?.call(database);
      },
      onUpgrade: (database, startVersion, endVersion) async {
        await MigrationAdapter.runMigrations(
            database, startVersion, endVersion, migrations);

        await callback?.onUpgrade?.call(database, startVersion, endVersion);
      },
      onCreate: (database, version) async {
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `conversations` (`id` TEXT NOT NULL, `type` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, `last_active_at` INTEGER NOT NULL, `is_primary` INTEGER NOT NULL, `is_muted` INTEGER NOT NULL, `status` TEXT NOT NULL, `other_participant` TEXT, `analytics_params` TEXT, `circle_id` TEXT, `permissions` TEXT, `is_participant` INTEGER NOT NULL, PRIMARY KEY (`id`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `messages` (`id` TEXT NOT NULL, `conversation_id` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, `received_at` INTEGER NOT NULL, `status` TEXT NOT NULL, `status_to_be_reported` INTEGER NOT NULL, `modified_status` TEXT NOT NULL, `type` TEXT NOT NULL, `sender_id` TEXT, `message_data` TEXT NOT NULL, PRIMARY KEY (`id`))');
        await database.execute(
            'CREATE UNIQUE INDEX `index_conversations_id` ON `conversations` (`id`)');
        await database.execute(
            'CREATE UNIQUE INDEX `index_messages_id` ON `messages` (`id`)');
        await database.execute(
            'CREATE VIEW IF NOT EXISTS `ConversationView` AS SELECT * FROM conversations INNER JOIN ((SELECT conversation_id, MAX(created_at) AS lastMessageTime, message_data AS lastMessageData, type as lastMessageType, status as lastMessageStatus FROM messages GROUP BY conversation_id) AS lastMessages LEFT JOIN (SELECT conversation_id, COUNT(id) AS unreadCount FROM messages WHERE type = \'NORMAL\' AND status = \'UNREAD\' GROUP BY conversation_id) AS unreadCounts ON lastMessages.conversation_id = unreadCounts.conversation_id) AS lastMessagesWithUnread ON conversations.id = lastMessagesWithUnread.conversation_id WHERE conversations.is_participant = 1 ');

        await callback?.onCreate?.call(database, version);
      },
    );
    return sqfliteDatabaseFactory.openDatabase(path, options: databaseOptions);
  }

  @override
  MessageDao get messageDao {
    return _messageDaoInstance ??= _$MessageDao(database, changeListener);
  }
}

class _$MessageDao extends MessageDao {
  _$MessageDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _dbConversationInsertionAdapter = InsertionAdapter(
            database,
            'conversations',
            (DbConversation item) => <String, Object?>{
                  'id': item.id,
                  'type': item.serialisedType,
                  'created_at': item.createdAt,
                  'updated_at': item.updatedAt,
                  'last_active_at': item.lastActiveAt,
                  'is_primary': item.isPrimary ? 1 : 0,
                  'is_muted': item.isMuted ? 1 : 0,
                  'status': item.status,
                  'other_participant': item.otherParticipant,
                  'analytics_params': item.backingFieldAnalyticsParams,
                  'circle_id': item.circleId,
                  'permissions': item.permissions,
                  'is_participant': item.isParticipant ? 1 : 0
                },
            changeListener),
        _dbMessageInsertionAdapter = InsertionAdapter(
            database,
            'messages',
            (DbMessage item) => <String, Object?>{
                  'id': item.id,
                  'conversation_id': item.conversationId,
                  'created_at': item.createdAt,
                  'updated_at': item.updatedAt,
                  'received_at': item.receivedAt,
                  'status': item.status,
                  'status_to_be_reported': item.statusToBeReported ? 1 : 0,
                  'modified_status': item.modifiedStatus,
                  'type': item.type,
                  'sender_id': item.senderId,
                  'message_data': item.messageData
                },
            changeListener),
        _dbConversationUpdateAdapter = UpdateAdapter(
            database,
            'conversations',
            ['id'],
            (DbConversation item) => <String, Object?>{
                  'id': item.id,
                  'type': item.serialisedType,
                  'created_at': item.createdAt,
                  'updated_at': item.updatedAt,
                  'last_active_at': item.lastActiveAt,
                  'is_primary': item.isPrimary ? 1 : 0,
                  'is_muted': item.isMuted ? 1 : 0,
                  'status': item.status,
                  'other_participant': item.otherParticipant,
                  'analytics_params': item.backingFieldAnalyticsParams,
                  'circle_id': item.circleId,
                  'permissions': item.permissions,
                  'is_participant': item.isParticipant ? 1 : 0
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<DbConversation> _dbConversationInsertionAdapter;

  final InsertionAdapter<DbMessage> _dbMessageInsertionAdapter;

  final UpdateAdapter<DbConversation> _dbConversationUpdateAdapter;

  @override
  Future<DbConversation?> getConversationWithUser(String userId) async {
    return _queryAdapter.query(
        'SELECT * FROM conversations WHERE other_participant = ?1',
        mapper: (Map<String, Object?> row) => DbConversation(
            id: row['id'] as String,
            serialisedType: row['type'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            lastActiveAt: row['last_active_at'] as int,
            isPrimary: (row['is_primary'] as int) != 0,
            isMuted: (row['is_muted'] as int) != 0,
            status: row['status'] as String,
            otherParticipant: row['other_participant'] as String?,
            backingFieldAnalyticsParams: row['analytics_params'] as String?,
            permissions: row['permissions'] as String?,
            circleId: row['circle_id'] as String?,
            isParticipant: (row['is_participant'] as int) != 0),
        arguments: [userId]);
  }

  @override
  Future<DbConversation?> getConversationWithCircle(
    String circleId,
    String type,
  ) async {
    return _queryAdapter.query(
        'SELECT * FROM conversations WHERE circle_id = ?1 AND type = ?2',
        mapper: (Map<String, Object?> row) => DbConversation(
            id: row['id'] as String,
            serialisedType: row['type'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            lastActiveAt: row['last_active_at'] as int,
            isPrimary: (row['is_primary'] as int) != 0,
            isMuted: (row['is_muted'] as int) != 0,
            status: row['status'] as String,
            otherParticipant: row['other_participant'] as String?,
            backingFieldAnalyticsParams: row['analytics_params'] as String?,
            permissions: row['permissions'] as String?,
            circleId: row['circle_id'] as String?,
            isParticipant: (row['is_participant'] as int) != 0),
        arguments: [circleId, type]);
  }

  @override
  Future<List<DbConversation>> getAllConversations() async {
    return _queryAdapter.queryList('SELECT * FROM conversations',
        mapper: (Map<String, Object?> row) => DbConversation(
            id: row['id'] as String,
            serialisedType: row['type'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            lastActiveAt: row['last_active_at'] as int,
            isPrimary: (row['is_primary'] as int) != 0,
            isMuted: (row['is_muted'] as int) != 0,
            status: row['status'] as String,
            otherParticipant: row['other_participant'] as String?,
            backingFieldAnalyticsParams: row['analytics_params'] as String?,
            permissions: row['permissions'] as String?,
            circleId: row['circle_id'] as String?,
            isParticipant: (row['is_participant'] as int) != 0));
  }

  @override
  Stream<List<ConversationView>> watchConversationViews(bool isPrimary) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM ConversationView WHERE is_primary = ?1 AND last_active_at > 0 ORDER BY lastMessageTime DESC',
        mapper: (Map<String, Object?> row) => ConversationView(
            id: row['id'] as String,
            createdAt: row['created_at'] as int,
            backingFieldUnreadCount: row['unreadCount'] as int?,
            lastMessageTime: row['lastMessageTime'] as int,
            lastMessageData: row['lastMessageData'] as String,
            lastMessageType: row['lastMessageType'] as String,
            lastMessageStatus: row['lastMessageStatus'] as String,
            otherParticipant: row['other_participant'] as String?,
            isPrimary: (row['is_primary'] as int) != 0,
            isMuted: (row['is_muted'] as int) != 0,
            type: row['type'] as String,
            backingFieldAnalyticsParams: row['analytics_params'] as String?,
            circleId: row['circle_id'] as String?,
            permissions: row['permissions'] as String),
        arguments: [isPrimary ? 1 : 0],
        queryableName: 'ConversationView',
        isView: true);
  }

  @override
  Future<List<ConversationView>> getLatestConversations(
    bool isPrimary,
    int limit,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM ConversationView WHERE is_primary = ?1 AND last_active_at > 0 ORDER BY lastMessageTime DESC LIMIT ?2',
        mapper: (Map<String, Object?> row) => ConversationView(id: row['id'] as String, createdAt: row['created_at'] as int, backingFieldUnreadCount: row['unreadCount'] as int?, lastMessageTime: row['lastMessageTime'] as int, lastMessageData: row['lastMessageData'] as String, lastMessageType: row['lastMessageType'] as String, lastMessageStatus: row['lastMessageStatus'] as String, otherParticipant: row['other_participant'] as String?, isPrimary: (row['is_primary'] as int) != 0, isMuted: (row['is_muted'] as int) != 0, type: row['type'] as String, backingFieldAnalyticsParams: row['analytics_params'] as String?, circleId: row['circle_id'] as String?, permissions: row['permissions'] as String),
        arguments: [isPrimary ? 1 : 0, limit]);
  }

  @override
  Future<DbConversation?> getConversation(String conversationId) async {
    return _queryAdapter.query('SELECT * FROM conversations WHERE id = ?1',
        mapper: (Map<String, Object?> row) => DbConversation(
            id: row['id'] as String,
            serialisedType: row['type'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            lastActiveAt: row['last_active_at'] as int,
            isPrimary: (row['is_primary'] as int) != 0,
            isMuted: (row['is_muted'] as int) != 0,
            status: row['status'] as String,
            otherParticipant: row['other_participant'] as String?,
            backingFieldAnalyticsParams: row['analytics_params'] as String?,
            permissions: row['permissions'] as String?,
            circleId: row['circle_id'] as String?,
            isParticipant: (row['is_participant'] as int) != 0),
        arguments: [conversationId]);
  }

  @override
  Stream<DbConversation?> watchConversationById(String conversationId) {
    return _queryAdapter.queryStream(
        'SELECT * FROM conversations WHERE id = ?1',
        mapper: (Map<String, Object?> row) => DbConversation(
            id: row['id'] as String,
            serialisedType: row['type'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            lastActiveAt: row['last_active_at'] as int,
            isPrimary: (row['is_primary'] as int) != 0,
            isMuted: (row['is_muted'] as int) != 0,
            status: row['status'] as String,
            otherParticipant: row['other_participant'] as String?,
            backingFieldAnalyticsParams: row['analytics_params'] as String?,
            permissions: row['permissions'] as String?,
            circleId: row['circle_id'] as String?,
            isParticipant: (row['is_participant'] as int) != 0),
        arguments: [conversationId],
        queryableName: 'conversations',
        isView: false);
  }

  @override
  Future<void> deleteAllConversations() async {
    await _queryAdapter.queryNoReturn('DELETE FROM conversations');
  }

  @override
  Future<void> deleteMessage(String messageId) async {
    await _queryAdapter.queryNoReturn('DELETE FROM messages WHERE id = ?1',
        arguments: [messageId]);
  }

  @override
  Future<void> updateMessageModifiedStatus(
    String messageId,
    String status,
    int updatedAt,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE messages SET modified_status = ?2, created_at = ?3, updated_at = ?3 WHERE id = ?1',
        arguments: [messageId, status, updatedAt]);
  }

  @override
  Future<void> updateMessageStatusAndCreatedAt(
    String messageId,
    String status,
    int updatedAt,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE messages SET status = ?2, created_at = ?3, updated_at = ?3 WHERE id = ?1',
        arguments: [messageId, status, updatedAt]);
  }

  @override
  Future<void> updateMessageStatus(
    String messageId,
    String status,
    int updatedAt,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE messages SET status = ?2, updated_at = ?3 WHERE id = ?1',
        arguments: [messageId, status, updatedAt]);
  }

  @override
  Future<void> markAsReadUntil(
    String conversationId,
    bool statusToBeReported,
    int until,
    int updatedAt,
    String loggedInUserId,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE messages SET status = \'READ\', updated_at = ?4, status_to_be_reported = ?2 WHERE conversation_id = ?1 AND (status = \'UNREAD\' OR status = \'READ_HIDDEN\') AND created_at <= ?3 AND type = \'NORMAL\' AND sender_id != ?5',
        arguments: [
          conversationId,
          statusToBeReported ? 1 : 0,
          until,
          updatedAt,
          loggedInUserId
        ]);
  }

  @override
  Future<void> markAsReadHiddenUntil(
    String conversationId,
    bool statusToBeReported,
    int until,
    int updatedAt,
    String loggedInUserId,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE messages SET status = \'READ_HIDDEN\', updated_at = ?4, status_to_be_reported = ?2 WHERE conversation_id = ?1 AND status = \'UNREAD\' AND created_at <= ?3 AND type = \'NORMAL\' AND sender_id != ?5',
        arguments: [
          conversationId,
          statusToBeReported ? 1 : 0,
          until,
          updatedAt,
          loggedInUserId
        ]);
  }

  @override
  Future<DbMessage?> getMessage(String id) async {
    return _queryAdapter.query('SELECT * FROM messages WHERE id = ?1',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String),
        arguments: [id]);
  }

  @override
  Stream<List<DbMessage>> getLatestMessages(
    int limit,
    String conversationId,
  ) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM messages WHERE conversation_id = ?2 ORDER BY created_at DESC LIMIT ?1',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String),
        arguments: [limit, conversationId],
        queryableName: 'messages',
        isView: false);
  }

  @override
  Stream<List<DbMessage>> getMessagesNewerThan(
    int timestamp,
    int limit,
    String conversationId,
  ) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM messages WHERE conversation_id = ?3 AND created_at >= ?1 ORDER BY created_at ASC LIMIT ?2',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String),
        arguments: [timestamp, limit, conversationId],
        queryableName: 'messages',
        isView: false);
  }

  @override
  Stream<List<DbMessage>> getMessagesOlderThan(
    int timestamp,
    int limit,
    String conversationId,
  ) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM messages WHERE conversation_id = ?3 AND created_at <= ?1 ORDER BY created_at DESC LIMIT ?2',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String),
        arguments: [timestamp, limit, conversationId],
        queryableName: 'messages',
        isView: false);
  }

  @override
  Stream<List<DbMessage>> getOldestMessages(
    int limit,
    String conversationId,
  ) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM messages WHERE conversation_id = ?2 ORDER BY created_at DESC LIMIT ?1',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String),
        arguments: [limit, conversationId],
        queryableName: 'messages',
        isView: false);
  }

  @override
  Stream<List<DbMessage>> getMessagesBetween(
    int from,
    int to,
    String conversationId,
  ) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM messages WHERE conversation_id = ?3 AND created_at <= ?2 AND created_at >= ?1 ORDER BY created_at DESC',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String),
        arguments: [from, to, conversationId],
        queryableName: 'messages',
        isView: false);
  }

  @override
  Stream<List<DbMessage>> getMessagesToBeSent() {
    return _queryAdapter.queryListStream(
        'SELECT * FROM messages WHERE (status = \'TO_BE_SENT\' OR modified_status != \'NONE\') AND type = \'NORMAL\' ORDER BY created_at DESC',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String),
        queryableName: 'messages',
        isView: false);
  }

  @override
  Future<List<DbMessage>> getMessagesWaitingForAck() async {
    return _queryAdapter.queryList(
        'SELECT * FROM messages WHERE status = \'WAITING_FOR_ACK\' AND type = \'NORMAL\' ORDER BY created_at ASC',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String));
  }

  @override
  Stream<List<DbMessage>> getMessagesWithStatusToBeReported() {
    return _queryAdapter.queryListStream(
        'SELECT * FROM messages WHERE status_to_be_reported = 1 AND type = \'NORMAL\' ORDER BY created_at ASC',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String),
        queryableName: 'messages',
        isView: false);
  }

  @override
  Future<List<DbMessage>> getLatestReceivedMessage() async {
    return _queryAdapter.queryList(
        'SELECT * FROM messages ORDER BY received_at DESC LIMIT 1',
        mapper: (Map<String, Object?> row) => DbMessage(
            id: row['id'] as String,
            conversationId: row['conversation_id'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            receivedAt: row['received_at'] as int,
            status: row['status'] as String,
            statusToBeReported: (row['status_to_be_reported'] as int) != 0,
            type: row['type'] as String,
            senderId: row['sender_id'] as String?,
            messageData: row['message_data'] as String,
            modifiedStatus: row['modified_status'] as String));
  }

  @override
  Stream<List<ConversationView>> watchUnreadConversationsCount(bool isPrimary) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM ConversationView WHERE unreadCount > 0 AND is_primary = ?1',
        mapper: (Map<String, Object?> row) => ConversationView(
            id: row['id'] as String,
            createdAt: row['created_at'] as int,
            backingFieldUnreadCount: row['unreadCount'] as int?,
            lastMessageTime: row['lastMessageTime'] as int,
            lastMessageData: row['lastMessageData'] as String,
            lastMessageType: row['lastMessageType'] as String,
            lastMessageStatus: row['lastMessageStatus'] as String,
            otherParticipant: row['other_participant'] as String?,
            isPrimary: (row['is_primary'] as int) != 0,
            isMuted: (row['is_muted'] as int) != 0,
            type: row['type'] as String,
            backingFieldAnalyticsParams: row['analytics_params'] as String?,
            circleId: row['circle_id'] as String?,
            permissions: row['permissions'] as String),
        arguments: [isPrimary ? 1 : 0],
        queryableName: 'ConversationView',
        isView: true);
  }

  @override
  Future<void> deleteAllMessages() async {
    await _queryAdapter.queryNoReturn('DELETE FROM messages');
  }

  @override
  Future<List<DbMessage>> getLatestMessagesWith(
    String userId,
    int limit,
  ) async {
    return _queryAdapter.queryList(
        'SELECT messages.* FROM conversations     INNER JOIN messages     ON conversations.id = messages.conversation_id     AND conversations.other_participant = ?1     AND messages.type = \'NORMAL\'     ORDER BY created_at DESC     LIMIT ?2',
        mapper: (Map<String, Object?> row) => DbMessage(id: row['id'] as String, conversationId: row['conversation_id'] as String, createdAt: row['created_at'] as int, updatedAt: row['updated_at'] as int, receivedAt: row['received_at'] as int, status: row['status'] as String, statusToBeReported: (row['status_to_be_reported'] as int) != 0, type: row['type'] as String, senderId: row['sender_id'] as String?, messageData: row['message_data'] as String, modifiedStatus: row['modified_status'] as String),
        arguments: [userId, limit]);
  }

  @override
  Future<List<DbConversation>> getCircleConversations() async {
    return _queryAdapter.queryList(
        'SELECT * FROM conversations WHERE circle_id IS NOT NULL',
        mapper: (Map<String, Object?> row) => DbConversation(
            id: row['id'] as String,
            serialisedType: row['type'] as String,
            createdAt: row['created_at'] as int,
            updatedAt: row['updated_at'] as int,
            lastActiveAt: row['last_active_at'] as int,
            isPrimary: (row['is_primary'] as int) != 0,
            isMuted: (row['is_muted'] as int) != 0,
            status: row['status'] as String,
            otherParticipant: row['other_participant'] as String?,
            backingFieldAnalyticsParams: row['analytics_params'] as String?,
            permissions: row['permissions'] as String?,
            circleId: row['circle_id'] as String?,
            isParticipant: (row['is_participant'] as int) != 0));
  }

  @override
  Future<void> insertConversation(DbConversation conversation) async {
    await _dbConversationInsertionAdapter.insert(
        conversation, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertConversations(List<DbConversation> conversations) async {
    await _dbConversationInsertionAdapter.insertList(
        conversations, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertConversationIfNotExists(
      DbConversation conversation) async {
    await _dbConversationInsertionAdapter.insert(
        conversation, OnConflictStrategy.ignore);
  }

  @override
  Future<void> insertMessage(DbMessage message) async {
    await _dbMessageInsertionAdapter.insert(
        message, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertMessages(List<DbMessage> messages) async {
    await _dbMessageInsertionAdapter.insertList(
        messages, OnConflictStrategy.replace);
  }

  @override
  Future<void> updateConversation(DbConversation conversation) async {
    await _dbConversationUpdateAdapter.update(
        conversation, OnConflictStrategy.replace);
  }
}
