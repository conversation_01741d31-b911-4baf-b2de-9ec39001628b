import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/livedata.dart';
import 'package:jetpack/viewmodel.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/models/user_identity.dart';
import 'package:praja/services/user/user_service.dart';
import 'package:praja_posters/praja_posters.dart';

class TestingPage extends BasePage {
  @override
  String get pageName => 'testing';

  const TestingPage({Key? key}) : super(key: key);

  @override
  Widget buildContent(BuildContext context) {
    final viewModel = context.viewModel;
    return Scaffold(
        appBar: AppBar(
          title: const Text('Testing Page'),
        ),
        body: LiveDataBuilder<Map<int, UserIdentity>>(
            liveData: viewModel.users,
            builder: (ctx, users) => const Padding(
                padding: EdgeInsets.all(16),
                child: PremiumFrameIdentityDemo())));
  }
}

@injectable
class TestingPageViewModel extends ViewModel {
  final UserService userService;

  final _users = MutableLiveData<Map<int, UserIdentity>>({});
  LiveData<Map<int, UserIdentity>> get users => _users;

  TestingPageViewModel(this.userService) {
    fetch();
  }

  Future<void> fetch() async {
    _users.value =
        await userService.getIdentities([454, 841341, 133280, 379615, 22759]);
  }
}

extension on BuildContext {
  TestingPageViewModel get viewModel => getViewModel<TestingPageViewModel>();
}
