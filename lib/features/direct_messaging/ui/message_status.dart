import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:praja/features/direct_messaging/database/entities/db_message.dart';
import 'package:praja/features/direct_messaging/models/ui_message.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/styles.dart';

class MessageStatusUI extends StatelessWidget {
  final DateTime timestamp;
  final MessageStatus status;
  final bool showStatus;

  const MessageStatusUI({
    super.key,
    required this.status,
    required this.timestamp,
    required this.showStatus,
  });

  factory MessageStatusUI.fromUIMessage(UINormalMessage message) {
    return MessageStatusUI(
      status: message.status,
      timestamp: message.timestamp,
      showStatus: message.isSender && !message.isDeleted,
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    return DateFormat.jm().format(timestamp);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(bottom: 2),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              _formatTimestamp(timestamp),
              style: const TextStyle(
                fontSize: 10,
                color: Styles.dimTextColor,
              ),
            ),
            const SizedBox(width: 4),
            if (showStatus) MessageStatusIcon(status: status, size: 14),
          ],
        ));
  }
}

class MessageStatusIcon extends StatelessWidget {
  final MessageStatus status;
  final double size;

  const MessageStatusIcon({
    Key? key,
    required this.status,
    this.size = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    switch (status) {
      case MessageStatus.undelivered:
      case MessageStatus.sent:
        return Icon(
          PrajaIcons.circle_tick,
          size: size,
          color: Styles.dimIconColor,
        );
      case MessageStatus.read:
        return Icon(
          PrajaIcons.doubletick,
          size: size,
          color: Styles.circleIndigo,
        );
      case MessageStatus.readHidden:
      case MessageStatus.delivered:
        return Icon(
          PrajaIcons.doubletick,
          size: size,
          color: Styles.dimIconColor,
        );
      case MessageStatus.failed:
        return Icon(
          Icons.error_outline,
          size: size,
          color: Theme.of(context).colorScheme.error,
        );
      case MessageStatus.toBeSent:
      case MessageStatus.waitingForAck:
        return Icon(
          Icons.schedule,
          size: size,
          color: Styles.dimIconColor,
        );
      default:
        return SizedBox(
          width: size,
          height: size,
        );
    }
  }
}
