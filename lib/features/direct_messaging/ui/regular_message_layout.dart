import 'package:flutter/material.dart';
import 'package:praja/features/direct_messaging/database/entities/db_conversation.dart';
import 'package:praja/features/direct_messaging/models/ui_message.dart';
import 'package:praja/features/direct_messaging/ui/message_content_ui.dart';
import 'package:praja/features/direct_messaging/ui/message_status.dart';
import 'package:praja/features/direct_messaging/ui/message_ui.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/presentation/user_avatar.dart';

import 'chat_ui_constants.dart';
import 'swipe_reveal.dart';

class RegularMessageLayout extends StatelessWidget {
  final UINormalMessage message;
  final bool showAvatar;
  final bool showFooter;
  final VoidCallback? onReveal;
  final VoidCallback? onLongPress;
  final VoidCallback? onForward;
  final MessageContentCallbacks? callbacks;

  const RegularMessageLayout({
    super.key,
    required this.message,
    this.showAvatar = true,
    this.showFooter = true,
    this.onReveal,
    this.onLongPress,
    this.onForward,
    this.callbacks,
  });

  /// Message content with an optional footer beneath
  Widget _buildContent(BuildContext context, UINormalMessage message,
      {bool showTail = true}) {
    return MessageContentUI(
      message: message,
      showBubbleTail: showTail,
      callbacks: callbacks,
    );
  }

  @override
  Widget build(BuildContext context) {
    final hasSpecialAttachments = message.hasSpecialAttachments;
    final showForwardIcon = hasSpecialAttachments && !message.stillToBeSent;
    final avatar = showAvatar
        ? UserAvatar.forUserId(context, message.senderIdAsInt, size: 36)
        : const SizedBox(width: 36);
    Widget? footer = showFooter ? MessageStatusUI.fromUIMessage(message) : null;
    if (footer != null && message.isSender) {
      footer = Padding(
          padding:
              const EdgeInsets.only(right: ChatUiConstants.bubbleTailWidth),
          child: footer);
    }

    Widget child = Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
        child: Row(
            key: key,
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!message.isSender &&
                  message.conversation.type != ConversationType.oneToOne) ...[
                const SizedBox(width: 4),
                avatar,
              ],
              Expanded(
                  child: GestureDetector(
                      onLongPress: onLongPress,
                      child: Align(
                        alignment: message.isSender
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (message.isSender && showForwardIcon)
                              IconButton(
                                  alignment: Alignment.centerRight,
                                  icon: const Icon(
                                      PrajaIcons.circle_forward_attachment,
                                      color: Color(0xFFA4A9AB)),
                                  onPressed: () {
                                    onForward?.call();
                                  }),
                            SizedBox(
                                width: MessageUI.bubbleWidth(context),
                                child: Align(
                                    alignment: message.isSender
                                        ? Alignment.centerRight
                                        : Alignment.centerLeft,
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          _buildContent(context, message,
                                              showTail: showAvatar),
                                          if (footer != null)
                                            const SizedBox(height: 2),
                                          if (footer != null) footer,
                                        ]))),
                            if (!message.isSender && showForwardIcon)
                              IconButton(
                                  icon: const Icon(
                                      PrajaIcons.circle_forward_attachment,
                                      color: Color(0xFFA4A9AB)),
                                  onPressed: onForward),
                          ],
                        ),
                      )))
            ]));
    return message.isDeleted
        ? child
        : SwipeReveal(
            onReveal: onReveal,
            reveal: PreferredSize(
                preferredSize: const Size.fromWidth(56),
                child: Padding(
                  padding: EdgeInsets.only(
                      left: 16, right: 16, bottom: showFooter ? 16 : 8),
                  child: Icon(PrajaIcons.replyto_message,
                      color: Colors.grey.shade600, size: 24),
                )),
            child: child);
  }
}
