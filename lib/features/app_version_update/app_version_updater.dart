import 'dart:io';

import 'package:flutter/material.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:praja/constants/praja_constants.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:url_launcher/url_launcher.dart';

class AppVersionUpdater {
  static Future<AppVersionUpdateResult> updateIfNeeded(
      BuildContext context, Map<String, dynamic> config) async {
    final platform = AppUpdatePlatform();
    if (config["force_update"] == true) {
      return await platform.force(context);
    } else {
      return await platform.nudge(context);
    }
  }

  static Future<void> completeUpdate() async {
    await AppUpdatePlatform().completeUpdate();
  }
}

enum AppVersionUpdateResult {
  forceUpdated,
  nudgeAccepted,
  nudgeDenied,
  nudgeFailed,
  noNudge,
  noUpdate,
  error;

  String asMode() {
    switch (this) {
      case AppVersionUpdateResult.forceUpdated:
        return "force_update";
      case AppVersionUpdateResult.nudgeAccepted:
        return "nudge_accepted";
      case AppVersionUpdateResult.nudgeDenied:
        return "nudge_denied";
      case AppVersionUpdateResult.nudgeFailed:
        return "nudge_failed";
      case AppVersionUpdateResult.noNudge:
        return "no_nudge";
      case AppVersionUpdateResult.noUpdate:
        return "no_update";
      case AppVersionUpdateResult.error:
        return "error";
    }
  }
}

abstract class AppUpdatePlatform {
  Future<AppVersionUpdateResult> nudge(BuildContext context);
  Future<AppVersionUpdateResult> force(BuildContext context);
  Future<void> openFullScreenUpdateUI(BuildContext context);
  Future<void> completeUpdate();

  factory AppUpdatePlatform() {
    if (Platform.isAndroid) {
      return AppVersionUpdaterAndroid();
    } else {
      return AppVersionUpdaterIOS();
    }
  }
}

class AppVersionUpdaterIOS implements AppUpdatePlatform {
  final appStoreLink = Uri.parse(
      "https://apps.apple.com/us/app/praja-app/id${PRAJAConstants.appleStoreId}");
  @override
  Future<AppVersionUpdateResult> force(BuildContext context) async {
    if (context.mounted) {
      _showForceUpdateDialog(context, appStoreLink);
      return AppVersionUpdateResult.forceUpdated;
    }

    return AppVersionUpdateResult.error;
  }

  @override
  Future<void> openFullScreenUpdateUI(BuildContext context) async {
    await launchUrl(appStoreLink, mode: LaunchMode.externalApplication);
  }

  @override
  Future<AppVersionUpdateResult> nudge(BuildContext context) async {
    return AppVersionUpdateResult.noNudge;
  }

  @override
  Future<void> completeUpdate() async {
    // do nothing
  }
}

class AppVersionUpdaterAndroid implements AppUpdatePlatform {
  @override
  Future<AppVersionUpdateResult> force(BuildContext context,
      {bool skipPopup = false}) async {
    try {
      AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.immediateUpdateAllowed) {
        final playServicesResult = await InAppUpdate.performImmediateUpdate();
        switch (playServicesResult) {
          case AppUpdateResult.success:
            return AppVersionUpdateResult.forceUpdated;
          default:
          // do nothing
        }
      }
    } catch (e, stackTrace) {
      logNonFatal("Error while trying in app force update in Android", e,
          stackTrace: stackTrace);
    }

    String packageId = (await PackageInfo.fromPlatform()).packageName;
    final playStoreLink =
        Uri.parse("https://play.google.com/store/apps/details?id=$packageId");
    if (skipPopup) {
      launchUrl(playStoreLink, mode: LaunchMode.externalApplication);
    } else if (context.mounted) {
      _showForceUpdateDialog(context, playStoreLink);
      return AppVersionUpdateResult.forceUpdated;
    }

    return AppVersionUpdateResult.error;
  }

  @override
  Future<void> openFullScreenUpdateUI(BuildContext context) async {
    await force(context, skipPopup: true);
  }

  @override
  Future<AppVersionUpdateResult> nudge(BuildContext context) async {
    final bool isEmulator = await Utils.isAppRunningOnEmulator();
    if (isEmulator) {
      return AppVersionUpdateResult.noUpdate;
    }
    try {
      AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.flexibleUpdateAllowed) {
        final playServicesResult = await InAppUpdate.startFlexibleUpdate();
        switch (playServicesResult) {
          case AppUpdateResult.success:
            return AppVersionUpdateResult.nudgeAccepted;
          case AppUpdateResult.inAppUpdateFailed:
            return AppVersionUpdateResult.nudgeFailed;
          case AppUpdateResult.userDeniedUpdate:
            return AppVersionUpdateResult.nudgeDenied;
        }
      }
    } catch (e, stackTrace) {
      logNonFatal("Error while trying in app update in Android", e,
          stackTrace: stackTrace);
      return AppVersionUpdateResult.error;
    }

    return AppVersionUpdateResult.noUpdate;
  }

  @override
  Future<void> completeUpdate() async {
    try {
      await InAppUpdate.completeFlexibleUpdate();
    } catch (e, stackTrace) {
      logNonFatal("Error while trying to complete update in Android", e,
          stackTrace: stackTrace);
    }
  }
}

Future<void> _showForceUpdateDialog(
    BuildContext context, Uri appStoreLink) async {
  await showDialog<String>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      String title = context.getString(StringKey.appUpdateTitle);
      String message = context.getString(StringKey.appUpdateMessage);
      String btnLabel = context.getString(StringKey.appUpdateButtonLabel);
      return PopScope(
        canPop: false,
        child: AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Colors.black),
              child: Text(btnLabel),
              onPressed: () {
                AppAnalytics.logEvent(name: "clicked_update", parameters: {
                  "source": "force_update_dialog",
                });
                launchUrl(appStoreLink, mode: LaunchMode.externalApplication);
              },
            ),
          ],
        ),
      );
    },
  );
}
