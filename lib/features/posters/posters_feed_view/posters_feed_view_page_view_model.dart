import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/rendering.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/app_event_bus.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/exceptions/api_exception.dart';
import 'package:praja/features/impression_tracker/view_model/impression_tracker.dart';
import 'package:praja/features/posters/models/poster_carousel.dart';
import 'package:praja/features/posters/models/full_page_feed_error_feed_item.dart';
import 'package:praja/features/posters/models/posters_feed_view_carousel.dart';
import 'package:praja/features/posters/models/posters_feed_view_filters_response.dart';
import 'package:praja/features/posters/models/full_page_feed_shimmer_feed_item.dart';
import 'package:praja/features/posters/services/poster_carousel_config.dart';
import 'package:praja/features/posters/services/poster_service.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/feed_item_abstract_class.dart';
import 'package:praja/services/hive_module.dart';
import 'package:praja/services/prefs/hive_prefs.dart';
import 'package:praja/test/mocks/video_poster_carousel_mocks.dart';
import 'package:praja/utils/logger.dart';

@injectable
class PostersFeedViewViewModel extends ViewModel {
  final PosterService _posterService;
  final HivePrefs hivePrefs;
  final AppEventBus _appEventBus;
  final PosterCarouselConfig _posterCarouselConfig;

  PostersFeedViewViewModel(
    this._posterService,
    @Named(appPrefsConstant) this.hivePrefs,
    this._appEventBus,
    this._posterCarouselConfig,
  );

  final MutableLiveData<PostersFeedViewState> _filtersState =
      MutableLiveData(FiltersLoadingState());
  LiveData<PostersFeedViewState> get filtersState => _filtersState;

  final MutableLiveData<PostersFeedViewState> _state =
      MutableLiveData(PostersFeedViewLoadingState());
  LiveData<PostersFeedViewState> get state => _state;

  final PageController _pageController = PageController();
  get pageController => _pageController;

  final MutableLiveData<List<PosterCarousel>> _posterCarousel =
      MutableLiveData([]);
  LiveData<List<PosterCarousel>> get posterCarousel => _posterCarousel;

  final MutableLiveData<int> _selectedFilterIndex = MutableLiveData(0);
  LiveData<int> get selectedFilterIndex => _selectedFilterIndex;

  final MutableLiveData<bool> _showFiltersWithLowOpacity =
      MutableLiveData(false);
  LiveData<bool> get showFiltersWithLowOpacity => _showFiltersWithLowOpacity;

  /// This variable is used to disable scroll in Posters feed
  /// Use case: While video poster generating/downloading or sharing
  /// we will disable scroll to avoid any issues
  final MutableLiveData<bool> _isScrollDisabled = MutableLiveData(false);
  LiveData<bool> get isScrollDisabled => _isScrollDisabled;

  Map<String, dynamic> _filterData = {};

  final Map<int, List<FeedItem>> _feedItemsPerFilter = {};
  final Map<String, double> _scrolledPagePerFilter = {};

  Map<String, dynamic> _postersFeedAnalyticsParams = {};
  late String source;

  bool isFeedFetching = false;
  bool hasFeedFetchError = false;

  static bool _reachedLastPage = false;
  static const int _perPageCount = 3;
  int _offset = 0;

  late ImpressionTracker _impressionTracker;

  Map<String, dynamic> _notificationParams = const {};

  CancelToken _cancelToken = CancelToken();

  StreamSubscription<AppEvent>? _appEventSubscription;

  bool get isInFirstPage =>
      _pageController.hasClients &&
      _pageController.page != null &&
      _pageController.page! == 0;

  bool isInitialised = false;

  Future<void> init(
      {Map<String, dynamic> notificationParams = const {},
      required ImpressionTracker impressionTracker,
      required String source}) async {
    if (isInitialised) return;
    isInitialised = true;
    _impressionTracker = impressionTracker;
    _notificationParams = notificationParams;
    this.source = source;
    _offset = 0;
    _reachedLastPage = false;
    _filterData.clear();
    _fetchFiltersData();
    await _fetchPostersFeedData();
    _listenToAppEvents();
    _setPosterSwipeBool();
  }

  void _listenToAppEvents() {
    _appEventSubscription?.cancel();
    _appEventSubscription = _appEventBus.stream.listen((event) {
      if (event is PostersFeedInvalidatedEvent) {
        _offset = 0;
        _reachedLastPage = false;
        hasFeedFetchError = false;
        isFeedFetching = false;
        _filterData.clear();
        _cancelToken.cancel();
        _state.value = PostersFeedViewLoadingState();
        _fetchFiltersData();
        _fetchPostersFeedData();
      }
    });
  }

  Future<void> onUpdatePostersFeedEventTriggered(
      {required Map<String, dynamic> params}) async {
    if (_notificationParams != params) {
      _notificationParams = params;
      _offset = 0;
      _reachedLastPage = false;
      _filterData.clear();
      _cancelToken.cancel();
      _state.value = PostersFeedViewLoadingState();
      _fetchFiltersData();
      await _impressionTracker.onInvisible();
      _fetchPostersFeedData();
    }
  }

  void _fetchFiltersData() async {
    _filtersState.value = FiltersLoadingState();
    try {
      final filters = await _posterService.fetchPosterFeedFilters(
          params: _notificationParams);
      final selectedFilter =
          filters.filters.indexWhere((element) => element.selected == true);
      _selectedFilterIndex.value = selectedFilter == -1 ? 0 : selectedFilter;
      _filtersState.value =
          FiltersSuccessState(postersFeedViewFilters: filters);
    } catch (e) {
      _filtersState.value = FiltersErrorState(localisedErrorMessage(e));
    }
  }

  void _logFetchedFeed({
    required bool isRefresh,
    required int itemsCount,
    Map<String, dynamic> currentFetchParams = const {},
    bool isLastPage = false,
  }) {
    AppAnalytics.onPostersFeedFetched(
      source: source,
      itemsCount: itemsCount,
      pageNumber: (_offset / _perPageCount + 1).toInt(),
      isRefresh: isRefresh,
      params: currentFetchParams,
      isLastPage: isLastPage,
    );
  }

  // we will clear the review status of protocol if exists,
  // while fetching new poster carousels
  void _clearPosterLayoutFeedbackRequestStatus(
      {required List<FeedItem> feedItems}) {
    for (final item in feedItems) {
      if (item is PostersFeedViewCarousel) {
        for (final layout in item.layouts) {
          if (layout.layoutFeedbackRequest != null) {
            _posterCarouselConfig
                .clearProtocolIdFromCompletedProtocols(layout.protocolId);
          }
        }
      }
    }
  }

  List<VideoPosterCarousel> _createMockVideoPosterCarousels() {
    return [
      getMockVideoPosterCarousel(),
      getMockVideoPosterCarouselSquare(),
      getMockVideoPosterCarouselLandscape(),
    ];
  }

  Future<void> _fetchPostersFeedData({bool isRefresh = false}) async {
    if (isFeedFetching) return;
    isFeedFetching = true;

    hasFeedFetchError = false;

    _cancelToken = CancelToken();
    final requestParams = {..._filterData, ..._notificationParams};

    try {
      final postersFeedViewResponse = await _posterService.fetchPostersFeed(
        offset: _offset,
        count: _perPageCount,
        loadedFeedItemIds: _state.value.getLoadedFeedItemIds(),
        cancelToken: _cancelToken,
        filterData: requestParams,
      );
      final fetchedFeedItems = postersFeedViewResponse.feedItems;
      _clearPosterLayoutFeedbackRequestStatus(feedItems: fetchedFeedItems);
      _postersFeedAnalyticsParams =
          postersFeedViewResponse.analyticsParams ?? {};
      if (fetchedFeedItems.isEmpty) {
        _reachedLastPage = true;
      }
      List<FeedItem> filteredFeedItems = _getFilteredFeedItems();

      if (fetchedFeedItems.isNotEmpty || !_reachedLastPage) {
        filteredFeedItems.addAll(fetchedFeedItems);

        // Append mock video poster carousels at the end of every page
        final mockVideoPosterCarousels = _createMockVideoPosterCarousels();
        filteredFeedItems.addAll(mockVideoPosterCarousels);

        if (!_reachedLastPage) {
          filteredFeedItems.add(FullPageFeedShimmerFeedItem());
        }
      }
      _logFetchedFeed(
        isRefresh: isRefresh,
        itemsCount: fetchedFeedItems.length,
        currentFetchParams: {
          ...postersFeedViewResponse.analyticsParams ?? {},
          ...requestParams
        },
        isLastPage: _reachedLastPage,
      );
      _offset += _perPageCount;
      _updateStateWithLatestFeedItems(filteredFeedItems);
    } catch (e, stackTrace) {
      logNonFatalIfAppError("error while fetching feed", e,
          stackTrace: stackTrace);
      if (e is ApiException &&
          e.failureType == ApiFailureType.requestCancelled) {
        return;
      }

      AppAnalytics.onPostersFeedFailed(
        source: source,
        pageNumber: (_offset / _perPageCount + 1).toInt(),
        errorMessage: e.toString(),
        params: requestParams,
      );

      List<FeedItem> filteredFeedItems = _getFilteredFeedItems()
        ..add(FullPageFeedErrorFeedItem(error: e));

      _updateStateWithLatestFeedItems(filteredFeedItems);
      hasFeedFetchError = true;
    } finally {
      isFeedFetching = false;
      _notificationParams = {};
    }
  }

  double _getCurrentPageWherePagingControllerIs() {
    if (_pageController.hasClients && _pageController.positions.isNotEmpty) {
      return _pageController.page ?? 0;
    }
    return 0;
  }

  void _setFiltersFeedData() {
    final List<FeedItem> feedItems = _getFilteredFeedItems();
    if (feedItems.isNotEmpty) {
      _feedItemsPerFilter[_selectedFilterIndex.value] = feedItems;
      _scrolledPagePerFilter[_selectedFilterIndex.value.toString()] =
          _getCurrentPageWherePagingControllerIs();
    }
  }

  Future<void> onFilterSelected(
    int index, {
    required Map<String, dynamic> filterData,
    Map<String, dynamic>? analyticsParams,
  }) async {
    final bool isSameIndex = _selectedFilterIndex.value == index;
    _setFiltersFeedData();
    if (isSameIndex && !isInFirstPage) {
      // Animate to Top
      if (_pageController.hasClients) {
        _pageController.animateToPage(0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut);
      }
      AppAnalytics.onPostersFeedFilterSelected(
        source: source,
        params: {...filterData, ...analyticsParams ?? {}},
        isScrollingToTop: true,
      );
      return;
    }

    setFilterData(filterData);
    if (!isSameIndex) _selectedFilterIndex.value = index;
    AppAnalytics.onPostersFeedFilterSelected(
      source: source,
      params: {...filterData, ...analyticsParams ?? {}},
    );
    isFeedFetching = false;
    hasFeedFetchError = false;
    _reachedLastPage = false;
    _showFiltersWithLowOpacity.value = false;

    final existingFeedItems = _feedItemsPerFilter[_selectedFilterIndex.value];
    if (existingFeedItems != null &&
        existingFeedItems.isNotEmpty &&
        (!isSameIndex)) {
      _updateStateFeedItemsWithFilterFeedItems(_selectedFilterIndex.value);
      if (_pageController.hasClients && _pageController.page != null) {
        _pageController
            .jumpToPage(_scrolledPagePerFilter[index.toString()]?.toInt() ?? 0);
      }
      return;
    }
    _cancelToken.cancel();
    _state.value = PostersFeedViewLoadingState();
    _offset = 0;
    await _impressionTracker.onInvisible();
    _fetchPostersFeedData();
  }

  void _updateStateFeedItemsWithFilterFeedItems(int index) {
    final feedItems = _feedItemsPerFilter[index] ?? [];
    _updateStateWithLatestFeedItems(feedItems);
  }

  Future<void> _setPosterSwipeBool() async {
    final currentState = _state.value;
    if (currentState is! PostersFeedViewSuccessState) return;
    if (currentState.feedItems.isNotEmpty) {
      final firstItem = currentState.feedItems.first;
      final bool notEligibleForSwipeTutorial =
          firstItem is PostersFeedViewCarousel &&
              firstItem.layouts.isNotEmpty &&
              firstItem.layouts[0].layoutFeedbackRequest != null;

      // We won't show swipe tutorial for the users who are having layout feedback request
      // Because we don't want to interrupt the layout feedback request animation
      if (notEligibleForSwipeTutorial) return;
    }
    final isVerticalSwipeBool = await _getVerticalSwipeBool();
    await Future.delayed(const Duration(milliseconds: 1000));
    if (!isVerticalSwipeBool) {
      showVerticalSwipeHint();
    }
  }

  Future<bool> showHelpTutorial() async {
    return await hivePrefs.getBool("show_posters_feed_help_button_tutorial",
        defaultValue: true);
  }

  void onHelpTutorialCompleted() {
    hivePrefs.putBool("show_posters_feed_help_button_tutorial", false);
  }

  // Posters Feed Vertical Swipe
  Future<bool> didUserLearnedVerticalSwipe() async {
    return await hivePrefs.getBool("user_learned_posters_feed_vertical_swipe");
  }

  Future<bool> _getVerticalSwipeBool() async {
    return await hivePrefs.getBool("user_learned_posters_feed_vertical_swipe",
        defaultValue: false);
  }

  void showVerticalSwipeHint() {
    if (_pageController.hasClients && _pageController.page == 0) {
      final double halfPageOffset =
          _pageController.position.viewportDimension / 2;

      _pageController.animateTo(
        halfPageOffset,
        duration: const Duration(milliseconds: 1000),
        curve: Curves.easeOutQuad,
      );

      Future.delayed(const Duration(milliseconds: 1000), () {
        if (_pageController.hasClients) {
          _pageController.animateToPage(
            0,
            duration: const Duration(milliseconds: 1000),
            curve: Curves.easeOutQuad,
          );
        }
      });
    }
    hivePrefs.putBool("user_learned_posters_feed_vertical_swipe", true);
  }

  void _onUserScroll() {
    final isScrollingToNextPage =
        _pageController.position.userScrollDirection == ScrollDirection.reverse;
    final isScrollingToPreviousPage =
        _pageController.position.userScrollDirection == ScrollDirection.forward;

    if (_pageController.page != null && _pageController.page! < 1) {
      _showFiltersWithLowOpacity.value = false;
    } else if (isScrollingToNextPage &&
        _showFiltersWithLowOpacity.value == false) {
      _showFiltersWithLowOpacity.value = true;
    } else if (isScrollingToPreviousPage &&
        _showFiltersWithLowOpacity.value == true) {
      _showFiltersWithLowOpacity.value = false;
    }
  }

  void _pagingListener() {
    _onUserScroll();
    if (isFeedFetching || hasFeedFetchError || _reachedLastPage) return;
    if (_pageController.isReachingEnd) {
      _fetchNextPage();
    }
  }

  void listenToPageController() {
    _pageController.addListener(_pagingListener);
  }

  void _fetchNextPage() {
    _fetchPostersFeedData();
  }

  Future<void> onRefresh() async {
    _offset = 0;
    _cancelToken.cancel();
    _reachedLastPage = false;
    _feedItemsPerFilter[_selectedFilterIndex.value] = [];
    await _impressionTracker.onInvisible();
    _state.value = PostersFeedViewLoadingState();
    _fetchPostersFeedData(isRefresh: true);
  }

  void setFilterData(Map<String, dynamic> data) {
    _filterData = data;
  }

  void onShowFilters() {
    // If scroll is disabled, we don't want to show filters
    if (_isScrollDisabled.value) return;
    _showFiltersWithLowOpacity.value = false;
    AppAnalytics.onFiltersEmptyAreaTapped(source: source);
  }

  void onPostersFeedErrorTryAgain(Object? error) {
    List<FeedItem> filteredFeedItems = _getFilteredFeedItems()
      ..add(FullPageFeedShimmerFeedItem());
    _updateStateWithLatestFeedItems(filteredFeedItems);
    hasFeedFetchError = false;
    _fetchPostersFeedData();
  }

  void onFiltersErrorTryAgain() {
    _fetchFiltersData();
  }

  void _updateStateWithLatestFeedItems(List<FeedItem> feedItems) {
    final currentState = _state.value;
    if (currentState is PostersFeedViewSuccessState) {
      _state.value = currentState.copyWith(feedItems: feedItems);
    } else {
      _state.value = PostersFeedViewSuccessState(feedItems: feedItems);
    }
  }

  List<FeedItem> _getFilteredFeedItems() {
    final state = _state.value;
    if (state is! PostersFeedViewSuccessState) return [];
    final feedItems = state.feedItems;
    return feedItems
        .where((element) =>
            element is! FullPageFeedShimmerFeedItem &&
            element is! FullPageFeedErrorFeedItem)
        .toList();
  }

  Map<String, dynamic> getAnalyticsParams() {
    final Map<String, dynamic> params = {};
    final filtersCurrentState = _filtersState.value;
    final feedCurrentState = _state.value;
    if (filtersCurrentState is FiltersSuccessState) {
      params.addAll(
          filtersCurrentState.postersFeedViewFilters.analyticsParams ?? {});
    }
    if (feedCurrentState is PostersFeedViewSuccessState) {
      params.addAll(_postersFeedAnalyticsParams);
    }
    params['source'] = source;
    return params;
  }

  void onVideoPosterOperationStarted() {
    _isScrollDisabled.value = true;
  }

  void onVideoPosterOperationEnded() {
    _isScrollDisabled.value = false;
  }

  @override
  void onDispose() {
    _pageController.removeListener(_pagingListener);
    _appEventSubscription?.cancel();
    _appEventSubscription = null;
    _pageController.dispose();
    _cancelToken.cancel();
    super.onDispose();
  }
}

abstract class PostersFeedViewState {
  List<String> getLoadedFeedItemIds() => [];
}

class FiltersLoadingState extends PostersFeedViewState {}

class FiltersSuccessState extends PostersFeedViewState {
  final PostersFeedViewFiltersResponse postersFeedViewFilters;

  FiltersSuccessState({required this.postersFeedViewFilters});
}

class FiltersErrorState extends PostersFeedViewState {
  final String errorMessage;

  FiltersErrorState(this.errorMessage);
}

class PostersFeedViewLoadingState extends PostersFeedViewState {}

class PostersFeedViewSuccessState extends PostersFeedViewState {
  final List<FeedItem> feedItems;

  PostersFeedViewSuccessState({required this.feedItems});

  @override
  List<String> getLoadedFeedItemIds() {
    List<String> feedItemIds = [];
    final filteredFeedItems = feedItems
        .where((element) =>
            element is! FullPageFeedShimmerFeedItem &&
            element is! FullPageFeedErrorFeedItem)
        .toList();
    for (var item in filteredFeedItems) {
      if (item.feedItemId != null) {
        feedItemIds.add(item.feedItemId.toString());
      }
    }
    return feedItemIds;
  }

  PostersFeedViewSuccessState copyWith({
    List<FeedItem>? feedItems,
  }) {
    return PostersFeedViewSuccessState(feedItems: feedItems ?? this.feedItems);
  }
}

extension on PageController {
  bool get atEdge =>
      page ==
      (positions.last.maxScrollExtent / positions.last.viewportDimension)
              .floor() -
          1;

  bool get isReachingEnd {
    if (position.userScrollDirection == ScrollDirection.forward) return false;
    if (atEdge) return true;
    return position.extentAfter < 2 * position.viewportDimension;
  }
}
