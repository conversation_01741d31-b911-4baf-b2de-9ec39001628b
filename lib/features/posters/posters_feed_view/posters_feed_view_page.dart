import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/common/full_page_feed/full_page_feed_item_builder.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/impression_tracker/view_model/impression_tracker.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/posters/models/posters_feed_view_filters_response.dart';
import 'package:praja/features/posters/posters_feed_view/posters_feed_config.dart';
import 'package:praja/features/posters/posters_feed_view/posters_feed_view_page_view_model.dart';
import 'package:praja/common/full_page_feed/full_page_feed_shimmer.dart';
import 'package:praja/features/posters/widgets/premium_help_button.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/nav_bar_view_model.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

const _filterItemHeight = 32.0;
const _filtersContainerHeight = _filterItemHeight + 20; // Add some padding

class PostersFeedViewPage extends BasePage {
  final EdgeInsets padding;
  final String source;

  const PostersFeedViewPage(
      {super.key,
      this.padding = const EdgeInsets.all(0),
      required this.source});

  @override
  String get pageName => "posters_feed_view";

  @override
  bool get pageLoadRequiredForTracking => true;

  @override
  Widget buildContent(BuildContext context) {
    return _PostersFeedViewPageInner(padding: padding, source: source);
  }
}

class _PostersFeedViewPageInner extends StatefulWidget {
  final EdgeInsets padding;
  final String source;

  const _PostersFeedViewPageInner(
      {required this.padding, required this.source});

  @override
  State<_PostersFeedViewPageInner> createState() =>
      _PostersFeedViewPageInnerState();
}

class _PostersFeedViewPageInnerState extends State<_PostersFeedViewPageInner>
    with TickerProviderStateMixin {
  late PostersFeedViewViewModel _viewModel;
  late NavViewModel _navViewModel;
  late PageViewTracker _pageViewTracker;
  late ImpressionTracker _impressionTracker;

  //For Help Spotlight Tutorial
  List<TargetFocus> targets = [];
  TutorialCoachMark? tutorial;
  late GlobalKey helpSpotlightKey;
  bool isTutorialAlreadyFinished = false;

  // For collapsible filters
  bool _isFiltersExpanded = false;
  Timer? _autoCollapseTimer;
  Timer? _filterSelectionDelayTimer;

  // Animation controllers for staggered filter reveal
  late AnimationController _filterAnimationController;
  late AnimationController _fadeOutAnimationController;
  final List<Animation<double>> _filterAnimations = [];
  final List<Animation<Offset>> _filterSlideAnimations = [];

  @override
  void initState() {
    super.initState();
    _navViewModel = context.globalNavViewModel;
    helpSpotlightKey = GlobalKey();
    _impressionTracker = context.impressionTracker();
    _viewModel = context.getViewModel<PostersFeedViewViewModel>()
      ..init(
        notificationParams: _navViewModel.postersFeedDeeplinkParams,
        impressionTracker: _impressionTracker,
        source: widget.source,
      );
    _viewModel.listenToPageController();
    _pageViewTracker = Provider.of<PageViewTracker>(context, listen: false);

    // Initialize animation controllers
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeOutAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeOutAnimationController.value = 1.0; // Start fully visible

    // Show the filters expanded by default and then hide them after 2 seconds
    _isFiltersExpanded = true;
    _startFilterSelectionDelayTimer();
  }

  initTargets() {
    if (!mounted) return;
    targets.add(
      TargetFocus(
        identify: "Help Spotlight",
        keyTarget: helpSpotlightKey,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  context.getString(StringKey.postersFeedHelpTutorialLabel,
                      listen: false),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 24.0,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: Text(
                    context.getString(StringKey.postersFeedHelpTutorialText,
                        listen: false),
                    style: const TextStyle(color: Colors.white, fontSize: 18.0),
                  ),
                ),
                const SizedBox(height: 50),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      width: 100,
                      height: 40,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white),
                      ),
                      child: InkWell(
                        onTap: () {
                          tutorial?.finish();
                          _finishTutorial();
                          _viewModel.onHelpTutorialCompleted();
                          AppAnalytics.onPosterHelpTutorialCompleted(
                            source: widget.source,
                            autoSkip: false,
                          );
                        },
                        child: Center(
                          child: Text(
                            context.getString(StringKey.okayLabel,
                                listen: false),
                            style: const TextStyle(
                                color: Colors.white, fontSize: 18.0),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  void _initHelpTutorial() async {
    final showHelpTutorial = await _viewModel.showHelpTutorial();
    final didUserLearnedVerticalSwipe =
        await _viewModel.didUserLearnedVerticalSwipe();
    if (!showHelpTutorial || !didUserLearnedVerticalSwipe) {
      return;
    }
    initTargets();
    if (mounted) {
      tutorial = TutorialCoachMark(
        targets: targets,
        opacityShadow: 0.9,
        paddingFocus: 6,
        skipWidget: const SizedBox(),
        colorShadow: Colors.grey.shade900,
      )..show(context: context);
      AppAnalytics.onPosterHelpTutorialShown(source: widget.source);
    }
    final PostersFeedConfig config = GetIt.I.get<PostersFeedConfig>();
    Future.delayed(Duration(milliseconds: config.helpTutorialDuration), () {
      if (!mounted || isTutorialAlreadyFinished) return;
      tutorial?.finish();
      _finishTutorial();
      _viewModel.onHelpTutorialCompleted();
      AppAnalytics.onPosterHelpTutorialCompleted(
        source: widget.source,
        autoSkip: true,
      );
    });
  }

  void _finishTutorial() {
    isTutorialAlreadyFinished = true;
  }

  @override
  void dispose() {
    tutorial?.finish();
    _autoCollapseTimer?.cancel();
    _filterSelectionDelayTimer?.cancel();
    _filterAnimationController.dispose();
    _fadeOutAnimationController.dispose();
    super.dispose();
  }

  Widget _getFilterUI({required String imageUrl, required bool isSelected}) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      cacheManager: AppCacheManager.instance,
      imageBuilder: (context, imageProvider) {
        return AnimatedOpacity(
          duration: const Duration(milliseconds: 300),
          opacity: isSelected ? 1.0 : 0.5,
          child: Container(
            width: _filterItemHeight,
            height: _filterItemHeight,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: isSelected ? Colors.white : Colors.transparent,
                width: 1.5,
              ),
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
              ),
            ),
          ),
        );
      },
      placeholder: (context, url) {
        return Container(
          width: _filterItemHeight,
          height: _filterItemHeight,
          decoration: BoxDecoration(
            color: Colors.grey.shade800,
            borderRadius: BorderRadius.circular(6),
          ),
          child: const Center(
            child: SizedBox(
              height: _filterItemHeight * 0.3,
              width: _filterItemHeight * 0.3,
              child: CircularProgressIndicator(
                strokeWidth: 1,
                valueColor: AlwaysStoppedAnimation(Colors.white),
              ),
            ),
          ),
        );
      },
      fadeInDuration: Duration.zero,
      fadeOutDuration: Duration.zero,
    );
  }

  void _onFilterTap(int index, PostersFeedViewFiltersResponse response) {
    // Don't allow filter selection when scroll is disabled (video poster operations active)
    if (_viewModel.isScrollDisabled.value) return;

    final filterAnalyticsParams = response.filters[index].analyticsParams ?? {};
    final analyticsParams = response.analyticsParams ?? {};

    _viewModel.onFilterSelected(
      index,
      filterData: response.filters[index].filter,
      analyticsParams: {
        ...analyticsParams,
        ...filterAnalyticsParams,
      },
    );

    // If selecting a different filter while expanded, start 2-second delay timer
    // This will collapse the filters after 2 seconds if no further selection is made
    if (_isFiltersExpanded) {
      _startFilterSelectionDelayTimer();
    }
  }

  void _startFilterSelectionDelayTimer() {
    // Cancel existing timers
    _autoCollapseTimer?.cancel();
    _filterSelectionDelayTimer?.cancel();

    // Start 2-second delay timer
    _filterSelectionDelayTimer = Timer(const Duration(seconds: 2), () {
      if (mounted) {
        // Instantly collapse without animation
        _instantCollapseFilters();
      }
    });
  }

  void _instantCollapseFilters() {
    _autoCollapseTimer?.cancel();
    _filterSelectionDelayTimer?.cancel();

    // Start fade-out animation
    _fadeOutAnimationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _isFiltersExpanded = false;
        });
        // Reset animation controllers to initial state
        _filterAnimationController.reset();
        _fadeOutAnimationController.value =
            1.0; // Reset to fully visible for next time
      }
    });
  }

  void _initializeFilterAnimations(int filterCount, int selectedIndex) {
    _filterAnimations.clear();
    _filterSlideAnimations.clear();

    for (int i = 0; i < filterCount; i++) {
      if (i == selectedIndex) {
        // Selected filter doesn't animate - always visible
        final staticFadeAnimation = Tween<double>(
          begin: 1.0,
          end: 1.0,
        ).animate(_filterAnimationController);

        final staticSlideAnimation = Tween<Offset>(
          begin: Offset.zero,
          end: Offset.zero,
        ).animate(_filterAnimationController);

        _filterAnimations.add(staticFadeAnimation);
        _filterSlideAnimations.add(staticSlideAnimation);
      } else {
        // Other filters animate in with stagger
        // Adjust index for staggering (selected filter doesn't count)
        final int animationIndex = i > selectedIndex ? i - 1 : i;

        // For forward animation: stagger from left to right
        final double forwardStartTime = (animationIndex * 0.15).clamp(0.0, 0.6);
        final double forwardEndTime = (forwardStartTime + 0.4).clamp(0.4, 1.0);

        // Note: Reverse animation is handled automatically by AnimationController.reverse()

        // Fade animation
        final fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _filterAnimationController,
          curve: Interval(forwardStartTime, forwardEndTime,
              curve: Curves.easeInOut),
        ));

        // Slide animation (slides in from left, slides out to left)
        final slideAnimation = Tween<Offset>(
          begin: const Offset(-0.5, 0.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _filterAnimationController,
          curve: Interval(forwardStartTime, forwardEndTime,
              curve: Curves.easeInOut),
        ));

        _filterAnimations.add(fadeAnimation);
        _filterSlideAnimations.add(slideAnimation);
      }
    }
  }

  void _expandFilters() {
    setState(() {
      _isFiltersExpanded = true;
    });
    // Ensure fade-out animation is reset to fully visible
    _fadeOutAnimationController.value = 1.0;
    _filterAnimationController.forward();
    _startAutoCollapseTimer();
  }

  void _collapseFilters() {
    _autoCollapseTimer?.cancel();
    _filterSelectionDelayTimer?.cancel();
    // Ensure fade-out animation is reset to fully visible for normal collapse
    _fadeOutAnimationController.value = 1.0;
    // Start reverse animation
    _filterAnimationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _isFiltersExpanded = false;
        });
      }
    });
  }

  void _startAutoCollapseTimer() {
    _autoCollapseTimer?.cancel();
    _filterSelectionDelayTimer
        ?.cancel(); // Cancel selection delay if auto-collapse starts
    _autoCollapseTimer = Timer(const Duration(seconds: 5), () {
      if (mounted) {
        _collapseFilters();
      }
    });
  }

  void _onFilterCircleTap() {
    // Don't allow filter expansion/collapse when scroll is disabled
    if (_viewModel.isScrollDisabled.value) return;

    if (_isFiltersExpanded) {
      _collapseFilters();
    } else {
      _expandFilters();
    }
  }

  Widget _buildCollapsedFilter(
      PostersFeedViewFiltersResponse postersFeedViewFilters,
      int selectedIndex) {
    return SizedBox(
      height: _filtersContainerHeight,
      child: Align(
        alignment: Alignment.centerLeft,
        child: InkWell(
          onTap: () {
            _initializeFilterAnimations(
                postersFeedViewFilters.filters.length, selectedIndex);
            _onFilterCircleTap();
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: AnimatedScale(
              duration: const Duration(milliseconds: 500),
              scale: 1.2, // Always show selected filter as scaled
              child: _getFilterUI(
                isSelected: true,
                imageUrl:
                    postersFeedViewFilters.filters[selectedIndex].imageUrl,
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<int> _getReorderedFilterIndices(int selectedIndex, int totalCount) {
    // Put selected filter first, then others in original order
    List<int> indices = [];
    indices.add(selectedIndex); // Selected filter first

    // Add other filters in order
    for (int i = 0; i < totalCount; i++) {
      if (i != selectedIndex) {
        indices.add(i);
      }
    }
    return indices;
  }

  Widget _buildExpandedFilters(
      PostersFeedViewFiltersResponse postersFeedViewFilters,
      int selectedIndex) {
    final reorderedIndices = _getReorderedFilterIndices(
        selectedIndex, postersFeedViewFilters.filters.length);

    return SizedBox(
      height: _filtersContainerHeight,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        itemCount: reorderedIndices.length,
        itemBuilder: (context, listIndex) {
          final originalIndex = reorderedIndices[listIndex];
          final isSelected = originalIndex == selectedIndex;

          Widget filterWidget;

          // Use staggered animations if available, otherwise show immediately
          if (originalIndex < _filterAnimations.length &&
              originalIndex < _filterSlideAnimations.length) {
            filterWidget = AnimatedBuilder(
              animation: _filterAnimationController,
              builder: (context, child) {
                return SlideTransition(
                  position: _filterSlideAnimations[originalIndex],
                  child: FadeTransition(
                    opacity: _filterAnimations[originalIndex],
                    child: Center(
                      child: InkWell(
                        onTap: () {
                          _onFilterTap(originalIndex, postersFeedViewFilters);
                        },
                        child: AnimatedScale(
                          duration: const Duration(milliseconds: 500),
                          scale: isSelected ? 1.2 : 1.0,
                          child: _getFilterUI(
                            isSelected: isSelected,
                            imageUrl: postersFeedViewFilters
                                .filters[originalIndex].imageUrl,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          } else {
            // Fallback for when animations aren't initialized
            filterWidget = Center(
              child: InkWell(
                onTap: () {
                  _onFilterTap(originalIndex, postersFeedViewFilters);
                },
                child: AnimatedScale(
                  duration: const Duration(milliseconds: 500),
                  scale: isSelected ? 1.2 : 1.0,
                  child: _getFilterUI(
                    isSelected: isSelected,
                    imageUrl:
                        postersFeedViewFilters.filters[originalIndex].imageUrl,
                  ),
                ),
              ),
            );
          }

          // Apply fade-out animation only to non-selected filters
          if (isSelected) {
            return filterWidget; // Selected filter doesn't fade out
          } else {
            return AnimatedBuilder(
              animation: _fadeOutAnimationController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeOutAnimationController,
                  child: filterWidget,
                );
              },
            );
          }
        },
        separatorBuilder: (context, index) {
          return const SizedBox(width: 16);
        },
      ),
    );
  }

  Widget _filtersUI({required PostersFeedViewState state}) {
    if (state is FiltersLoadingState) {
      return SizedBox(
        height: _filtersContainerHeight,
        child: Row(
          children: [
            // Show single shimmer circle on the left
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Center(
                child: Shimmer.fromColors(
                  baseColor: Colors.grey.shade800,
                  highlightColor: Colors.grey.shade700,
                  child: Container(
                    height: _filterItemHeight,
                    width: _filterItemHeight,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    } else if (state is FiltersSuccessState) {
      final postersFeedViewFilters = state.postersFeedViewFilters;
      AppAnalytics.onFiltersFetched(
        source: widget.source,
        filtersCount: postersFeedViewFilters.filters.length,
        params: postersFeedViewFilters.analyticsParams,
      );
      if (postersFeedViewFilters.showHelp) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _initHelpTutorial();
        });
      }
      return LiveDataBuilder(
          liveData: _viewModel.showFiltersWithLowOpacity,
          builder: (context, showWithLowOpacity) {
            return AnimatedPositioned(
              duration: const Duration(milliseconds: 500),
              top: showWithLowOpacity ? -_filtersContainerHeight : 0,
              left: 0,
              right: 0,
              child: LiveDataBuilder(
                  liveData: _viewModel.selectedFilterIndex,
                  builder: (context, selectedIndex) {
                    return Row(
                      children: [
                        // Collapsible filters container
                        Expanded(
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut,
                            height: _filtersContainerHeight,
                            child: _isFiltersExpanded
                                ? _buildExpandedFilters(
                                    postersFeedViewFilters, selectedIndex)
                                : _buildCollapsedFilter(
                                    postersFeedViewFilters, selectedIndex),
                          ),
                        ),
                        // Help button always at the end
                        if (postersFeedViewFilters.showHelp)
                          SizedBox(
                            // Why 1.2? Selected filter is scaled to 1.2
                            width: _filterItemHeight * 1.2,
                            height: _filterItemHeight * 1.2,
                            child: PremiumHelpButtonV2(
                              key: helpSpotlightKey,
                              source: "posters_feed_view",
                              iconSize: 18,
                            ),
                          ),
                      ],
                    );
                  }),
            );
          });
    } else if (state is FiltersErrorState) {
      AppAnalytics.onFiltersFetchFailed(
          source: widget.source, errorMessage: state.errorMessage);
      return SizedBox(
        height: _filtersContainerHeight,
        child: Row(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        state.errorMessage,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.refresh, color: Colors.white),
                      onPressed: _viewModel.onFiltersErrorTryAgain,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  static const List<String> _noTopPaddingFeedItems = [
    "__full_page_feed_shimmer",
  ];

  Widget _getBody({required PostersFeedViewState state}) {
    if (state is PostersFeedViewLoadingState) {
      return const FullPageFeedShimmer();
    } else if (state is PostersFeedViewSuccessState) {
      _pageViewTracker.onPageLoaded(_viewModel.getAnalyticsParams());
      return RefreshIndicator(
        displacement: _filtersContainerHeight,
        color: Colors.white,
        onRefresh: () async {
          _viewModel.onRefresh();
        },
        child: LiveDataBuilder(
          liveData: _viewModel.isScrollDisabled,
          builder: (context, isScrollDisabled) {
            return PageView.builder(
              scrollDirection: Axis.vertical,
              controller: _viewModel.pageController,
              physics: isScrollDisabled
                  ? const NeverScrollableScrollPhysics()
                  : null,
              itemCount: state.feedItems.length,
              itemBuilder: (context, index) {
                return Container(
                  padding: widget.padding.copyWith(
                    top: _noTopPaddingFeedItems
                            .contains(state.feedItems[index].feedType)
                        ? 0
                        : _filtersContainerHeight,
                  ),
                  key: ValueKey(state.feedItems[index].feedItemId),
                  color: Colors.black,
                  child: FullPageFeedItemBuilder(
                    item: state.feedItems[index],
                    source: "posters_feed_view",
                    index: index,
                    onFullPageFeedTryAgain:
                        _viewModel.onPostersFeedErrorTryAgain,
                    showHorizontalSwipeHint:
                        index == 2, //show horizontal swipe hint on 3rd page
                    onPhotoUpdated: (returnUrl) {
                      DeeplinkDestination.fromRoute(returnUrl)
                          ?.go(context, DeeplinkSource.internalDeeplink);
                    },
                    onVideoPosterOperationStarted:
                        _viewModel.onVideoPosterOperationStarted,
                    onVideoPosterOperationEnded:
                        _viewModel.onVideoPosterOperationEnded,
                  ),
                );
              },
            );
          },
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.black,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
        systemNavigationBarColor: Color(0xFF292929),
        systemNavigationBarIconBrightness: Brightness.light,
      ),
      child: Theme(
        data: ThemeData.dark(),
        child: EventListener(
          eventQueue: _navViewModel.eventQueue,
          onEvent: (context, event) async {
            if (event is PostersFeedDeeplinkEvent) {
              _viewModel.onUpdatePostersFeedEventTriggered(
                  params: event.params);
            }
          },
          child: Stack(
            children: [
              LiveDataBuilder(
                liveData: _viewModel.state,
                builder: (context, state) {
                  return _getBody(state: state);
                },
              ),
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: InkWell(
                  onTap: _viewModel.onShowFilters,
                  child: Container(
                    height: _filtersContainerHeight,
                    color: Colors.transparent,
                  ),
                ),
              ),
              LiveDataBuilder(
                liveData: _viewModel.filtersState,
                builder: (context, state) {
                  return _filtersUI(state: state);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
