/// If we use it like this, then we need to create a new enum for every type of posters
/// Eg: Image posters, video, animated posters, etc.
/// So, created a common enum [PosterShareDestination] in [lib/enums/poster_share_destination.dart]
/// Use this enum for all the poster shares to maintain consistency
@Deprecated('Use PosterShareDestination instead')
enum PosterLayoutShareDestination {
  whatsapp,
  externalShare,
  download,
  unknown;

  String asMethod() {
    switch (this) {
      case PosterLayoutShareDestination.whatsapp:
        return 'whatsapp';
      case PosterLayoutShareDestination.externalShare:
        return 'external-share';
      case PosterLayoutShareDestination.download:
        return 'download';
      case PosterLayoutShareDestination.unknown:
        return 'unknown';
    }
  }
}
