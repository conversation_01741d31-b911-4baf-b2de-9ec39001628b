import 'package:flutter/material.dart';
import 'package:praja/features/localization/vertical_adjustment_padding.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/presentation/praja_icons.dart';

const Color _lightModeIconColor = Color(0xff222222);

class PosterShareActionsWidget extends StatelessWidget {
  final VoidCallback onShareClicked;
  final VoidCallback onDownloadClicked;
  final VoidCallback onWhatsappClicked;
  final Size minimumActionSize;

  const PosterShareActionsWidget({
    super.key,
    required this.onShareClicked,
    required this.onDownloadClicked,
    required this.onWhatsappClicked,
    this.minimumActionSize = const Size.fromHeight(48),
  });

  ButtonStyle _darkModeButtonStyle() => ElevatedButton.styleFrom(
        foregroundColor: Colors.white,
        backgroundColor: const Color(0xFF292929),
        minimumSize: minimumActionSize,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
        elevation: 0,
      );

  ButtonStyle _lightModeButtonStyle() => ElevatedButton.styleFrom(
        foregroundColor: const Color(0xFF000000),
        backgroundColor: const Color(0xFFFFFFFF),
        minimumSize: minimumActionSize,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
            side: const BorderSide(color: Color(0xff000000), width: 1.0)),
        elevation: 0,
      );

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Column(
      key: const ValueKey('share-buttons'),
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          height: 8,
        ),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: onShareClicked,
                label: Padding(
                  padding: const EdgeInsets.all(4.0) +
                      context.verticalAdjustmentPadding(),
                  child: Text(
                    context.getString(StringKey.posterShareButtonLabel),
                    maxLines: 1,
                  ),
                ),
                icon: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Icon(
                    Icons.share,
                    color: isDarkMode ? Colors.white : _lightModeIconColor,
                    size: 18,
                  ),
                ),
                style: isDarkMode
                    ? _darkModeButtonStyle()
                    : _lightModeButtonStyle(),
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: onDownloadClicked,
                label: Padding(
                  padding: const EdgeInsets.all(4.0) +
                      context.verticalAdjustmentPadding(),
                  child: Text(
                    context.getString(StringKey.posterDownloadButtonLabel),
                    maxLines: 1,
                  ),
                ),
                icon: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Icon(
                    Icons.download,
                    color: isDarkMode ? Colors.white : _lightModeIconColor,
                    size: 18,
                  ),
                ),
                style: isDarkMode
                    ? _darkModeButtonStyle()
                    : _lightModeButtonStyle(),
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        ElevatedButton.icon(
          onPressed: onWhatsappClicked,
          label: Padding(
            padding:
                const EdgeInsets.all(4.0) + context.verticalAdjustmentPadding(),
            child: Text(
              context.getString(StringKey.posterWhatsappShareButtonLabel),
              maxLines: 1,
              style: const TextStyle(
                color: Colors.white,
              ),
            ),
          ),
          icon: const Icon(
            PrajaIcons.whatsapp_fill,
            color: Colors.white,
            size: 16,
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xff25D366),
            foregroundColor: Colors.black,
            minimumSize: minimumActionSize,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
        const SizedBox(
          height: 8,
        ),
      ],
    );
  }
}
