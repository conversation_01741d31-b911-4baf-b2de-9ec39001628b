import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:jetpack/livedata.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/media_picker/media_picker.dart';
import 'package:praja/features/media_picker/profile_image_picker.dart';
import 'package:praja/features/posters/models/poster_photo_selected_from_history_model.dart';
import 'package:praja/features/posters/models/poster_photos_history_response.dart';
import 'package:praja/features/posters/poster_photo_update/poster_photo_selection_view_model.dart';
import 'package:praja/features/posters/poster_photos_history/poster_photos_history_widget.dart';
import 'package:praja/features/posters/widgets/poster_photo_picker_placeholder_ui.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/slide_replace_animation_widget.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/utils/logger.dart';
import 'package:provider/provider.dart';

class PosterPhotoSelectionPage extends BasePage {
  final String source;
  final String userPhotoType;
  final Function(PosterPhoto updatedPhoto)? onPhotoUpdated;
  final String? returnUrl;

  const PosterPhotoSelectionPage({
    super.key,
    required this.source,
    required this.userPhotoType,
    this.onPhotoUpdated,
    this.returnUrl,
  });

  @override
  Widget buildContent(BuildContext context) {
    return _PosterPhotoSelectionPageInner(
      key: key,
      source: source,
      userPhotoType: userPhotoType,
      onPhotoUpdated: onPhotoUpdated,
      returnUrl: returnUrl,
    );
  }

  @override
  String get pageName => "poster_photo_selection";

  @override
  bool get pageLoadRequiredForTracking => true;
}

class _PosterPhotoSelectionPageInner extends StatefulWidget {
  final String source;
  final String userPhotoType;
  final Function(PosterPhoto updatedPhoto)? onPhotoUpdated;
  final String? returnUrl;

  const _PosterPhotoSelectionPageInner({
    super.key,
    required this.source,
    required this.userPhotoType,
    this.onPhotoUpdated,
    this.returnUrl,
  });

  @override
  State<_PosterPhotoSelectionPageInner> createState() =>
      _PosterPhotoSelectionPageInnerState();
}

class _PosterPhotoSelectionPageInnerState
    extends State<_PosterPhotoSelectionPageInner> {
  late PosterPhotoSelectionViewModel _viewModel;
  late PageViewTracker _pageViewTracker;

  bool get isDarkMode => Theme.of(context).brightness == Brightness.dark;

  @override
  void initState() {
    super.initState();
    _pageViewTracker = Provider.of<PageViewTracker>(context, listen: false);
    _viewModel = context.posterPhotoSelectionViewModel(
      userPhotoType: widget.userPhotoType,
      onPhotoUpdated: (updatedPhoto) {
        if (!mounted) return;
        Navigator.pop(context);
        if (widget.onPhotoUpdated != null) {
          widget.onPhotoUpdated!(updatedPhoto);
        } else if (widget.returnUrl != null) {
          DeeplinkDestination.fromRoute(widget.returnUrl)
              ?.go(context, DeeplinkSource.internalDeeplink);
        } else {
          DeeplinkDestination.fromRoute(
                  '/posters/layout?source=poster_photo_update')
              ?.go(context, DeeplinkSource.internalDeeplink);
        }
        //pop the page after photo is updated
      },
    );
    _pageViewTracker.onPageLoaded(
      {
        'source': widget.source,
        'user_photo_type': widget.userPhotoType,
      },
    );
  }

  Widget _buildImageWidget(String imageUrl) {
    return AspectRatio(
      aspectRatio: 1.0,
      child: CachedNetworkImage(
        cacheManager: AppCacheManager.instance,
        imageUrl: imageUrl,
        fadeInDuration: const Duration(milliseconds: 100),
        fadeOutDuration: const Duration(milliseconds: 100),
      ),
    );
  }

  Widget _buildFileImageWidget(String imageUrl) {
    return AspectRatio(
      aspectRatio: 1.0,
      child: Image.file(
        File(imageUrl),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvoked: (bool didPop) {
        if (didPop) {
          _viewModel.onBackPressed();
        }
      },
      child: Theme(
        data: ThemeData.dark(),
        child: Stack(
          children: [
            Scaffold(
              appBar: AppBar(
                backgroundColor: Colors.black,
                actions: [
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () {
                      _viewModel.onDeleteImage();
                    },
                  )
                ],
              ),
              body: Column(
                children: [
                  SizedBox(
                    height: 400,
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      transitionBuilder: (child, animation) {
                        return FadeTransition(
                          opacity: animation,
                          child: child,
                        );
                      },
                      child: LiveDataBuilder(
                          liveData: _viewModel.selectedImageFromGallery,
                          builder: (context, selectedImagePath) {
                            if (selectedImagePath != null) {
                              return LiveDataBuilder(
                                liveData: _viewModel.bgRemovedPhotoURL,
                                builder: (context, url) {
                                  return SlideReplaceAnimationWidget(
                                    before: _buildFileImageWidget(
                                        selectedImagePath),
                                    after: url.isNotEmpty
                                        ? _buildImageWidget(url)
                                        : null,
                                    onAnimationComplete: () {
                                      _viewModel.onAnimationComplete();
                                    },
                                  );
                                },
                              );
                            }
                            return PosterPhotoPickerPlaceholderUI(
                              height: double.infinity,
                              userPhotoType: widget.userPhotoType,
                              onTap: () async {
                                try {
                                  AppAnalytics
                                      .onPhotoPhotoUpdateGalleryIconClicked(
                                    source: widget.source,
                                    userPhotoType: widget.userPhotoType,
                                  );
                                  final ProfilePicResult? result =
                                      await pickPictureForPosterPhoto(context,
                                          type: MediaPickerType.image);
                                  final image = result?.image;
                                  if (image == null) return;
                                  AppAnalytics.onPosterPhotoSelectedFromGallery(
                                    source: widget.source,
                                    userPhotoType: widget.userPhotoType,
                                  );
                                  _viewModel.onImageSelected(image: image);
                                } catch (e, st) {
                                  logNonFatal(
                                      "Error Picking Poster Photo Picture From Gallery",
                                      e,
                                      stackTrace: st);
                                }
                              },
                            );
                          }),
                    ),
                  ),
                  Expanded(
                    child: Stack(
                      children: [
                        // History widget (doesn't rebuild when gallery image changes)
                        PosterPhotosHistoryWidget(
                          userPhotoType: widget.userPhotoType,
                          onPhotoSelected: (PosterPhotoSelectedFromHistoryModel
                              photoSelected) async {
                            await _viewModel
                                .onPosterPhotoSelectedFromPhotosHistory(
                                    photoSelected);
                          },
                        ),
                        // Overlay that rebuilds when gallery image changes
                        LiveDataBuilder<String?>(
                          liveData: _viewModel.selectedImageFromGallery,
                          builder: (context, selectedImagePath) {
                            return selectedImagePath != null
                                ? Positioned.fill(
                                    child: Container(
                                      color: Colors.black.withOpacity(0.5),
                                    ),
                                  )
                                : const SizedBox.shrink();
                          },
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
            LiveDataBuilder(
              liveData: _viewModel.userPhotoUpdateInProgress,
              builder: (context, inProgress) {
                return !inProgress
                    ? const SizedBox.shrink()
                    : Positioned.fill(
                        child: Container(
                          color: isDarkMode
                              ? Colors.white.withOpacity(0.7)
                              : Colors.black.withOpacity(0.7),
                          child: Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation(
                                isDarkMode ? Colors.black : Colors.white,
                              ),
                            ),
                          ),
                        ),
                      );
              },
            ),
          ],
        ),
      ),
    );
  }
}
