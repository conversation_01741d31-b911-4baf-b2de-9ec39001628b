import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/feed_item_abstract_class.dart';
import 'package:praja_posters/praja_posters.dart';

part 'poster_carousel.g.dart';

@JsonSerializable()
class PosterCarousel extends FeedItem {
  @JsonKey(name: 'items')
  final List<PosterCarouselItem> items;
  @Json<PERSON>ey(name: 'more_text', defaultValue: '')
  final String moreText;
  @<PERSON>son<PERSON>ey(name: 'more_deeplink', defaultValue: '')
  final String moreDeeplink;
  @<PERSON>son<PERSON>ey(name: 'more_cta', defaultValue: '')
  final String moreCta;
  @Json<PERSON>ey(name: 'show_help', defaultValue: false)
  final bool showHelp;
  @Json<PERSON>ey(name: 'disable_screenshot', defaultValue: true)
  final bool disableScreenshot;

  PosterCarousel({
    required this.items,
    required this.moreText,
    required this.moreDeeplink,
    required this.moreCta,
    required this.showHelp,
    required this.disableScreenshot,
    required super.feedType,
    required super.feedItemId,
  });

  factory PosterCarousel.fromJson(Map<String, dynamic> json) =>
      _$PosterCarouselFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PosterCarouselToJson(this);

  //copy with method
  PosterCarousel copyWith({
    List<PosterCarouselItem>? items,
    String? moreText,
    String? moreDeeplink,
    String? moreCta,
    bool? showHelp,
    bool? disableScreenshot,
    String? feedType,
    String? feedItemId,
  }) {
    return PosterCarousel(
      items: items ?? this.items,
      moreText: moreText ?? this.moreText,
      moreDeeplink: moreDeeplink ?? this.moreDeeplink,
      moreCta: moreCta ?? this.moreCta,
      showHelp: showHelp ?? this.showHelp,
      disableScreenshot: disableScreenshot ?? this.disableScreenshot,
      feedType: feedType ?? this.feedType,
      feedItemId: feedItemId ?? this.feedItemId,
    );
  }

  @override
  String toString() {
    return 'PosterCarousel{items: $items, moreText: $moreText, moreDeeplink: $moreDeeplink, moreCta: $moreCta, showHelp: $showHelp, disableScreenshot: $disableScreenshot}';
  }
}

@JsonSerializable()
class PosterCarouselItem {
  final PosterCreative creative;
  final PosterLayout layout;

  @JsonKey(name: 'event_date', defaultValue: '')
  final String eventDate;

  final Map<String, dynamic> params;

  PosterCarouselItem({
    required this.creative,
    required this.layout,
    required this.eventDate,
    required this.params,
  });

  factory PosterCarouselItem.fromJson(Map<String, dynamic> json) =>
      _$PosterCarouselItemFromJson(json);

  Map<String, dynamic> toJson() => _$PosterCarouselItemToJson(this);

  //copyWith method
  PosterCarouselItem copyWith({
    PosterCreative? creative,
    PosterLayout? layout,
    String? eventDate,
    Map<String, dynamic>? params,
  }) {
    return PosterCarouselItem(
      creative: creative ?? this.creative,
      layout: layout ?? this.layout,
      eventDate: eventDate ?? this.eventDate,
      params: params ?? this.params,
    );
  }

  @override
  String toString() {
    return 'PosterCarouselItem{creative: $creative, layout: $layout, eventDate: $eventDate, params: $params}';
  }
}
