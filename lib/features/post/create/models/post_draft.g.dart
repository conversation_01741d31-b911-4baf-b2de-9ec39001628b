// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_draft.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostDraft _$PostDraftFromJson(Map<String, dynamic> json) => PostDraft(
      text: json['text'] as String? ?? '',
      taggedCircleIds: (json['tagged_circle_ids'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          [],
      repostId: json['repost_id'] as String?,
      videos: (json['videos'] as List<dynamic>?)
              ?.map((e) => LocalVideo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      photos: (json['photos'] as List<dynamic>?)
              ?.map((e) => LocalImage.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      commentOption: json['comment_option'] == null
          ? null
          : CommentOption.fromJson(
              json['comment_option'] as Map<String, dynamic>),
      analyticsParams: json['analytics_params'] as Map<String, dynamic>,
      imageUploadUrl: json['image_upload_url'] as String,
      videoUploadUrl: json['video_upload_url'] as String,
      attachmentCreative: json['attachment_creative'] == null
          ? null
          : PostAttachmentCreative.fromJson(
              json['attachment_creative'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PostDraftToJson(PostDraft instance) {
  final val = <String, dynamic>{
    'text': instance.text,
    'tagged_circle_ids': instance.taggedCircleIds,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('repost_id', instance.repostId);
  val['videos'] = instance.videos.map((e) => e.toJson()).toList();
  val['photos'] = instance.photos.map((e) => e.toJson()).toList();
  writeNotNull('comment_option', instance.commentOption?.toJson());
  val['analytics_params'] = instance.analyticsParams;
  val['image_upload_url'] = instance.imageUploadUrl;
  val['video_upload_url'] = instance.videoUploadUrl;
  writeNotNull('attachment_creative', instance.attachmentCreative?.toJson());
  return val;
}
