import 'package:flutter/material.dart';
import 'package:praja/features/intl/intl.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/profile/my_profile/my_profile_page.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/features/user/ui/app_user_editable_avatar.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/badge.dart';
import 'package:praja/utils/widgets/badge_strip_profile.dart';

class UserProfileTile extends StatelessWidget {
  final AppUser user;
  final String source;
  const UserProfileTile({
    super.key,
    required this.user,
    required this.source,
  });

  bool isGoldStrip() {
    final badge = user.badge;
    return (badge?.badgeBanner == BadgeBanner.gold) ? true : false;
  }

  Widget getFollowersAndFollowingDetailsWidget({
    required BuildContext context,
    bool isBadgePresent = false,
  }) {
    String followersTeluguCountWithText =
        user.followersCount.toDisplayFormat(usedAsPrefix: true);
    List<String> followersTextToShow = followersTeluguCountWithText.split(' ');
    String followingTeluguCountWithText =
        user.followingCount.toDisplayFormat(usedAsPrefix: true);
    List<String> followingTextToShow = followingTeluguCountWithText.split(' ');
    return Padding(
      padding: isBadgePresent
          ? const EdgeInsets.only(top: 0.0, bottom: 4.0, left: 69, right: 20)
          : const EdgeInsets.only(top: 4.0),
      child: Align(
        alignment: Alignment.centerLeft,
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Row(
            mainAxisAlignment: isBadgePresent
                ? MainAxisAlignment.center
                : MainAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    followersTextToShow[0],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textScaleFactor: 1,
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  followersTextToShow.length > 1
                      ? Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Text(
                            followersTextToShow[1],
                            textScaleFactor: 1,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 10,
                            ),
                          ),
                        )
                      : const SizedBox(),
                  const SizedBox(width: 2),
                  Text(
                    context.getPluralizedString(
                        StringKey.userFollowersCountSuffixText,
                        user.followersCount),
                    textScaleFactor: 1,
                    style: const TextStyle(
                      fontSize: 10,
                      color: Color(0xff8f8f8f),
                      height: 1.5,
                    ),
                  )
                ],
              ),
              const SizedBox(width: 20),
              Row(
                children: [
                  Text(
                    followingTextToShow[0],
                    textScaleFactor: 1,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  followingTextToShow.length > 1
                      ? Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Text(
                            followingTextToShow[1],
                            textScaleFactor: 1,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 10,
                            ),
                          ),
                        )
                      : const SizedBox(),
                  const SizedBox(width: 2),
                  Text(
                    context.getString(StringKey.followingUserLabel),
                    textScaleFactor: 1,
                    style: const TextStyle(
                      fontSize: 10,
                      color: Color(0xff8f8f8f),
                      height: 1.5,
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final badge = user.badge;
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(top: (user.badge != null) ? 10.0 : 0.0),
              child: AppUserEditableAvatar(
                size: 56,
                source: source,
              ),
            ),
            const SizedBox(width: 13),
            Expanded(
                child: InkWell(
              onTap: () {
                AppAnalytics.navigationDrawerOptionTileClickEvent(
                  option: "user_profile_tile",
                  source: "navigation_drawer",
                );
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const MyProfilePage(
                      source: "navigation_drawer_user_tile",
                    ),
                  ),
                );
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    user.name,
                    textScaleFactor: 1,
                    maxLines: 1,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xff222222),
                      fontWeight: FontWeight.bold,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (badge != null && badge.description.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 8.0,
                        top: 2.0,
                        right: 22,
                      ),
                      child: BadgeStripProfile(badge, height: 20, fontSize: 8),
                    ),
                  (badge != null && badge.description.isNotEmpty)
                      ? const SizedBox()
                      : getFollowersAndFollowingDetailsWidget(context: context),
                ],
              ),
            )),
          ],
        ),
        SizedBox(
            height: (badge != null && badge.description.isNotEmpty) ? 2 : 0),
        (badge != null && badge.description.isNotEmpty)
            ? InkWell(
                onTap: () {
                  AppAnalytics.navigationDrawerOptionTileClickEvent(
                    option: "user_profile_tile",
                    source: "navigation_drawer",
                  );
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MyProfilePage(
                        source: "navigation_drawer_user_tile",
                      ),
                    ),
                  );
                },
                child: getFollowersAndFollowingDetailsWidget(
                    context: context, isBadgePresent: true),
              )
            : const SizedBox(),
      ],
    );
  }
}
