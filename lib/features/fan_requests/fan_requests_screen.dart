import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/sponsor_banner_widget.dart';
import 'package:praja/common/widgets/dots_indicator_widget.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/features/fan_requests/fan_requests_screen_view_model.dart';
import 'package:praja/features/fan_requests/widgets/lead_section_details_widget.dart';
import 'package:praja/features/fan_requests/widgets/requested_users_widget.dart';
import 'package:praja/features/posters/widgets/basic_poster_widget.dart';
import 'package:praja/features/posters/widgets/poster_carousel_extensions.dart';
import 'package:praja/features/posters/widgets/poster_constants.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja_posters/praja_posters.dart';

class FanRequestsScreen extends BasePage {
  final int circleId;
  final String source;
  final Map<String, dynamic>? analyticsParams;

  const FanRequestsScreen({
    super.key,
    required this.circleId,
    required this.source,
    this.analyticsParams,
  });

  @override
  Widget buildContent(BuildContext context) {
    return FanRequestsScreenInner(
      circleId: circleId,
      source: source,
      analyticsParams: analyticsParams,
    );
  }

  @override
  String get pageName => "fan_requests";
}

class FanRequestsScreenInner extends StatelessWidget {
  final int circleId;
  final String source;
  final Map<String, dynamic>? analyticsParams;

  const FanRequestsScreenInner({
    super.key,
    required this.circleId,
    required this.source,
    this.analyticsParams,
  });

  Widget getInnerChild({
    required PosterLayout currentLayout,
    required FanRequestsScreenSuccessState state,
  }) {
    switch (currentLayout.layoutType) {
      case PosterLayoutTypeEnum.basic:
        return BasicPosterWidget(
          creative: state.fanRequestsScreenResponse.creative,
          layout: currentLayout,
          interactive: false,
          loggedInUser: null,
          showLayoutUserData: true,
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _getItemBannerWidget({required PosterLayout currentLayout}) {
    final sponsorBanner = currentLayout.sponsorBanner;
    if (sponsorBanner != null) {
      return SponsorBannerWidget(
        sponsorBanner: currentLayout.sponsorBanner,
      );
    }
    return const SizedBox.shrink();
  }

  getItems({
    required FanRequestsScreenSuccessState state,
  }) {
    final List<Widget> items = [];
    final List<PosterLayout> layouts = state.fanRequestsScreenResponse.layouts;
    for (int i = 0; i < layouts.length; i++) {
      final PosterLayout currentLayout = layouts[i];

      items.add(
        Padding(
          padding: const EdgeInsets.only(right: 24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _getItemBannerWidget(currentLayout: currentLayout),
              const SizedBox(height: 16),
              AspectRatio(
                aspectRatio: posterWidth / posterHeight,
                child: FittedBox(
                  fit: BoxFit.cover,
                  child: getInnerChild(
                    state: state,
                    currentLayout: currentLayout,
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
      );
    }
    return items;
  }

  Widget _getBody({
    required FanRequestsScreenState state,
    required FanRequestsScreenViewModel viewModel,
    required BuildContext context,
  }) {
    if (state is FanRequestsScreenLoadingState) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    } else if (state is FanRequestsScreenErrorState) {
      AppAnalytics.onFanRequestsScreenError(source: source, params: {
        'error': state.errorMessage,
        ...analyticsParams ?? {},
      });
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            state.errorMessage,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              AppAnalytics.onRetryFanRequestsScreenClicked(
                  source: source,
                  params: {
                    'error': state.errorMessage,
                    ...analyticsParams ?? {}
                  });
              viewModel.getFanRequestsScreenResponse(circleId: circleId);
            },
            child: const Text(
              'మళ్ళీ ప్రయత్నించండి',
            ),
          ),
        ],
      );
    } else if (state is FanRequestsScreenSuccessState) {
      AppAnalytics.onFanRequestsScreenViewed(
          source: source, params: analyticsParams);
      return SingleChildScrollView(
        child: Column(
          children: [
            RequestedUsersWidget(
              requestedUsers: state.fanRequestsScreenResponse.requestedUsers,
            ),
            const SizedBox(height: 8),
            CarouselSlider(
              items: getItems(state: state),
              options: CarouselOptions(
                height: context.carouselHeight(),
                pauseAutoPlayOnTouch: true,
                pauseAutoPlayOnManualNavigate: true,
                enableInfiniteScroll: false,
                enlargeCenterPage: false,
                viewportFraction: 0.9,
                autoPlay: false,
                onPageChanged: (index, reason) {
                  viewModel.onLayoutChange(index);
                },
              ),
            ),
            LiveDataBuilder(
              liveData: viewModel.selectedLayoutIndex,
              builder: (_, index) {
                return DotsIndicatorWidget(
                  length: state.fanRequestsScreenResponse.layouts.length,
                  controller: viewModel.dotsIndicatorController,
                );
              },
            ),
            const SizedBox(height: 16),
            LeadSectionDetailsWidget(
              leadSectionDetails:
                  state.fanRequestsScreenResponse.leadSectionDetails,
              viewModel: viewModel,
              circleId: circleId,
              source: source,
            ),
            const SizedBox(height: 16),
          ],
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = context.getViewModel<FanRequestsScreenViewModel>()
      ..initialise(circleId: circleId);
    return WillPopScope(
      onWillPop: () async {
        final curState = viewModel.state.value;
        Map<String, dynamic> params = analyticsParams ?? {};
        if (curState is FanRequestsScreenSuccessState) {
          params['sheet_status'] = 'success';
        } else if (curState is FanRequestsScreenErrorState) {
          params['error'] = curState.errorMessage;
          params['sheet_status'] = 'error';
        } else {
          params['sheet_status'] = 'loading';
        }
        AppAnalytics.onFanRequestsScreenClosed(source: source, params: params);
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.black,
          title: const Text(
            'ప్రీమియం పోస్టర్లు',
            style: TextStyle(color: Colors.white),
          ),
          iconTheme: const IconThemeData(color: Colors.white),
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.black,
            statusBarIconBrightness: Brightness.light,
            systemNavigationBarColor: Colors.black,
            systemNavigationBarIconBrightness: Brightness.light,
          ),
        ),
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xff2D2D2D),
                Color(0xff000000),
                Color(0xff2D2D2D),
              ],
              stops: [0.2976, 0.593, 0.8133],
            ),
          ),
          child: LiveDataBuilder(
            liveData: viewModel.state,
            builder: (_, state) {
              return _getBody(
                state: state,
                viewModel: viewModel,
                context: context,
              );
            },
          ),
        ),
      ),
    );
  }
}
