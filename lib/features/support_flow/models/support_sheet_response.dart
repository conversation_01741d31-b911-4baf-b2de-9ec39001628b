import 'package:json_annotation/json_annotation.dart';
import 'package:praja/features/support_flow/models/support_sheet_option.dart';

part 'support_sheet_response.g.dart';

@JsonSerializable()
class SupportSheetResponse {
  @Json<PERSON>ey(name: 'title', defaultValue: '')
  final String title;
  @Json<PERSON>ey(name: 'sub_title', defaultValue: '')
  final String subTitle;
  @JsonKey(name: 'options', defaultValue: [])
  final List<SupportSheetOption> options;
  @JsonKey(name: 'is_premium_user', defaultValue: false)
  final bool isPremiumUser;
  @Json<PERSON>ey(name: 'analytics_params')
  final Map<String, dynamic>? analyticsParams;

  SupportSheetResponse({
    required this.title,
    required this.subTitle,
    required this.options,
    required this.isPremiumUser,
    required this.analyticsParams,
  });

  factory SupportSheetResponse.fromJson(Map<String, dynamic> json) =>
      _$SupportSheetResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SupportSheetResponseToJson(this);

  @override
  String toString() {
    return 'SupportSheetResponse{title: $title, subTitle: $subTitle, options: $options, premiumUser: $isPremiumUser, analyticsParams: $analyticsParams}';
  }
}
