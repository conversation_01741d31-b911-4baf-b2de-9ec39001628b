// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'offer_reveal_sheet_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OfferRevealSheetResponse _$OfferRevealSheetResponseFromJson(
        Map<String, dynamic> json) =>
    OfferRevealSheetResponse(
      imageUrl: json['image_url'] as String?,
      label: json['label'] as String? ?? '',
      giftOfferText: json['gift_offer_text'] as String? ?? '',
      subText: json['sub_text'] as String? ?? '',
      buttonText: json['button_text'] as String? ?? '',
      skipText: json['skip_text'] as String? ?? '',
      deeplink: json['deeplink'] as String? ?? '',
      params: json['params'] as Map<String, dynamic>?,
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$OfferRevealSheetResponseToJson(
    OfferRevealSheetResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('image_url', instance.imageUrl);
  val['label'] = instance.label;
  val['gift_offer_text'] = instance.giftOfferText;
  val['sub_text'] = instance.subText;
  val['button_text'] = instance.buttonText;
  val['skip_text'] = instance.skipText;
  val['deeplink'] = instance.deeplink;
  writeNotNull('params', instance.params);
  writeNotNull('analytics_params', instance.analyticsParams);
  return val;
}
