// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'premium_benefits_loss_screen_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PremiumBenefitsLossScreenResponse _$PremiumBenefitsLossScreenResponseFromJson(
        Map<String, dynamic> json) =>
    PremiumBenefitsLossScreenResponse(
      title: json['title'] as String? ?? '',
      subTitle: json['sub_title'] as String? ?? '',
      carousel:
          PosterCarousel.fromJson(json['carousel'] as Map<String, dynamic>),
      continueMembershipButton: json['continue_membership_button'] == null
          ? null
          : PremiumExperienceButtonDetails.fromJson(
              json['continue_membership_button'] as Map<String, dynamic>),
      extendPlanButton: json['extend_plan_button'] == null
          ? null
          : PremiumExperienceButtonDetails.fromJson(
              json['extend_plan_button'] as Map<String, dynamic>),
      cancelMembershipButton: PremiumExperienceButtonDetails.fromJson(
          json['cancel_membership_button'] as Map<String, dynamic>),
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PremiumBenefitsLossScreenResponseToJson(
    PremiumBenefitsLossScreenResponse instance) {
  final val = <String, dynamic>{
    'title': instance.title,
    'sub_title': instance.subTitle,
    'carousel': instance.carousel.toJson(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('continue_membership_button',
      instance.continueMembershipButton?.toJson());
  writeNotNull('extend_plan_button', instance.extendPlanButton?.toJson());
  val['cancel_membership_button'] = instance.cancelMembershipButton.toJson();
  writeNotNull('analytics_params', instance.analyticsParams);
  return val;
}
