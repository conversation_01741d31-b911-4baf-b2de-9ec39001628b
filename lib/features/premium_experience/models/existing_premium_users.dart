import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/user_identity.dart';

part 'existing_premium_users.g.dart';

@JsonSerializable()
class ExistingPremiumUsers {
  final String title;
  final List<UserIdentity> users;

  ExistingPremiumUsers({required this.title, required this.users});

  factory ExistingPremiumUsers.fromJson(Map<String, dynamic> json) =>
      _$ExistingPremiumUsersFromJson(json);

  Map<String, dynamic> toJson() => _$ExistingPremiumUsersToJson(this);

  @override
  String toString() {
    return 'ExistingPremiumUsers{title: $title, users: $users}';
  }
}
