import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/feed_item_abstract_class.dart';

part 'payment_feed_item.g.dart';

@JsonSerializable()
class PaymentFeedItem extends FeedItem {
  @Json<PERSON>ey(name: 'text', defaultValue: '')
  final String text;
  @Json<PERSON>ey(name: 'sub_text', defaultValue: '')
  final String subText;
  @<PERSON>son<PERSON>ey(name: 'discount_text', defaultValue: '')
  final String discountText;
  @<PERSON>son<PERSON>ey(name: 'discount_text_color', defaultValue: 0xff4ea502)
  final int discountTextColor;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'deeplink', defaultValue: '')
  final String deeplink;
  @Json<PERSON>ey(name: 'analytics_params', defaultValue: {})
  final Map<String, dynamic> analyticsParams;

  PaymentFeedItem({
    required this.text,
    required this.subText,
    required this.discountText,
    required this.discountTextColor,
    required this.deeplink,
    required this.analyticsParams,
    required super.feedType,
    super.feedItemId,
  });

  factory PaymentFeedItem.fromJson(Map<String, dynamic> json) =>
      _$PaymentFeedItemFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PaymentFeedItemToJson(this);

  @override
  String toString() {
    return 'PaymentFeedItem{text: $text, subText: $subText, discountText: $discountText, discountTextColor: $discountTextColor, deeplink: $deeplink, analyticsParams: $analyticsParams}';
  }
}
