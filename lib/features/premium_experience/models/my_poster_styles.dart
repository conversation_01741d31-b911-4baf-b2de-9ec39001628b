import 'package:json_annotation/json_annotation.dart';
import 'package:praja/models/feed_item_abstract_class.dart';
import 'package:praja_posters/praja_posters.dart';

part 'my_poster_styles.g.dart';

@JsonSerializable()
class MyPosterStyles extends FeedItem {
  @Json<PERSON>ey(name: 'title', defaultValue: '')
  final String title;
  @<PERSON>son<PERSON><PERSON>(name: 'locked', defaultValue: false)
  final bool locked;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'creative')
  final PosterCreative creative;
  @<PERSON>sonKey(name: 'layouts', defaultValue: [])
  final List<PosterLayout> layouts;

  /// On Clicking Lock Icon, we will navigate to DeepLink
  @JsonKey(name: 'deeplink', defaultValue: '')
  final String deeplink;
  @Json<PERSON>ey(name: 'analytics_params', defaultValue: {})
  final Map<String, dynamic> analyticsParams;

  MyPosterStyles({
    required this.title,
    required this.locked,
    required this.creative,
    required this.layouts,
    required this.deeplink,
    required this.analyticsParams,
    required super.feedType,
    super.feedItemId,
  });

  factory MyPosterStyles.fromJson(Map<String, dynamic> json) =>
      _$MyPosterStylesFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$MyPosterStylesToJson(this);

  @override
  String toString() {
    return 'MyPosterStyles{title: $title, locked: $locked, creative: $creative, layouts: $layouts, deeplink: $deeplink, analyticsParams: $analyticsParams}';
  }
}
