// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Subscription _$SubscriptionFromJson(Map<String, dynamic> json) => Subscription(
      id: json['id'] as String,
      status: $enumDecodeNullable(_$SubscriptionStatusEnumMap, json['status'],
              unknownValue: SubscriptionStatus.unknown) ??
          SubscriptionStatus.unknown,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SubscriptionToJson(Subscription instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'status': _$SubscriptionStatusEnumMap[instance.status]!,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('message', instance.message);
  return val;
}

const _$SubscriptionStatusEnumMap = {
  SubscriptionStatus.successful: 'successful',
  SubscriptionStatus.opened: 'opened',
  SubscriptionStatus.failed: 'failed',
  SubscriptionStatus.pending: 'pending',
  SubscriptionStatus.unknown: 'unknown',
};
