// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'faqs.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FAQs _$FAQsFromJson(Map<String, dynamic> json) => FAQs(
      title: json['title'] as String,
      faqs: (json['faqs'] as List<dynamic>)
          .map((e) => FAQ.fromJson(e as Map<String, dynamic>))
          .toList(),
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$FAQsToJson(FAQs instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['title'] = instance.title;
  val['faqs'] = instance.faqs.map((e) => e.toJson()).toList();
  writeNotNull('analytics_params', instance.analyticsParams);
  return val;
}

FAQ _$FAQFromJson(Map<String, dynamic> json) => FAQ(
      question: json['question'] as String,
      answer: json['answer'] as String,
      button: json['button'] == null
          ? null
          : PremiumExperienceButtonDetails.fromJson(
              json['button'] as Map<String, dynamic>),
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$FAQToJson(FAQ instance) {
  final val = <String, dynamic>{
    'question': instance.question,
    'answer': instance.answer,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('button', instance.button?.toJson());
  writeNotNull('analytics_params', instance.analyticsParams);
  return val;
}
