import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/enums/payment_status.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/payments/models/upi_app.dart';
import 'package:praja/features/premium_experience/models/payment_bottom_sheet_response.dart';
import 'package:praja/features/premium_experience/payment_bottom_sheet/payment_bottom_sheet_view_model.dart';
import 'package:praja/features/premium_experience/premium_success_screen/premium_success_screen.dart';
import 'package:praja/features/premium_experience/premium_utils.dart';
import 'package:praja/features/premium_experience/subscription_handler.dart';
import 'package:praja/features/premium_experience/widgets/existing_premium_users_widget.dart';
import 'package:praja/features/premium_experience/widgets/pay_with_upi_app_widget.dart';
import 'package:praja/features/premium_experience/widgets/payment_terms_ui.dart';
import 'package:praja/features/premium_experience/widgets/premium_experience_button_ui.dart';
import 'package:praja/features/premium_experience/widgets/upi_app_picker.dart';
import 'package:praja/features/whatsapp_share/whatsapp_share.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/services/app_state.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

const _selectedItemColor = Color(0xffEAC348);
const _unselectedItemColor = Color(0xffFFEFB7);
const _selectedItemTextColor = Color(0xff5A3D1B);
const _unselectedItemTextColor = Color(0xffB5811E);
const Color _whatsappColor = Color(0xff00A650);

class PaymentBottomSheetBody extends BasePage {
  final String source;
  final String? returnUrl;

  const PaymentBottomSheetBody({
    super.key,
    required this.source,
    this.returnUrl,
  });

  @override
  String get pageName => 'payment_bottom_sheet';

  @override
  bool get pageLoadRequiredForTracking => true;

  @override
  Widget buildContent(BuildContext context) {
    return PaymentBottomSheetBodyInner(source: source);
  }
}

class PaymentBottomSheetBodyInner extends StatefulWidget {
  final String source;
  final String? returnUrl;

  const PaymentBottomSheetBodyInner({
    super.key,
    required this.source,
    this.returnUrl,
  });

  @override
  State<PaymentBottomSheetBodyInner> createState() =>
      _PaymentBottomSheetBodyInnerState();
}

class _PaymentBottomSheetBodyInnerState
    extends State<PaymentBottomSheetBodyInner> with RouteAware {
  late PaymentBottomSheetViewModel viewModel;
  StreamSubscription<AppLifecycleState>? _appStateSubscription;
  late PageViewTracker _pageViewTracker;

  @override
  void initState() {
    super.initState();
    viewModel = context.getViewModel<PaymentBottomSheetViewModel>()..init();
    _pageViewTracker = Provider.of<PageViewTracker>(context, listen: false);
  }

  @override
  dispose() {
    _appStateSubscription?.cancel();
    _appStateSubscription = null;
    super.dispose();
  }

  void _onAppStateChanged(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      viewModel.onUserReturnedToThePage();
      _appStateSubscription?.cancel();
      _appStateSubscription = null;
    }
  }

  Widget _selectedTickMark({required bool isSelected}) {
    return Container(
      height: 18,
      width: 18,
      margin: const EdgeInsets.only(bottom: 2),
      alignment: Alignment.center,
      decoration: const BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
      ),
      child: Icon(
        applyTextScaling: false,
        FontAwesomeIcons.check,
        color: isSelected
            ? _selectedItemTextColor
            : _selectedItemTextColor.withOpacity(0.2),
        size: 12,
      ),
    );
  }

  Widget _subscriptionItem(
      {required PlanItem item, Map<String, dynamic>? params}) {
    final selectedItemId = viewModel.selectedPlanId.value;
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () {
        viewModel.updatedSelectedSubscriptionItemId(item.id);
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Container(
          height: 142,
          decoration: BoxDecoration(
            border: Border.all(
              width: 1.5,
              style: BorderStyle.solid,
              color: item.id == selectedItemId
                  ? _selectedItemColor
                  : _unselectedItemColor,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                height: 42,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: item.id == selectedItemId
                      ? _selectedItemColor
                      : _unselectedItemColor,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _selectedTickMark(isSelected: item.id == selectedItemId),
                    const SizedBox(width: 8),
                    Text(
                      item.durationText,
                      textScaler: const TextScaler.linear(1.0),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: item.id == selectedItemId
                            ? _selectedItemTextColor
                            : _unselectedItemTextColor,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        // ignore: avoid_hardcoded_strings_in_ui
                        '₹${item.amount}',
                        textScaler: const TextScaler.linear(1.0),
                        style: TextStyle(
                          fontSize: 34,
                          fontWeight: FontWeight.bold,
                          color: item.id == selectedItemId
                              ? _selectedItemTextColor
                              : _unselectedItemTextColor,
                        ),
                      ),
                      if (item.perMonthText.isNotEmpty ||
                          item.discountText.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (item.perMonthText.isNotEmpty)
                              Text(
                                item.perMonthText,
                                textScaler: const TextScaler.linear(1.0),
                                style: TextStyle(
                                  fontSize: 14,
                                  decoration: item.showStrikeThrough
                                      ? TextDecoration.lineThrough
                                      : TextDecoration.none,
                                  decorationColor: item.id == selectedItemId
                                      ? _selectedItemTextColor
                                      : _unselectedItemTextColor,
                                  decorationThickness: 2,
                                  fontWeight: FontWeight.w400,
                                  color: item.id == selectedItemId
                                      ? _selectedItemTextColor
                                      : _unselectedItemTextColor,
                                ),
                              ),
                            if (item.discountText.isNotEmpty) ...[
                              const SizedBox(width: 4),
                              Container(
                                alignment: Alignment.center,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Color(item.discountTextBgColor),
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                child: Text(
                                  item.discountText,
                                  textAlign: TextAlign.center,
                                  textScaler: const TextScaler.linear(1.0),
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Color(item.discountTextColor),
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _paymentShareLinkUI({
    required String text,
    required bool sharing,
    void Function()? onShare,
  }) {
    return InkWell(
      onTap: onShare,
      child: Container(
        width: double.infinity,
        height: 48,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: _whatsappColor.withOpacity(0.2),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(4),
            boxShadow: [
              BoxShadow(
                  color: const Color(0xff000000).withOpacity(0.1),
                  offset: const Offset(0, 0),
                  blurRadius: 4,
                  spreadRadius: 0)
            ]),
        child: sharing
            ? const SizedBox(
                height: 16,
                width: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: _whatsappColor,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(
                    FontAwesomeIcons.whatsapp,
                    color: _whatsappColor,
                    size: 22,
                  ),
                  const SizedBox(width: 8),
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      text,
                      textScaler: MediaQuery.textScalerOf(context).clamp(
                        minScaleFactor: 1.0,
                        maxScaleFactor: 1.2,
                      ),
                      style: const TextStyle(
                        color: _whatsappColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                ],
              ),
      ),
    );
  }

  Widget _autoRechargeTextWidget({required String text}) {
    if (text.isEmpty) return const SizedBox();
    return Column(
      children: [
        Text(
          text,
          textScaler: MediaQuery.textScalerOf(context).clamp(
            minScaleFactor: 1.0,
            maxScaleFactor: 1.4,
          ),
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: Color(0xff8F8F8F),
          ),
        ),
        //divider
        const Divider(
          thickness: 1,
          color: Color(0xFFDCDCDC),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _autoRechargeCancelTextWidget({required String text}) {
    return Column(
      children: [
        Row(
          children: [
            IntrinsicWidth(
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  minWidth: 60,
                ),
                child: Container(
                  height: 1,
                  color: const Color(0xFFDCDCDC),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                text,
                textScaler: MediaQuery.textScalerOf(context).clamp(
                  minScaleFactor: 1.0,
                  maxScaleFactor: 1.4,
                ),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xff8F8F8F),
                ),
              ),
            ),
            const SizedBox(width: 8),
            IntrinsicWidth(
              child: ConstrainedBox(
                constraints: const BoxConstraints(
                  minWidth: 60,
                ),
                child: Container(
                  height: 1,
                  color: const Color(0xFFDCDCDC),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  String _getButtonText({PlanItem? selectedPlan, required String textSuffix}) {
    if (selectedPlan == null) return '';
    final suffix = selectedPlan.mandatePresent
        ? selectedPlan.mandatePresentTextSuffix
        : textSuffix;
    // ignore: avoid_hardcoded_strings_in_ui
    return '₹${selectedPlan.amount} $suffix';
  }

  Widget _getBody(BuildContext context) {
    final PaymentBottomSheetState state = viewModel.state.value;
    if (state is PaymentBottomSheetLoadingState) {
      return Container(
        height: 100,
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 24),
        decoration: const BoxDecoration(
          gradient: PremiumUtils.premiumScreenBgGradient,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: const SizedBox(
          height: 24,
          width: 24,
          child: Center(
            child: CircularProgressIndicator(
              color: Color(0xFFBF951E),
              strokeWidth: 3,
            ),
          ),
        ),
      );
    } else if (state is PaymentBottomSheetSuccessState) {
      final response = state.response;
      final rmUser = response.rmUser;
      final rmUserPhoto = rmUser?.photoUrl;
      final existingPremiumUsers = response.existingPremiumUsers;
      final pageParams = {
        'source': widget.source,
        ...response.analyticsParams,
      };
      _pageViewTracker.onPageLoaded(pageParams);
      AppAnalytics.onPaymentSheetLoaded(params: pageParams);
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (response.title.isNotEmpty)
                        Text(
                          response.title,
                          textScaler: const TextScaler.linear(1.0),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Color(response.titleTextColor),
                          ),
                        ),
                      const SizedBox(height: 4),
                      if (response.subtitle.isNotEmpty)
                        Text(
                          response.subtitle,
                          textScaler: const TextScaler.linear(1.0),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Color(response.subtitleTextColor),
                          ),
                        ),
                    ],
                  ),
                ),
                if (rmUser != null)
                  InkWell(
                    customBorder: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(34),
                    ),
                    onTap: () async {
                      //open dialer
                      final Uri phoneLaunchUri = Uri(
                        scheme: 'tel',
                        path: rmUser.phone.toString(),
                      );
                      String errorMessage = context.getString(
                          StringKey.premiumUnableToOpenPhoneErrorMessage,
                          listen: false);
                      if (await canLaunchUrl(phoneLaunchUri)) {
                        AppAnalytics.onInitiatedCallToRm(
                            source: 'payment_bottom_sheet',
                            params: response.analyticsParams);
                        await launchUrl(phoneLaunchUri);
                      } else {
                        AppAnalytics.onFailedCallToRm(
                            source: 'payment_bottom_sheet',
                            reason: 'failed to open dialer',
                            params: response.analyticsParams);
                        Utils.showToast(errorMessage);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                            color: const Color(0xff8F8F8F), width: 1),
                        borderRadius: BorderRadius.circular(34),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xff000000).withOpacity(0.25),
                            offset: const Offset(0, 2),
                            blurRadius: 4,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          if (rmUserPhoto != null)
                            CachedNetworkImage(
                              imageUrl: rmUserPhoto,
                              fadeOutDuration:
                                  const Duration(milliseconds: 300),
                              fadeInDuration: const Duration(milliseconds: 300),
                              errorWidget: (context, url, error) =>
                                  const Icon(Icons.error),
                              imageBuilder: (context, imageProvider) =>
                                  Container(
                                width: 30,
                                height: 30,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  image: DecorationImage(
                                    image: imageProvider,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                            ),
                          const SizedBox(width: 12),
                          const Icon(
                            PrajaIcons.call,
                            color: Color(0xff8F8F8F),
                            size: 20,
                          ),
                          const SizedBox(width: 4),
                        ],
                      ),
                    ),
                  )
              ],
            ),
            if (existingPremiumUsers != null) ...[
              const SizedBox(height: 24),
              ExistingPremiumUsersWidget(
                  existingPremiumUsers: existingPremiumUsers,
                  source: widget.source),
            ],
            const SizedBox(height: 24),
            _autoRechargeTextWidget(text: response.autoRechargeText),
            Wrap(
              spacing: 16.0,
              runSpacing: 16.0,
              children: List.generate(
                response.plans.length,
                (index) {
                  return LiveDataBuilder(
                    liveData: viewModel.selectedPlanId,
                    builder: (context, id) {
                      return SizedBox(
                        width: MediaQuery.of(context).size.width / 2 - 24,
                        child: _subscriptionItem(
                          item: response.plans[index],
                          params: response.analyticsParams,
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            LiveDataBuilder<bool>(
              liveData: viewModel.paymentWhatsappShareLoading,
              builder: (_, sharing) => _paymentShareLinkUI(
                text: response.paymentShareText,
                sharing: sharing,
                onShare: sharing
                    ? () {}
                    : () async {
                        viewModel.onPaymentWhatsappShare();
                        AppAnalytics.onPremiumPaymentLinkShare(
                          source: 'payment_bottom_sheet',
                          planId: viewModel.selectedPlanId.value,
                          pageName: 'payment_bottom_sheet',
                          otherParameters: response.analyticsParams,
                        );
                        try {
                          final String shareText =
                              await viewModel.onCheckoutUrlShareToWhatsapp();
                          if (Platform.isAndroid) {
                            await WhatsappShareAndroid.shareText(shareText);
                          } else {
                            await Share.share(shareText);
                          }
                        } catch (e, stackTrace) {
                          logNonFatal(
                            "Error While Sharing Payment Link",
                            e,
                            stackTrace: stackTrace,
                          );
                          Utils.showToast(localisedErrorMessage(e));
                        } finally {
                          viewModel.onPaymentWhatsappShareComplete();
                        }
                      },
              ),
            ),
            const SizedBox(height: 40),
            _autoRechargeCancelTextWidget(
                text: response.autoRechargeCancelText),
            LiveDataBuilder(
              liveData: viewModel.selectedPlanId,
              builder: (_, id) {
                return Row(
                  children: [
                    LiveDataBuilder<UpiApp?>(
                        liveData: viewModel.preselectedUpiApp,
                        builder: (_, upiApp) {
                          if (upiApp == null) return const SizedBox();
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: PayWithUpiAppWidget(
                              upiApp: upiApp,
                              onTap: () async {
                                final upiApp = await pickUpiApp(context);
                                if (upiApp != null) {
                                  viewModel.onUpiAppSelected(upiApp);
                                }
                              },
                            ),
                          );
                        }),
                    Expanded(
                      child: LiveDataBuilder(
                          liveData: viewModel.paymentStatus,
                          builder: (_, status) {
                            return PremiumExperienceButtonUI(
                              buttonDetails: response.buttonDetails,
                              loading: status == PaymentStatus.pending,
                              onPressed: () {
                                viewModel.onCheckoutButtonClicked();
                              },
                              text: _getButtonText(
                                selectedPlan:
                                    viewModel.getCurrentSelectedPlanItem(),
                                textSuffix: response.buttonDetails.textSuffix,
                              ),
                            );
                          }),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
            PaymentTermsUI(terms: response.terms),
          ],
        ),
      );
    } else if (state is PaymentBottomSheetErrorState) {
      AppAnalytics.onPaymentSheetError(
          source: widget.source, errorMessage: state.errorMessage);
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 24),
        width: double.infinity,
        decoration: const BoxDecoration(
          gradient: PremiumUtils.premiumScreenBgGradient,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            PremiumUtils.premiumCrownGoldIconWidget(iconSize: 48),
            const SizedBox(height: 10),
            Text(
              state.errorMessage,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: PremiumUtils.premiumHeaderTitleColor,
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  Future<void> onEvent(
      BuildContext context, SubscriptionHandlerEvent event) async {
    final paymentFailedText =
        context.getString(StringKey.yourPaymentFailedText, listen: false);
    if (event is SubscriptionHandlerUrlCheckoutInitiated) {
      printDebug("PaymentBottomSheet: Checkout started");
      AppAnalytics.onPremiumPaymentInitiated(
          source: 'payment_bottom_sheet',
          planId: event.planId,
          params: event.analyticsParams);
      _appStateSubscription =
          AppState.lifecycleStateStream.listen(_onAppStateChanged);
      await launchUrl(Uri.parse(event.url),
          mode: LaunchMode.externalApplication);
    } else if (event is SubscriptionHandlerIntentCheckoutInitiated) {
      printDebug("PaymentBottomSheet: Intent Checkout started");
      AppAnalytics.onPremiumPaymentInitiated(
          source: 'payment_bottom_sheet',
          planId: event.planId,
          params: event.analyticsParams);
      _appStateSubscription =
          AppState.lifecycleStateStream.listen(_onAppStateChanged);
      try {
        final uri = Uri.parse(event.intentUrl);
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } catch (e, st) {
        logNonFatalIfAppError(
            "Error launching intent URL in payment bottom sheet", e,
            stackTrace: st);
        AppAnalytics.onPremiumPaymentFailed(
            source: 'payment_bottom_sheet',
            planId: event.planId,
            params: {...event.analyticsParams, 'error': e.toString()});
        Utils.showToast(paymentFailedText);
      }
    } else if (event is SubscriptionHandlerFailure) {
      AppAnalytics.onPremiumPaymentFailed(
        source: 'payment_bottom_sheet',
        planId: event.planId,
        params: event.analyticsParams,
      );
      Utils.showToast(event.message ?? paymentFailedText);
    } else if (event is SubscriptionHandlerCheckoutSuccess) {
      navigateToPremiumSuccessScreen(
        context,
        source: 'payment_bottom_sheet',
        returnNavigationUrl: widget.returnUrl,
      );
    } else if (event is SubscriptionHandlerNotifyError) {
      Utils.showToast(event.message);
    }
  }

  @override
  Widget build(BuildContext context) {
    return LiveDataBuilder<bool>(
      liveData: viewModel.shouldInterceptBackPresses,
      builder: (ctx, shouldIntercept) => PopScope(
        canPop: !shouldIntercept,
        onPopInvoked: (bool popped) async {
          if (popped) {
            viewModel.onPaymentSheetClosed(source: widget.source);
          }

          if (shouldIntercept) {
            await viewModel.onBackPressed();
          }
        },
        child: EventListener<SubscriptionHandlerEvent>(
          eventQueue: viewModel.eventQueue,
          onEvent: onEvent,
          child: LiveDataBuilder<PaymentBottomSheetState>(
            liveData: viewModel.state,
            builder: (context, state) {
              return _getBody(context);
            },
          ),
        ),
      ),
    );
  }
}
