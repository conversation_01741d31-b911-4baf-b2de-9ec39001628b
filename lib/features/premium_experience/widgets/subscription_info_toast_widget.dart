import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:praja/features/premium_experience/models/subscription_info_toast.dart';

class SubscriptionInfoToastWidget extends StatelessWidget {
  final SubscriptionInfoToast subscriptionInfoToast;
  const SubscriptionInfoToastWidget({
    super.key,
    required this.subscriptionInfoToast,
  });

  Color _getBackgroundColor() {
    final SubscriptionInfoToastType infoType = subscriptionInfoToast.type;
    switch (infoType) {
      case SubscriptionInfoToastType.info:
        return const Color.fromRGBO(203, 255, 158, 0.2);
      case SubscriptionInfoToastType.warning:
        return const Color.fromRGBO(255, 255, 158, 0.2);
      case SubscriptionInfoToastType.alert:
        return const Color.fromRGBO(255, 113, 113, 0.1);
      case SubscriptionInfoToastType.quiet:
        return const Color(0xff222222).withOpacity(0.05);
    }
  }

  Color _getTextColor() {
    final SubscriptionInfoToastType infoType = subscriptionInfoToast.type;
    switch (infoType) {
      case SubscriptionInfoToastType.info:
        return const Color(0xff4EA502);
      case SubscriptionInfoToastType.warning:
        return const Color(0xffFFC107);
      case SubscriptionInfoToastType.alert:
        return const Color(0xffF64646);
      case SubscriptionInfoToastType.quiet:
        return const Color(0xff8F8F8F);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 6, right: 6, top: 10, bottom: 10),
      margin: const EdgeInsets.symmetric(horizontal: 14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: _getBackgroundColor(),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (subscriptionInfoToast.label.isNotEmpty) ...[
            Text(
              subscriptionInfoToast.label,
              textScaler: MediaQuery.textScalerOf(context).clamp(
                minScaleFactor: 1.0,
                maxScaleFactor: 1.2,
              ),
              style: GoogleFonts.anekTelugu(
                fontSize: 14,
                height: 2.2,
                letterSpacing: -0.33,
                fontWeight: FontWeight.w600,
                color: const Color(0xff696969),
              ),
            ),
            const SizedBox(width: 4),
          ],
          if (subscriptionInfoToast.text.isNotEmpty)
            Text(
              subscriptionInfoToast.text,
              textScaler: MediaQuery.textScalerOf(context).clamp(
                minScaleFactor: 1.0,
                maxScaleFactor: 1.2,
              ),
              style: GoogleFonts.anekTelugu(
                fontSize: 14,
                height: 2.2,
                letterSpacing: -0.33,
                fontWeight: FontWeight.w600,
                color: _getTextColor(),
              ),
            ),
        ],
      ),
    );
  }
}
