import 'package:flutter/material.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/premium_experience/models/payment_feed_item.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/presentation/view_detector.dart';

class PaymentFeedItemWidget extends StatelessWidget {
  final PaymentFeedItem paymentFeedItem;
  final String source;
  final int index;

  const PaymentFeedItemWidget({
    super.key,
    required this.paymentFeedItem,
    required this.source,
    required this.index,
  });

  Widget _discountText({required BuildContext context}) {
    if (paymentFeedItem.discountText.isEmpty) {
      return const SizedBox();
    }
    return Row(
      children: [
        if (paymentFeedItem.subText.isNotEmpty) const SizedBox(width: 12),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.8),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            paymentFeedItem.discountText,
            textScaler: MediaQuery.textScalerOf(context).clamp(
              minScaleFactor: 1.0,
              maxScaleFactor: 1.2,
            ),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color(paymentFeedItem.discountTextColor),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return ViewDetector(
        uniqueId: 'payment_feed_item_${paymentFeedItem.feedItemId}',
        threshold: 0.5,
        onView: (_) {
          AppAnalytics.onPaymentFeedItemViewed(
              source: source,
              index: index,
              params: paymentFeedItem.analyticsParams);
        },
        builder: (_, __) {
          return GestureDetector(
            onTap: () {
              AppAnalytics.onPaymentFeedItemClicked(
                source: source,
                index: index,
                deeplink: paymentFeedItem.deeplink,
                params: paymentFeedItem.analyticsParams,
              );
              if (paymentFeedItem.deeplink.isNotEmpty) {
                DeeplinkDestination.fromRoute(paymentFeedItem.deeplink)
                    ?.go(context, DeeplinkSource.internalDeeplink);
              }
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xffD8B141),
                    Color(0xffCCA028),
                    Color(0xffE7B42D),
                    Color(0xffF7EFCC),
                    Color(0xffE7B42D),
                    Color(0xffA68320),
                  ],
                  stops: [0.0917, 0.175, 0.2874, 0.5601, 0.7796, 0.8912],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    margin: const EdgeInsets.only(right: 16),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color.fromRGBO(96, 56, 19, 0.8),
                    ),
                    child: ShaderMask(
                      shaderCallback: (Rect bounds) {
                        return const LinearGradient(
                          begin: Alignment.topRight,
                          end: Alignment.bottomLeft,
                          stops: [0.0917, 0.2874, 0.496, 0.6663, 0.8912],
                          colors: [
                            Color.fromRGBO(166, 131, 32, 1),
                            Color.fromRGBO(231, 180, 45, 1),
                            Color.fromRGBO(255, 231, 129, 1),
                            Color.fromRGBO(231, 180, 45, 1),
                            Color.fromRGBO(166, 131, 32, 1),
                          ],
                        ).createShader(bounds);
                      },
                      child: const Icon(
                        PrajaIcons.premium_crown,
                        size: 24,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (paymentFeedItem.text.isNotEmpty) ...[
                          Text(
                            paymentFeedItem.text,
                            textScaler: MediaQuery.textScalerOf(context).clamp(
                              minScaleFactor: 1.0,
                              maxScaleFactor: 1.2,
                            ),
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 2),
                        ],
                        Row(
                          children: [
                            if (paymentFeedItem.subText.isNotEmpty)
                              Expanded(
                                child: Text(
                                  paymentFeedItem.subText,
                                  textScaler:
                                      MediaQuery.textScalerOf(context).clamp(
                                    minScaleFactor: 1.0,
                                    maxScaleFactor: 1.2,
                                  ),
                                  style: const TextStyle(
                                    color: Color(0xff603813),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            _discountText(context: context),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (paymentFeedItem.deeplink.isNotEmpty)
                    const Icon(
                      Icons.arrow_forward_ios,
                      color: Color(0xff603813),
                      size: 20,
                    )
                ],
              ),
            ),
          );
        });
  }
}
