import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/premium_experience/cancel_membership/cancel_membership_helper_widgets.dart';
import 'package:praja/features/premium_experience/premium_success_screen/premium_success_screen.dart';
import 'package:praja/features/premium_experience/premium_utils.dart';
import 'package:praja/features/premium_experience/upgrade/widgets/plan_transition_widget.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/utils/utils.dart';

import 'cancel_flow_downgrade_sheet_view_model.dart';

extension CancelFlowDowngradeSheetExtension on BuildContext {
  Future<void> showCancelFlowDowngradeSheet({required String source}) async {
    await showModalBottomSheet(
      context: this,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      enableDrag: false,
      builder: (context) {
        return Wrap(
          children: [_CancelFlowDowngradeSheetBody(source: source)],
        );
      },
    );
  }
}

class _CancelFlowDowngradeSheetBody extends StatelessWidget {
  final String source;

  const _CancelFlowDowngradeSheetBody({required this.source});

  Widget _getBody({
    required CancelFlowDowngradeSheetViewModel viewModel,
    required BuildContext context,
  }) {
    final state = viewModel.state.value;
    if (state is CancelFlowDowngradeSheetLoading) {
      return const SizedBox(
        height: 270,
        child: Center(
          child: SizedBox(
            height: 30,
            width: 30,
            child: CircularProgressIndicator(color: Colors.black),
          ),
        ),
      );
    } else if (state is CancelFlowDowngradeSheetSuccess) {
      final response = state.response;
      final rmUser = response.rmUser;

      AppAnalytics.onCancelFlowDowngradeSheetViewed(
        source: source,
        params: response.analyticsParams,
      );

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Premium gradient header with title
          Container(
            height: 140,
            width: double.infinity,
            decoration: const BoxDecoration(
              gradient: PremiumUtils.premiumScreenBgGradient,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: Stack(
              children: [
                // Close button on left
                Positioned(
                  left: 16,
                  top: 16,
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: const Icon(
                      Icons.close,
                      color: Color(0xFFD3AF4A),
                      size: 24,
                    ),
                  ),
                ),
                // Premium crown and title in center
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      PremiumUtils.premiumCrownGoldIconWidget(iconSize: 30),
                      const SizedBox(height: 12),
                      Text(
                        response.title,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: PremiumUtils.premiumHeaderTitleColor,
                        ),
                      ),
                    ],
                  ),
                ),
                // Call button on right if RM user is available
                if (rmUser != null)
                  Positioned(
                    right: 16,
                    top: 16,
                    child: CancelMembershipHelperWidgets.rmUserWidget(
                      rmUser: rmUser, // Using the local variable
                      context: context,
                      source: source,
                      analyticsParams: response.analyticsParams,
                    ),
                  ),
              ],
            ),
          ),
          // Sub title
          if (response.subTitle.isNotEmpty) ...[
            const SizedBox(height: 40),
            Text(
              response.subTitle,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFFDAAD2F),
              ),
            ),
          ],
          // Add margin before plan transition widget
          const SizedBox(height: 40),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: PlanTransitionWidget(
              currentPlan: response.currentPlan,
              targetPlan: response.targetPlan,
            ),
          ),

          // Add margin after plan transition widget
          const SizedBox(height: 40),
          // Buttons container
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: const BoxDecoration(color: Colors.white),
            child: LiveDataBuilder(
                liveData: viewModel.isSwitchingPlan,
                builder: (context, isSwitching) {
                  return Column(
                    children: [
                      // Switch Plan Button
                      CancelMembershipHelperWidgets.extendOrSwitchPlanButtonUI(
                        text: response.downgradePlanButton.text,
                        isSwitchingOrExtending: isSwitching,
                        onTap: () {
                          AppAnalytics.onDowngradeMembershipClicked(
                            source: 'cancel_flow_downgrade_sheet',
                            params: {
                              ...response.analyticsParams ?? {},
                              ...response.downgradePlanButton.analyticsParams
                            },
                          );
                          viewModel.onDowngradePlan(
                            apiUrl: response.downgradePlanButton.apiUrl,
                            source: source,
                          );
                        },
                      ),

                      const SizedBox(height: 12),
                      CancelMembershipHelperWidgets.cancelMembershipButtonUI(
                        text: response.cancelMembershipButton.text,
                        isDisabled: isSwitching,
                        onTap: () async {
                          AppAnalytics.onCancelMembershipClicked(
                              source: 'cancel_flow_downgrade_sheet',
                              params: {
                                ...response.analyticsParams ?? {},
                                ...response
                                    .cancelMembershipButton.analyticsParams,
                              });

                          // Navigate using the deeplink from the button details
                          final deeplink =
                              response.cancelMembershipButton.deeplink;
                          if (deeplink.isNotEmpty) {
                            Navigator.of(context).pop();
                            DeeplinkDestination.fromRoute(deeplink)
                                ?.go(context, DeeplinkSource.internalDeeplink);
                          }
                        },
                      ),
                    ],
                  );
                }),
          ),
        ],
      );
    } else if (state is CancelFlowDowngradeSheetError) {
      return SizedBox(
        height: 270,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                state.message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  viewModel.retry();
                },
                child: Text(
                  context.getString(StringKey.retryLabel),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = context.cancelFlowDowngradeSheetViewModel(source: source);
    return PopScope(
      canPop: true,
      onPopInvoked: (bool popped) {
        if (popped) {
          AppAnalytics.onCancelFlowDowngradeSheetClosed(source: source);
        }
      },
      child: Theme(
        //light theme
        data: ThemeData.light(useMaterial3: false),
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          child: EventListener(
            eventQueue: viewModel.eventQueue,
            onEvent: (context, event) async {
              if (event is PlanSwitchedSuccessEvent) {
                Navigator.of(context).pop();
                navigateToPremiumSuccessScreen(context,
                    source: 'cancel_flow_downgrade_sheet');
              } else if (event is FailedToSwitchPlanEvent) {
                Utils.showToast(event.message);
              }
            },
            child: LiveDataBuilder(
              liveData: viewModel.state,
              builder: (context, state) {
                return _getBody(viewModel: viewModel, context: context);
              },
            ),
          ),
        ),
      ),
    );
  }
}
