import 'package:flutter/material.dart';
import 'package:praja/styles.dart';

class PaymentTrustElements extends StatelessWidget {
  final String secureText;
  final String cancelText;

  const PaymentTrustElements(
      {super.key, required this.secureText, required this.cancelText});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
            child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
          const Icon(Icons.verified_user_outlined,
              color: Styles.dimIconColor, size: 18),
          const SizedBox(width: 12.0),
          Text(secureText,
              style: const TextStyle(fontSize: 10, color: Styles.dimTextColor))
        ])),
        Expanded(
            child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
          const Icon(Icons.cancel_outlined,
              size: 18, color: Styles.dimIconColor),
          const SizedBox(width: 12.0),
          Text(cancelText,
              style: const TextStyle(fontSize: 10, color: Styles.dimTextColor))
        ]))
      ],
    );
  }
}
