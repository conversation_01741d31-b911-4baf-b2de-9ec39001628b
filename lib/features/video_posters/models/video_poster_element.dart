import 'package:json_annotation/json_annotation.dart';

part 'video_poster_element.g.dart';

@JsonSerializable()
class VideoPosterElement {
  @Json<PERSON><PERSON>(
      name: 'type',
      defaultValue: VideoPosterElementType.unknown,
      unknownEnumValue: VideoPosterElementType.unknown)
  final VideoPosterElementType type;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'url')
  final String url;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'height')
  final double height;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'width')
  final double width;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'x')
  final double x;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'y')
  final double y;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'border')
  final VideoPosterElementBorder? border;
  @Json<PERSON>ey(name: 'is_user_photo', defaultValue: false)
  final bool isUserPhoto;

  const VideoPosterElement({
    required this.type,
    required this.url,
    required this.height,
    required this.width,
    required this.x,
    required this.y,
    required this.isUserPhoto,
    this.border,
  });

  factory VideoPosterElement.from<PERSON>son(Map<String, dynamic> json) =>
      _$VideoPosterElementFromJson(json);

  Map<String, dynamic> toJson() => _$VideoPosterElementToJson(this);

  @override
  String toString() {
    return 'VideoPosterElement{type: $type, url: $url, height: $height, width: $width, x: $x, y: $y, border: $border, isUserPhoto: $isUserPhoto}';
  }
}

@JsonEnum(valueField: 'value')
enum VideoPosterElementType {
  video('video'),
  photo('photo'),
  unknown('unknown');

  final String value;

  const VideoPosterElementType(this.value);
}

@JsonSerializable()
class VideoPosterElementBorder {
  @JsonKey(name: 'radius', defaultValue: 8.0)
  final double radius;
  @JsonKey(name: 'width', defaultValue: 1.0)
  final double width;
  @JsonKey(name: 'color', defaultValue: 0xFFFFFFFF)
  final int color;

  const VideoPosterElementBorder({
    required this.radius,
    required this.width,
    required this.color,
  });

  factory VideoPosterElementBorder.fromJson(Map<String, dynamic> json) =>
      _$VideoPosterElementBorderFromJson(json);

  Map<String, dynamic> toJson() => _$VideoPosterElementBorderToJson(this);

  @override
  String toString() {
    return 'VideoPosterElementBorder{radius: $radius, width: $width, color: $color}';
  }
}
