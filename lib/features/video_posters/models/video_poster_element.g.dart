// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_poster_element.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VideoPosterElement _$VideoPosterElementFromJson(Map<String, dynamic> json) =>
    VideoPosterElement(
      type: $enumDecodeNullable(_$VideoPosterElementTypeEnumMap, json['type'],
              unknownValue: VideoPosterElementType.unknown) ??
          VideoPosterElementType.unknown,
      url: json['url'] as String,
      height: (json['height'] as num).toDouble(),
      width: (json['width'] as num).toDouble(),
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      isUserPhoto: json['is_user_photo'] as bool? ?? false,
      border: json['border'] == null
          ? null
          : VideoPosterElementBorder.fromJson(
              json['border'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$VideoPosterElementToJson(VideoPosterElement instance) {
  final val = <String, dynamic>{
    'type': _$VideoPosterElementTypeEnumMap[instance.type]!,
    'url': instance.url,
    'height': instance.height,
    'width': instance.width,
    'x': instance.x,
    'y': instance.y,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('border', instance.border?.toJson());
  val['is_user_photo'] = instance.isUserPhoto;
  return val;
}

const _$VideoPosterElementTypeEnumMap = {
  VideoPosterElementType.video: 'video',
  VideoPosterElementType.photo: 'photo',
  VideoPosterElementType.unknown: 'unknown',
};

VideoPosterElementBorder _$VideoPosterElementBorderFromJson(
        Map<String, dynamic> json) =>
    VideoPosterElementBorder(
      radius: (json['radius'] as num?)?.toDouble() ?? 8.0,
      width: (json['width'] as num?)?.toDouble() ?? 1.0,
      color: (json['color'] as num?)?.toInt() ?? 4294967295,
    );

Map<String, dynamic> _$VideoPosterElementBorderToJson(
        VideoPosterElementBorder instance) =>
    <String, dynamic>{
      'radius': instance.radius,
      'width': instance.width,
      'color': instance.color,
    };
