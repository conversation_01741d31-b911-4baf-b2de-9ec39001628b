import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/enums/poster_share_destination.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/posters/models/poster_photos_history_response.dart';
import 'package:praja/features/posters/services/poster_photo_update_config.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/models/video_poster_with_status.dart';
import 'package:praja/features/video_posters/video_posters_service.dart';
import 'package:praja/utils/logger.dart';

@injectable
class VideoPosterCarouselViewModel extends ViewModel {
  final VideoPostersService _videoPostersService;
  final PosterPhotoUpdateConfig _posterPhotoUpdateConfig;

  VideoPosterCarouselViewModel(
    this._videoPostersService,
    this._posterPhotoUpdateConfig,
  );

  final MutableLiveData<VideoPosterCarouselState> _state =
      MutableLiveData(VideoPosterCarouselIdle());
  LiveData<VideoPosterCarouselState> get state => _state;

  final MutableLiveData<int> _progress = MutableLiveData(0);
  LiveData<int> get progress => _progress;

  final MutableEventQueue<VideoPosterCarouselEvent> _eventQueue =
      MutableEventQueue();
  EventQueue<VideoPosterCarouselEvent> get eventQueue => _eventQueue;

  final MutableLiveData<bool> _disappearCameraIcon = MutableLiveData(false);
  LiveData<bool> get disappearCameraIcon => _disappearCameraIcon;

  late int _frameId;
  late int _videoCreativeId;
  late VideoPosterCarousel _carousel;
  Map<String, dynamic>? _sourceAnalyticsParams;
  StreamSubscription? _videoPosterStatusSubscription;
  PosterShareDestination _pendingActionMethod = PosterShareDestination.unknown;
  int? _currentVideoPosterId;
  Timer? _successTimer;
  bool _isInitialized = false;
  String _shareText = "";
  VoidCallback? _onOperationStarted;
  VoidCallback? _onOperationEnded;

  // Auto-generation state management
  bool _hasTriggeredAutoGeneration = false;
  bool _hasUserInitiatedGeneration = false;

  /// Getter to expose pending action method for UI
  PosterShareDestination get pendingActionMethod => _pendingActionMethod;

  void _init({
    required VideoPosterCarousel carousel,
    VoidCallback? onOperationStarted,
    VoidCallback? onOperationEnded,
  }) {
    if (_isInitialized) return;
    _carousel = carousel;
    _frameId = carousel.videoFrameId;
    _videoCreativeId = carousel.videoCreativeId;
    _sourceAnalyticsParams = carousel.analyticsParams;
    _shareText = carousel.shareText;
    _onOperationStarted = onOperationStarted;
    _onOperationEnded = onOperationEnded;
    _isInitialized = true;

    // Initialize camera icon visibility for user photo elements
    _initializeCameraIconVisibility();
  }

  void _initializeCameraIconVisibility() {
    // Check if any element is a user photo
    final hasUserPhoto =
        _carousel.elements.any((element) => element.isUserPhoto);
    if (hasUserPhoto) {
      _disappearCameraIcon.value = false;
      _disappearCameraIconAfterFewSeconds();
    } else {
      _disappearCameraIcon.value = true;
    }
  }

  void _disappearCameraIconAfterFewSeconds() {
    if (_disappearCameraIcon.value) return;
    final durationInMilliseconds =
        _posterPhotoUpdateConfig.premiumPosterCameraDisplayTime;
    Future.delayed(Duration(milliseconds: durationInMilliseconds), () {
      _disappearCameraIcon.value = true;
    });
  }

  void onUserPhotoTapped() {
    _disappearCameraIcon.value = false;
    _disappearCameraIconAfterFewSeconds();
  }

  void onPosterPhotoUpdated(PosterPhoto photo) {
    final videoPosterDeeplink = _carousel.videoPosterDeeplink;
    if (videoPosterDeeplink.isNotEmpty) {
      final updatedReturnUrl = Uri.parse(videoPosterDeeplink).replace(
        queryParameters: {
          ...Uri.parse(videoPosterDeeplink).queryParameters,
          'source': 'video_poster_photo_update',
        },
      );
      _eventQueue
          .push(VideoPosterPhotoUpdatedEvent(updatedReturnUrl.toString()));
    }
  }

  // TODO: Need to Refactor Return URL Logic
  void _triggerLayoutLockedEvent(String method) {
    final deeplink = _carousel.lockedDeeplink;
    if (deeplink.isEmpty) return;
    final uri = Uri.parse(deeplink);
    final queryParams = Map<String, String>.from(uri.queryParameters);
    final path = uri.path;

    final source = queryParams.remove('source');
    final returnUrl = queryParams.remove('return_url');
    final frameId = queryParams.remove('frame_id');

    // Reconstruct finalSource with method
    final finalSource = source != null ? '${source}_$method' : method;
    queryParams['source'] = finalSource;

    if (returnUrl != null) {
      //attach frame_id to return_url if present
      // ignore: avoid_hardcoded_strings_in_ui
      final sep = returnUrl.contains('?') ? '&' : '?';
      final nested = (frameId != null && !returnUrl.contains('frame_id='))
          ? '$returnUrl${sep}frame_id=$frameId&source=$finalSource'
          : '$returnUrl${sep}source=$finalSource';
      queryParams['return_url'] = nested;
    }

    // Generate the final deeplink with all params
    final updatedUri = uri.replace(path: path, queryParameters: queryParams);
    final navigateDeeplink = updatedUri.toString();

    _eventQueue.push(VideoPosterLockedEvent(navigateDeeplink));
  }

  Future<void> onDownloadClicked() async {
    if (_carousel.lockedDeeplink.isNotEmpty) {
      _triggerLayoutLockedEvent('download');
      return;
    }
    await _generateAndDownloadVideoPosters(PosterShareDestination.download);
  }

  Future<void> onShareClicked() async {
    if (_carousel.lockedDeeplink.isNotEmpty) {
      _triggerLayoutLockedEvent('external_share');
      return;
    }
    await _generateAndDownloadVideoPosters(
        PosterShareDestination.externalShare);
  }

  Future<void> onWhatsappClicked() async {
    if (_carousel.lockedDeeplink.isNotEmpty) {
      _triggerLayoutLockedEvent('whatsapp');
      return;
    }
    await _generateAndDownloadVideoPosters(PosterShareDestination.whatsapp);
  }

  void onRetryClicked() {
    if (_currentVideoPosterId != null) {
      fetchVideoDetails(_currentVideoPosterId!);
    } else {
      _generateAndDownloadVideoPosters(_pendingActionMethod);
    }
  }

  void onCloseErrorClicked() {
    _state.value = VideoPosterCarouselIdle();
    _pendingActionMethod = PosterShareDestination.unknown;
    _currentVideoPosterId = null;
    _successTimer?.cancel();

    // Notify parent that operation ended
    _onOperationEnded?.call();
  }

  /// Called when user watches video for specified duration to trigger auto-generation
  Future<void> onUserWatchedVideoTillTriggerDuration() async {
    // Check if auto-generation is enabled
    if (!_carousel.enableAutoGeneration ||
        _hasUserInitiatedGeneration ||
        _hasTriggeredAutoGeneration) {
      return;
    }
    _hasTriggeredAutoGeneration = true;

    printDebug(
        'Starting auto-generation for video poster: frameId=$_frameId, videoCreativeId=$_videoCreativeId, triggerAfter=${_carousel.triggerAutoGenerationAfter}s');

    try {
      // Generate video poster in background without showing UI or listening to status
      await _videoPostersService.autoGenerateVideoPoster(
        _frameId,
        _videoCreativeId,
        additionalParams: _carousel.additionalParams,
      );

      printDebug('Auto-generation triggered successfully');
    } catch (e) {
      printDebug('Auto-generation failed: $e');
      // Reset flag so user can retry if needed
      _hasTriggeredAutoGeneration = false;
    }
  }

  Future<void> _generateAndDownloadVideoPosters(
      PosterShareDestination actionMethod) async {
    final currentState = _state.value;
    if (currentState is VideoPosterCarouselGenerating ||
        currentState is VideoPosterCarouselDownloading) return;

    // Mark that user has initiated generation to prevent auto-generation
    _hasUserInitiatedGeneration = true;

    // Store action method for retry functionality
    _pendingActionMethod = actionMethod;

    // Reset progress
    _progress.value = 0;

    // Start with generating state
    _state.value = VideoPosterCarouselGenerating();

    // Notify parent that operation started
    _onOperationStarted?.call();

    try {
      // Call the service to generate and download video posters
      final videoPosterId =
          await _videoPostersService.generateAndDownloadVideoPosters(
        _frameId,
        _videoCreativeId,
        additionalParams: _carousel.additionalParams,
      );

      _currentVideoPosterId = videoPosterId;

      // Listen to the service stream for status updates
      // The service handles internal socket listening and timer fallbacks
      listenToVideoPosterStatusEvent(videoPosterId);

      printDebug('Video poster generation started for ID: $videoPosterId');
    } catch (e) {
      printDebug('Video poster generation failed: $e');
      _state.value = VideoPosterCarouselError(localisedErrorMessage(e));
      _resetState();

      // Notify parent that operation ended
      _onOperationEnded?.call();
    }
  }

  // Add fetchVideoDetails method for retry functionality
  Future<void> fetchVideoDetails(int videoPosterId) async {
    _state.value = VideoPosterCarouselGenerating();
    try {
      final response =
          await _videoPostersService.getVideoPosterWithStatus(videoPosterId);
      final videoPosterStatus = response.videoPosterStatus;

      printDebug('Fetched video poster status: $videoPosterStatus');

      if (videoPosterStatus == VideoPosterStatus.downloadCompleted) {
        _state.value = VideoPosterCarouselSuccess();
        _triggerActionMethod(_pendingActionMethod, videoPosterId);
        _autoHideSuccess();
        // Don't reset state here - it will be reset when success UI is hidden

        // Notify parent that operation ended (success)
        _onOperationEnded?.call();
      } else {
        // Continue listening for status updates
        listenToVideoPosterStatusEvent(videoPosterId);
        _updateStateFromStatus(videoPosterStatus);
      }
    } catch (e) {
      printDebug('Failed to fetch video details: $e');
      _state.value = VideoPosterCarouselError(localisedErrorMessage(e));

      // Notify parent that operation ended (error)
      _onOperationEnded?.call();
    }
  }

  void listenToVideoPosterStatusEvent(int videoPosterId) {
    _videoPosterStatusSubscription?.cancel();
    _videoPosterStatusSubscription = _videoPostersService.stream
        .where((event) => event.videoPosterId == videoPosterId)
        .listen(
      (event) {
        if (event is VideoPosterStatusUpdate) {
          _handleStatusUpdate(event.videoPosterStatus, videoPosterId);
        } else if (event is VideoPosterDownloadProgressUpdate) {
          _progress.value = event.progress;
        }
      },
      onError: (e) {
        printDebug('Video poster status stream error: $e');
        _state.value = VideoPosterCarouselError(localisedErrorMessage(e));
      },
    );
  }

  void _handleStatusUpdate(VideoPosterStatus status, int videoPosterId) {
    printDebug('Video poster status update: $status');
    _updateStateFromStatus(status);

    if (status == VideoPosterStatus.downloadCompleted) {
      _state.value = VideoPosterCarouselSuccess();
      _triggerActionMethod(_pendingActionMethod, videoPosterId);
      _autoHideSuccess();
      // Don't reset state here - it will be reset when success UI is hidden

      // Notify parent that operation ended (success)
      _onOperationEnded?.call();
    } else if (status == VideoPosterStatus.generationFailed ||
        status == VideoPosterStatus.downloadFailed ||
        status == VideoPosterStatus.downloadCancelled) {
      final errorMessage = _getErrorMessage(status);
      _state.value = VideoPosterCarouselError(errorMessage);
      // Don't reset state here to allow retry functionality

      // Notify parent that operation ended (error)
      _onOperationEnded?.call();
    }
  }

  void _updateStateFromStatus(VideoPosterStatus status) {
    switch (status) {
      case VideoPosterStatus.generationPending:
      case VideoPosterStatus.generationProcessing:
        _state.value = VideoPosterCarouselGenerating();
        break;

      case VideoPosterStatus.downloadEnqueued:
      case VideoPosterStatus.downloadRunning:
        _state.value = VideoPosterCarouselDownloading();
        break;

      case VideoPosterStatus.downloadUndefined:
      case VideoPosterStatus.downloadPaused:
        // Keep current state for these intermediate states
        break;

      default:
        // Handle other states in _handleStatusUpdate
        break;
    }
  }

  String _getErrorMessage(VideoPosterStatus status) {
    switch (status) {
      case VideoPosterStatus.generationFailed:
        return 'Video poster generation failed';
      case VideoPosterStatus.downloadFailed:
        return 'Video poster download failed';
      case VideoPosterStatus.downloadCancelled:
        return 'Video poster download was cancelled';
      default:
        return 'An error occurred';
    }
  }

  void _autoHideSuccess() {
    _successTimer?.cancel();
    _successTimer = Timer(const Duration(seconds: 2), () {
      if (_state.value is VideoPosterCarouselSuccess) {
        _state.value = VideoPosterCarouselIdle();
        _resetState(); // Reset state after hiding success UI

        // Note: We don't call _onOperationEnded here because it was already called
        // when the success state was first set
      }
    });
  }

  void _resetState() {
    _pendingActionMethod = PosterShareDestination.unknown;
    _currentVideoPosterId = null;
    // Note: We don't reset auto-generation state here as it should persist
    // across user interactions until the widget is disposed
  }

  Future<void> _triggerActionMethod(
      PosterShareDestination destination, int videoPosterId) async {
    try {
      final deeplinkUrl = await _recordShare(destination, videoPosterId);
      final downloadFilePath =
          await _videoPostersService.getDownloadedFilePath(videoPosterId);

      // Emit event with destination and deeplink
      // right now we are firing same event for all the destinations
      // but in future if we want to handle them differently, we can add more events
      switch (destination) {
        case PosterShareDestination.whatsapp:
        case PosterShareDestination.externalShare:
        case PosterShareDestination.download:
        case PosterShareDestination.unknown:
          _eventQueue.push(
            VideoPosterShareEvent(
              filePath: downloadFilePath,
              shareText: _shareText,
              destination: destination,
              deeplinkUrl: deeplinkUrl,
            ),
          );
          break;
      }
    } catch (e, st) {
      logNonFatalIfAppError(
        "Failure while video poster share in _triggerActionMethod",
        e,
        stackTrace: st,
      );
    }
  }

  Future<String?> _recordShare(
    PosterShareDestination destination,
    int videoPosterId,
  ) async {
    try {
      final response = await _videoPostersService.recordShare(
        shareDestination: destination.asMethod(),
        videoPosterId: videoPosterId,
        videoFrameId: _frameId,
        videoCreativeId: _videoCreativeId,
        analyticsParams: _sourceAnalyticsParams,
      );

      return response.deeplinkUrl;
    } catch (e, st) {
      logNonFatalIfAppError("Failure while recording video poster share", e,
          stackTrace: st);
      return null;
    }
  }

  @override
  void onDispose() {
    _videoPosterStatusSubscription?.cancel();
    _successTimer?.cancel();
    super.onDispose();
  }
}

/// Represents the different states of video poster carousel during generation and download process
sealed class VideoPosterCarouselState {}

/// Initial state when no operation is in progress
class VideoPosterCarouselIdle extends VideoPosterCarouselState {}

/// State when video poster is being generated on the server
class VideoPosterCarouselGenerating extends VideoPosterCarouselState {}

/// State when video poster is being downloaded with progress tracking
class VideoPosterCarouselDownloading extends VideoPosterCarouselState {}

/// State when video poster generation and download completed successfully
class VideoPosterCarouselSuccess extends VideoPosterCarouselState {}

/// State when an error occurred during generation or download process
class VideoPosterCarouselError extends VideoPosterCarouselState {
  final String message;
  VideoPosterCarouselError(this.message);
}

/// Events for video poster carousel actions
sealed class VideoPosterCarouselEvent {}

/// Event to trigger sharing with specified destination
class VideoPosterShareEvent extends VideoPosterCarouselEvent {
  final String filePath;
  final String shareText;
  final PosterShareDestination destination;

  ///This Deeplink is to redirect user to any of the screen that we want after the share action is completed
  final String? deeplinkUrl;

  VideoPosterShareEvent({
    required this.filePath,
    required this.shareText,
    required this.destination,
    this.deeplinkUrl,
  });
}

class VideoPosterLockedEvent extends VideoPosterCarouselEvent {
  final String deeplink;

  VideoPosterLockedEvent(this.deeplink);
}

class VideoPosterPhotoUpdatedEvent extends VideoPosterCarouselEvent {
  final String deeplink;

  VideoPosterPhotoUpdatedEvent(this.deeplink);
}

extension VideoPosterCarouselViewModelX on BuildContext {
  VideoPosterCarouselViewModel videoPosterCarouselViewModel({
    required String key,
    required VideoPosterCarousel carousel,
    VoidCallback? onOperationStarted,
    VoidCallback? onOperationEnded,
  }) {
    return getViewModel<VideoPosterCarouselViewModel>(
        key: _getKey(carousel, key))
      .._init(
        carousel: carousel,
        onOperationStarted: onOperationStarted,
        onOperationEnded: onOperationEnded,
      );
  }
}

String _getKey(VideoPosterCarousel carousel, String existingKey) {
  final userPhotoElement =
      carousel.elements.firstWhereOrNull((element) => element.isUserPhoto);
  final userPhotoUrl = userPhotoElement?.url ?? '';
  final hashedPhoto = md5.convert(utf8.encode(userPhotoUrl)).toString();
  return "${existingKey}_$hashedPhoto";
}
