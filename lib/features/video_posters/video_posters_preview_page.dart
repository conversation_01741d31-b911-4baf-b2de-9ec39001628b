import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/features/video_posters/widgets/video_posters_preview_widget.dart';
import 'package:praja/models/video.dart';
import 'package:praja/styles.dart';
import 'video_posters_preview_page_view_model.dart';

class VideoPostersPreviewPage extends BasePage {
  final VideoPostersPreviewArgs args;

  const VideoPostersPreviewPage({
    required this.args,
    super.key,
  });

  factory VideoPostersPreviewPage.from(
      {required Video video,
      required FlickManager flickManager,
      required int videoWidth,
      required int videoHeight,
      required String source}) {
    return VideoPostersPreviewPage(
      args: VideoPostersPreviewArgs(
        video: video,
        flickManager: flickManager,
        videoWidth: videoWidth,
        videoHeight: videoHeight,
        source: source,
      ),
    );
  }

  @override
  String get pageName => 'video_posters_preview';

  Widget _getBody(BuildContext context, VideoPostersPreviewPageState state,
      VideoPostersPreviewPageViewModel viewModel) {
    if (state is VideoPostersPreviewPageLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state is VideoPostersPreviewPageError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(state.message, textAlign: TextAlign.center),
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: viewModel.onRetryClicked,
                label: const Text('Retry'),
                icon: const Icon(Icons.refresh),
              ),
            ],
          ),
        ),
      );
    }
    if (state is VideoPostersPreviewPageSuccess) {
      return VideoPostersPreviewWidget(
        sourceVideo: args.video,
        videoFrames: state.videoFrames,
        flickManager: args.flickManager,
      );
    }

    return const SizedBox();
  }

  @override
  Widget buildContent(BuildContext context) {
    final viewModel = context.viewModel(args);
    return EventListener<VideoPostersPreviewPageEvent>(
      eventQueue: viewModel.eventQueue,
      onEvent: (ctx, event) async {
        if (event is StartVideoPlayback) {
          args.flickManager.flickControlManager?.autoResume();
        }
      },
      child: Theme(
        data: ThemeData.dark(useMaterial3: false).copyWith(
          colorScheme: ColorScheme.fromSwatch(
              brightness: Brightness.dark,
              backgroundColor: Colors.black,
              primarySwatch: Styles.circleIndigo),
        ),
        child: LiveDataBuilder<VideoPostersPreviewPageState>(
          liveData: viewModel.state,
          builder: (ctx, state) => Scaffold(
            backgroundColor: Colors.black,
            body: _getBody(context, state, viewModel),
          ),
        ),
      ),
    );
  }
}

extension on BuildContext {
  VideoPostersPreviewPageViewModel viewModel(VideoPostersPreviewArgs args) =>
      ViewModelProvider.of<VideoPostersPreviewPageViewModel>(this)..init(args);
}

class VideoPostersPreviewArgs {
  final Video video;
  final int videoWidth;
  final int videoHeight;
  final String source;
  final FlickManager flickManager;

  const VideoPostersPreviewArgs({
    required this.video,
    required this.videoWidth,
    required this.videoHeight,
    required this.source,
    required this.flickManager,
  });
}
