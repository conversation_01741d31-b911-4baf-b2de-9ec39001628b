import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/features/video_posters/models/video_poster_with_status.dart';
import 'package:praja/exceptions/api_exception.dart';
import 'package:praja/features/bg_downloader/bg_downloader.dart';
import 'package:praja/features/direct_messaging/socket/messaging_socket.dart';
import 'package:praja/features/direct_messaging/socket/messaging_socket_events.dart';
import 'package:praja/features/posters/models/record_poster_share_response.dart';
import 'package:praja/features/video_posters/models/video_poster.dart';
import 'package:praja/features/video_posters/video_poster_id_store.dart';
import 'package:praja/network/network_constants.dart';

@lazySingleton
class VideoPostersService {
  final Dio _dio;
  final MessagingSocket _messagingSocket;
  final BgDownloader _bgDownloader;
  final VideoPosterIdStore _videoPosterIdStore;

  VideoPostersService(
    @Named(ror) this._dio,
    this._messagingSocket,
    this._bgDownloader,
    this._videoPosterIdStore,
  );

  final StreamController<VideoPosterUpdate> _videoPosterUpdate =
      StreamController.broadcast();

  Stream<VideoPosterUpdate> get stream => _videoPosterUpdate.stream;

  StreamSubscription? _socketSubscription;
  // Used as a fallback in case socket messages from backend are missed
  // We wait for some time and request the latest status from API in case there are no socket messages during that time
  final Map<int, Timer> _timers = {};

  Future<int> generateAndDownloadVideoPosters(
      int videoFrameId, int videoCreativeId,
      {required Map<String, dynamic>? additionalParams}) async {
    try {
      final response = await _dio.post(
        '/video-posters/create',
        data: {
          'video_frame_id': videoFrameId,
          'video_creative_id': videoCreativeId,
          ...?additionalParams,
        },
      );
      final videoPoster = VideoPoster.fromJson(response.data);
      if (videoPoster.generationStatus == GenerationStatus.completed) {
        await _updateStateOnVideoPosterGenerationStatusUpdated(videoPoster);
      } else {
        await _listenToVideoPosterStatusEvent(videoPoster.id);
      }
      return videoPoster.id;
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  /// Generate video poster in background without triggering download
  /// Used for auto-generation after user watched video `n seconds`
  Future<int> autoGenerateVideoPoster(int videoFrameId, int videoCreativeId,
      {required Map<String, dynamic>? additionalParams}) async {
    try {
      final response = await _dio.post(
        '/video-posters/create',
        data: {
          'video_frame_id': videoFrameId,
          'video_creative_id': videoCreativeId,
          ...?additionalParams,
        },
      );
      final videoPoster = VideoPoster.fromJson(response.data);
      // Don't listen to status events for auto-generation
      // The existing generateAndDownloadVideoPosters will handle any subsequent user actions
      return videoPoster.id;
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> _listenToVideoPosterStatusEvent(int videoPosterId) async {
    _startTimer(videoPosterId);
    _socketSubscription?.cancel();
    _socketSubscription = _messagingSocket.stream.listen(
      (event) async {
        if (event is VideoPosterStatusUpdated) {
          _timers[videoPosterId]?.cancel();
          await _updateStateOnSocketMessageReceived(event);
          final generationStatus = event.videoPoster.generationStatus;
          if (generationStatus != GenerationStatus.completed &&
              generationStatus != GenerationStatus.failed) {
            _resetTimer(videoPosterId);
          }
        }
      },
    );
  }

  Future<void> _updateStateOnSocketMessageReceived(
      VideoPosterStatusUpdated event) async {
    final generationStatus = event.videoPoster.generationStatus;
    _videoPosterUpdate.add(
      VideoPosterStatusUpdate(
          (generationStatus == GenerationStatus.completed)
              ? VideoPosterStatus.downloadEnqueued
              : generationStatus.toVideoPosterStatus(),
          event.videoPoster.id),
    );
    if (event.videoPoster.generationStatus == GenerationStatus.completed) {
      await _startDownload(event.videoPoster.id, event.videoPoster.video!.url);
    }
  }

  Future<void> _updateStateOnVideoPosterGenerationStatusUpdated(
      VideoPoster response) async {
    final generationStatus = response.generationStatus;
    _videoPosterUpdate.add(
      VideoPosterStatusUpdate(
          (generationStatus == GenerationStatus.completed)
              ? VideoPosterStatus.downloadEnqueued
              : generationStatus.toVideoPosterStatus(),
          response.id),
    );
    if (response.generationStatus == GenerationStatus.completed) {
      await _startDownload(response.id, response.video!.url);
    }
  }

  Future<void> _startDownload(int videoPosterId, String url) async {
    final task = await _bgDownloader.startDownload(url);
    _videoPosterIdStore.saveVideoPosterIdAndTaskId(task.taskId, videoPosterId);
    _bgDownloader.removeListener(
      task.taskId,
      _onProgressChanged,
    );
    if (task.status != DownloadTaskStatus.complete &&
        task.status != DownloadTaskStatus.failed) {
      _bgDownloader.addListener(
        task.taskId,
        _onProgressChanged,
      );
    }
    if (task.status == DownloadTaskStatus.complete) {
      onDownloadStatusUpdate(task.taskId, task.status);
    }
  }

  Future<void> _onProgressChanged(
      String taskId, DownloadTaskStatus status, int progress) async {
    final int? videoPosterId =
        await _videoPosterIdStore.getVideoPosterId(taskId);
    if (videoPosterId == null) {
      return;
    }
    _videoPosterUpdate
        .add(VideoPosterDownloadProgressUpdate(progress, videoPosterId));
    if (status == DownloadTaskStatus.complete ||
        status == DownloadTaskStatus.failed) {
      _bgDownloader.removeListener(taskId, _onProgressChanged);
    }
    onDownloadStatusUpdate(taskId, status);
  }

  Future<void> onDownloadStatusUpdate(
      String taskId, DownloadTaskStatus downloadStatus) async {
    final int? videoPosterId =
        await _videoPosterIdStore.getVideoPosterId(taskId);
    _videoPosterUpdate.add(
      VideoPosterStatusUpdate(
          downloadStatus.toVideoPosterStatus(), videoPosterId!),
    );
  }

  Future<String> getDownloadedFilePath(int videoPosterId) async {
    final taskId = await _videoPosterIdStore.getTaskId(videoPosterId);
    if (taskId == null) {
      throw ArgumentError.value(videoPosterId,
          "No task ID found for video poster ID: $videoPosterId");
    }
    final task = await _bgDownloader.getDownloadTaskForTaskId(taskId);
    return task.downloadPath;
  }

  void _startTimer(int videoPosterId) {
    _timers[videoPosterId]?.cancel();
    _timers[videoPosterId] = Timer(
      const Duration(seconds: 15),
      () async {
        final response = await getVideoPosterWithStatus(videoPosterId);
        _updateStateOnVideoPosterGenerationStatusUpdated(response.videoPoster);
      },
    );
  }

  void _resetTimer(int videoId) {
    _startTimer(videoId);
  }

  Future<VideoPosterWithStatus> getVideoPosterWithStatus(
      int videoPosterId) async {
    try {
      final response = await _dio.get('/video-posters/$videoPosterId');
      final videoPoster = VideoPoster.fromJson(response.data);
      if (videoPoster.generationStatus == GenerationStatus.completed) {
        final taskId = await _videoPosterIdStore.getTaskId(videoPosterId);
        if (taskId == null) {
          throw ArgumentError.value(videoPosterId,
              "No task ID found for the video poster id: $videoPosterId");
        }
        final task = await _bgDownloader.getDownloadTaskForTaskId(taskId);
        final videoPosterStatus = task.status.toVideoPosterStatus();
        return VideoPosterWithStatus(
          videoPoster: videoPoster,
          videoPosterStatus: videoPosterStatus,
        );
      } else {
        return VideoPosterWithStatus(
          videoPoster: videoPoster,
          videoPosterStatus: videoPoster.generationStatus.toVideoPosterStatus(),
        );
      }
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<RecordPosterShareResponse> recordShare({
    required String shareDestination,
    required int videoPosterId,
    required int videoFrameId,
    required int videoCreativeId,
    required Map<String, dynamic>? analyticsParams,
  }) async {
    try {
      final response = await _dio.post(
        '/video-posters/share',
        data: {
          'video_frame_id': videoFrameId,
          'video_creative_id': videoCreativeId,
          'video_poster_id': videoPosterId,
          'method': shareDestination,
          if (analyticsParams != null) 'analytics_params': analyticsParams,
        },
      );
      return RecordPosterShareResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> markAsSeenVideoCreativesBulk(
      Map<String, int> viewedVideoCreatives) async {
    if (viewedVideoCreatives.isEmpty) {
      return;
    }

    try {
      await _dio.post(
        '/video-posters/mark-as-seen-creatives',
        data: {"video_creative_ids": viewedVideoCreatives},
      );
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> markAsSeenVideoFramesBulk(
      Map<String, int> viewedVideoFrames) async {
    if (viewedVideoFrames.isEmpty) {
      return;
    }

    try {
      await _dio.post(
        '/video-posters/mark-as-seen-frames',
        data: {"video_frame_ids": viewedVideoFrames},
      );
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }
}

sealed class VideoPosterUpdate {
  final int videoPosterId;
  VideoPosterUpdate(this.videoPosterId);
}

class VideoPosterStatusUpdate extends VideoPosterUpdate {
  final VideoPosterStatus videoPosterStatus;
  VideoPosterStatusUpdate(this.videoPosterStatus, super.videoPosterId);
}

class VideoPosterDownloadProgressUpdate extends VideoPosterUpdate {
  final int progress;
  VideoPosterDownloadProgressUpdate(this.progress, super.videoPosterId);
}
