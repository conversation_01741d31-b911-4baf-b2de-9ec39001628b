import 'package:carousel_slider/carousel_slider.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/widgets/adaptive_back_arrow_icon.dart';
import 'package:praja/common/widgets/dots_indicator_widget.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/video_posters/models/video_frame.dart';
import 'package:praja/features/video_posters/video_posters_status_page.dart';
import 'package:praja/features/video_posters/widgets/video_poster_player.dart';
import 'package:praja/models/video.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/presentation/view_detector.dart';
import 'package:video_player/video_player.dart';
import 'package:praja/features/video_posters/view_model/video_posters_preview_view_model.dart';
import 'video_poster_widget.dart';

class VideoPostersPreviewWidget extends StatefulWidget {
  final Video sourceVideo;
  final List<VideoFrame> videoFrames;
  final FlickManager? flickManager;

  const VideoPostersPreviewWidget({
    required this.sourceVideo,
    required this.videoFrames,
    this.flickManager,
    super.key,
  });

  @override
  State<VideoPostersPreviewWidget> createState() =>
      _VideoPostersPreviewWidgetState();
}

class _VideoPostersPreviewWidgetState extends State<VideoPostersPreviewWidget> {
  late FlickManager flickManager;
  late VideoPostersPreviewViewModel viewModel;

  @override
  void initState() {
    super.initState();
    flickManager = widget.flickManager ??
        FlickManager(
          videoPlayerController: VideoPlayerController.networkUrl(
            Uri.parse(widget.sourceVideo.url),
            videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
          ),
          autoPlay: false,
        );
    viewModel = context.getViewModel(key: "${widget.key}")
      ..init(widget.sourceVideo, widget.videoFrames,
          flickManager.flickControlManager!.isMute);
  }

  Widget _getBody(BuildContext context, VideoPostersPreviewState state,
      VideoPostersPreviewViewModel viewModel) {
    if (state is VideoPostersPreviewLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    } else if (state is VideoPostersPreviewError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(state.message, textAlign: TextAlign.center),
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: viewModel.onRetryClicked,
                label: const Text('Retry'),
                icon: const Icon(Icons.refresh),
              ),
            ],
          ),
        ),
      );
    } else if (state is VideoPostersPreviewSuccess) {
      return Column(
        children: [
          Expanded(
              child: Stack(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Flexible(
                    flex: 1,
                    child: LayoutBuilder(
                      builder: (_, constraints) => CarouselSlider.builder(
                        itemCount: widget.videoFrames.length,
                        itemBuilder: (ctx, index, _) {
                          return Center(
                            child: ViewDetector(
                              uniqueId: "${widget.sourceVideo.id}",
                              onView: (_) {
                                flickManager
                                    .flickVideoManager?.videoPlayerController
                                    ?.play();
                              },
                              builder: (context, _) {
                                return Padding(
                                  padding: const EdgeInsets.only(top: 60.0),
                                  child: Stack(
                                    children: [
                                      VideoPosterWidget(
                                        sourceVideo: AspectRatio(
                                          aspectRatio: state.videoWidth /
                                              state.videoHeight,
                                          child: VideoPosterPlayer(
                                            video: widget.sourceVideo,
                                            flickManager: flickManager,
                                          ),
                                        ),
                                        videoFrame: widget.videoFrames[index],
                                      ),
                                      Positioned(
                                        bottom: 2,
                                        right: 4,
                                        child: GestureDetector(
                                          onTap: () {
                                            if (flickManager
                                                .flickControlManager!.isMute) {
                                              flickManager.flickControlManager!
                                                  .unmute();
                                              viewModel.updateMuteStatus(false);
                                            } else {
                                              flickManager.flickControlManager!
                                                  .mute();
                                              viewModel.updateMuteStatus(true);
                                            }
                                          },
                                          child: LiveDataBuilder<bool>(
                                            liveData: viewModel.isMute,
                                            builder: (context, isMute) {
                                              return Container(
                                                decoration: BoxDecoration(
                                                  color: Colors.black
                                                      .withOpacity(0.5),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                    50,
                                                  ),
                                                ),
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(6.0),
                                                  child: Icon(
                                                    isMute
                                                        ? Icons
                                                            .volume_off_rounded
                                                        : Icons
                                                            .volume_up_rounded,
                                                    size: 18,
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                );
                              },
                            ),
                          );
                        },
                        options: CarouselOptions(
                          height: constraints.maxHeight,
                          viewportFraction: 1.0,
                          autoPlay: false,
                          enlargeCenterPage: false,
                          enableInfiniteScroll: false,
                          onPageChanged: viewModel.onPageChanged,
                        ),
                      ),
                    ),
                  ),
                  Center(
                    heightFactor: 1.0,
                    child: _getPagerIndicator(context, viewModel),
                  ),
                  Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 14.0,
                      vertical: 8.0,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: ElevatedButton(
                            onPressed: () {
                              int index = viewModel
                                  .dotsIndicatorController.currentIndex;
                              viewModel.onDownloadClicked(
                                widget.videoFrames[index].id,
                                widget.sourceVideo.id,
                                initiateShareMethod: ShareMethod.none,
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF292929),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 10.0),
                              child: Icon(
                                Icons.download,
                                size: 18,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 4.0,
                        ),
                        Expanded(
                          flex: 3,
                          child: ElevatedButton.icon(
                            label: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: Text(
                                context.getString(StringKey.shareLabel),
                              ),
                            ),
                            icon: const Icon(
                              Icons.share,
                              size: 18,
                            ),
                            onPressed: () {
                              int index = viewModel
                                  .dotsIndicatorController.currentIndex;
                              viewModel.onDownloadClicked(
                                  widget.videoFrames[index].id,
                                  widget.sourceVideo.id,
                                  initiateShareMethod: ShareMethod.other);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF292929),
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 4.0,
                        ),
                        Expanded(
                          flex: 3,
                          child: ElevatedButton.icon(
                            label: Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 10.0),
                              child: Text(
                                context.getString(StringKey.whatsappLabel),
                              ),
                            ),
                            icon: const Icon(
                              PrajaIcons.whatsapp_fill,
                              size: 18,
                            ),
                            onPressed: () {
                              int index = viewModel
                                  .dotsIndicatorController.currentIndex;
                              viewModel.onDownloadClicked(
                                widget.videoFrames[index].id,
                                widget.sourceVideo.id,
                                initiateShareMethod: ShareMethod.whatsapp,
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xff25D366),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Positioned(
                child: SafeArea(
                  child: Align(
                    alignment: Alignment.topLeft,
                    child: InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        margin: const EdgeInsets.only(
                          left: 10,
                          top: 20,
                        ),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.black.withOpacity(0.5),
                        ),
                        child: const AdaptiveBackArrowIcon(),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          )),
        ],
      );
    }
    return const SizedBox();
  }

  Widget _getPagerIndicator(
      BuildContext context, VideoPostersPreviewViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 2),
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        color: Colors.grey.shade900,
        borderRadius: BorderRadius.circular(20),
      ),
      child: DotsIndicatorWidget(
          sizeConfig: const DotsIndicatorSizeConfig(indicatorWidth: 100),
          length: widget.videoFrames.length,
          controller: viewModel.dotsIndicatorController,
          selectedColor: Colors.white,
          unselectedColor: const Color(0xff505050)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return EventListener<VideoPostersPreviewEvent>(
      eventQueue: viewModel.eventQueue,
      onEvent: (ctx, event) async {
        if (event is GoToVideoPosterStatusEvent) {
          flickManager.flickVideoManager?.videoPlayerController?.pause();
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoPostersStatusPage(
                source: "video_posters_preview_page",
                videoPosterId: event.videoPosterId,
                initiateShareMethod: event.initiateShareMethod,
              ),
            ),
          );
        }
      },
      child: LiveDataBuilder(
        liveData: viewModel.state,
        builder: (context, state) {
          return _getBody(context, state, viewModel);
        },
      ),
    );
  }

  @override
  void dispose() {
    if (widget.flickManager == null) {
      flickManager.dispose();
    }
    super.dispose();
  }
}
