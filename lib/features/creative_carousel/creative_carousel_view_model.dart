import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/creative_carousel/create_poster_cta_tutorial.dart';
import 'package:praja/features/creative_carousel/creative_carousel_widget_item.dart';
import 'package:praja/features/creative_carousel/models/creative_carousel.dart';
import 'package:praja/features/creative_carousel/models/creative_carousel_item.dart';
import 'package:praja/features/posters/services/poster_service.dart';
import 'package:praja/models/live_config.dart';
import 'package:praja/utils/logger.dart';

@injectable
class CreativeCarouselViewModel extends ViewModel {
  final PosterService _posterService;
  final CreatePosterCtaTutorial _createPosterCtaTutorial;
  final ScrollController scrollController = ScrollController();
  late CreativeCarousel creativeCarousel;
  String? nextPageUrl;
  String? nextPageErrorMessage;

  CreativeCarouselViewModel(this._posterService, this._createPosterCtaTutorial);

  MutableLiveData<CreativeCarouselState> state =
      MutableLiveData<CreativeCarouselState>(CreativeCarouselState([], false));

  final MutableLiveData<LiveConfig?> _liveConfig = MutableLiveData(null);
  LiveData<LiveConfig?> get liveConfig => _liveConfig;

  StreamSubscription? _creativeCarouselLivePollingSubscription;

  bool _isLoading = false;

  bool _isInitialized = false;
  Future<void> _init(CreativeCarousel creativeCarousel) async {
    if (_isInitialized && creativeCarousel == this.creativeCarousel) return;

    _isInitialized = true;
    this.creativeCarousel = creativeCarousel;
    _updateLiveConfig(creativeCarousel.liveConfig);
    nextPageUrl = creativeCarousel.nextPageUrl;
    _updateItems(creativeCarousel.items);
    scrollController.addListener(_onScrollChanged);
    await _createPosterCtaTutorial.init();
    _onTutorialStateChanged();
    _createPosterCtaTutorial.addListener(_onTutorialStateChanged);
  }

  onVisible() {
    final LiveConfig? config = liveConfig.value;
    if (config != null) {
      _startLivePolling();
    }
  }

  DateTime _lastLivePollingTime = DateTime.now();

  void _startLivePolling() {
    _creativeCarouselLivePollingSubscription?.cancel();
    if (liveConfig.value == null) return;

    final diff = DateTime.now().difference(_lastLivePollingTime);
    if (diff.inSeconds > liveConfig.value!.refreshInterval) {
      _fetchLiveCreativeCarousel();
    }

    _creativeCarouselLivePollingSubscription = Stream.periodic(
        Duration(seconds: liveConfig.value!.refreshInterval), (_) {
      _fetchLiveCreativeCarousel();
    }).listen((_) {});
  }

  onInvisible() {
    _stopLivePolling();
  }

  void _stopLivePolling() {
    _creativeCarouselLivePollingSubscription?.cancel();
    _creativeCarouselLivePollingSubscription = null;
  }

  void _updateLiveConfig(LiveConfig? liveConfig) {
    _liveConfig.value = liveConfig;
  }

  void onRetryClicked() {
    nextPageErrorMessage = null;
    _loadNextPage();
  }

  void onHighlightAnimationComplete() {
    _createPosterCtaTutorial.onCreatePosterCtaNudgeShown();
  }

  void onCreatePosterCtaClicked() {
    _createPosterCtaTutorial.onInteractedWithCreatePosterCta();
  }

  void _onTutorialStateChanged() {
    state.value = state.value.copyWith(
      highlightCreatePosterCta:
          !_createPosterCtaTutorial.userLearntCreatePosterCta,
    );
    logDebug(
        "highlightCreatePosterCta: ${state.value.highlightCreatePosterCta}");
  }

  void _updateItems(List<CreativeCarouselItem> items) {
    final nextPageUrl = this.nextPageUrl;
    final nextPageErrorMessage = this.nextPageErrorMessage;
    final newItems = <CreativeCarouselWidgetItem>[];
    for (final item in items) {
      newItems.add(NormalCreativeCarouselWidgetItem(item));
    }
    if (nextPageErrorMessage != null && nextPageErrorMessage.isNotEmpty) {
      newItems.add(ErrorCreativeCarouselWidgetItem(nextPageErrorMessage));
    } else if (nextPageUrl != null && nextPageUrl.isNotEmpty) {
      newItems.add(LoadingCreativeCarouselWidgetItem());
    } else if (creativeCarousel.ctaDeeplink.isNotEmpty &&
        creativeCarousel.ctaText.isNotEmpty) {
      newItems.add(CTACreativeCarouselWidgetItem(
          ctaDescription: creativeCarousel.ctaDescription,
          ctaDeeplink: creativeCarousel.ctaDeeplink,
          ctaText: creativeCarousel.ctaText));
    }

    state.value = state.value.copyWith(items: newItems);
  }

  void _replaceItems(
      {required List<CreativeCarouselItem> items, String? nextPageUrl}) {
    final newItems = <CreativeCarouselWidgetItem>[];
    this.nextPageUrl = nextPageUrl;
    for (final item in items) {
      newItems.add(NormalCreativeCarouselWidgetItem(item));
    }
    if (nextPageUrl != null && nextPageUrl.isNotEmpty) {
      newItems.add(LoadingCreativeCarouselWidgetItem());
    } else if (creativeCarousel.ctaDeeplink.isNotEmpty &&
        creativeCarousel.ctaText.isNotEmpty) {
      newItems.add(
        CTACreativeCarouselWidgetItem(
            ctaDescription: creativeCarousel.ctaDescription,
            ctaDeeplink: creativeCarousel.ctaDeeplink,
            ctaText: creativeCarousel.ctaText),
      );
    }

    state.value = state.value.copyWith(items: newItems);
  }

  void _onScrollChanged() {
    if (_isLoading) return;

    if (scrollController.position.isReachingEnd && nextPageUrl != null) {
      _loadNextPage();
    }
  }

  Future<void> _loadNextPage() async {
    if (_isLoading) return;

    _isLoading = true;
    try {
      final nextPageUrl = this.nextPageUrl;
      if (nextPageUrl != null && nextPageUrl.isNotEmpty) {
        final response =
            await _posterService.getCreativeCarouselNextPage(nextPageUrl);
        this.nextPageUrl = response.nextPageUrl;
        final responseItems = response.items;
        final existingItemIds = creatives.map((e) => e.id).toSet();
        final newItems = responseItems
            .where((e) => !existingItemIds.contains(e.id))
            .toList();
        _updateItems([...creatives, ...newItems]);
      }
    } catch (e, stackTrace) {
      logNonFatalIfAppError(
          "Error while fetching next page of creative carousel - $nextPageUrl",
          e,
          stackTrace: stackTrace);
      nextPageErrorMessage = localisedErrorMessage(e);
      _updateItems(creatives);
    } finally {
      _isLoading = false;
    }
  }

  Future<void> _fetchLiveCreativeCarousel() async {
    try {
      if (liveConfig.value == null) return;
      final response = await _posterService.getLiveCreativeCarousel(
          url: liveConfig.value!.pollingUrl);
      _replaceItems(items: response.items, nextPageUrl: response.nextPageUrl);
      _lastLivePollingTime = DateTime.now();
      _updateLiveConfig(response.liveConfig);
    } catch (e, stackTrace) {
      logNonFatalIfAppError("Error while fetching live creative carousel", e,
          stackTrace: stackTrace);
      nextPageErrorMessage = localisedErrorMessage(e);
      _updateItems(creatives);
    }
  }

  List<CreativeCarouselItem> get creatives => state.value.items
      .whereType<NormalCreativeCarouselWidgetItem>()
      .map((e) => e.item)
      .toList();

  @override
  void onDispose() {
    scrollController.dispose();
    _stopLivePolling();
    _createPosterCtaTutorial.removeListener(_onTutorialStateChanged);
    super.onDispose();
  }
}

class CreativeCarouselState {
  final List<CreativeCarouselWidgetItem> items;
  final bool highlightCreatePosterCta;

  CreativeCarouselState(this.items, this.highlightCreatePosterCta);

  CreativeCarouselState copyWith(
      {List<CreativeCarouselWidgetItem>? items,
      bool? highlightCreatePosterCta}) {
    return CreativeCarouselState(items ?? this.items,
        highlightCreatePosterCta ?? this.highlightCreatePosterCta);
  }
}

extension CreativeCarouselViewModelX on BuildContext {
  CreativeCarouselViewModel creativeCarouselViewModel(
          CreativeCarousel creativeCarousel) =>
      getViewModel<CreativeCarouselViewModel>(
          key: "${creativeCarousel.feedItemId}-${creativeCarousel.title}")
        .._init(creativeCarousel);
}

extension on ScrollPosition {
  bool get isAtEnd => extentAfter == 0;
  bool get isReachingEnd {
    if (userScrollDirection == ScrollDirection.forward) return false;
    if (isAtEnd) return true;
    return extentAfter < 2 * viewportDimension;
  }
}
