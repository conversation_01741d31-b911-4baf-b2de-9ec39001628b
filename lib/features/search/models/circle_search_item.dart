import 'package:json_annotation/json_annotation.dart';
import 'package:praja/features/search/models/search_result.dart';
import 'package:praja/models/v2/circle_background.dart';
import 'package:praja/models/v2/photo.dart';

part 'circle_search_item.g.dart';

@JsonSerializable()
class CircleSearchItem extends SearchResultItem {
  @Json<PERSON>ey(name: 'id')
  final int id;

  @Json<PERSON>ey(name: 'name')
  final String name;

  @<PERSON>son<PERSON><PERSON>(name: 'name_en')
  final String nameEn;

  @Json<PERSON>ey(name: 'level')
  final String level;

  @JsonKey(name: 'level_verbose')
  final String levelVerbose;

  @<PERSON>son<PERSON>ey(name: 'type_of_organisation')
  final String typeOfOrganisation;

  @J<PERSON><PERSON><PERSON>(name: 'circle_background')
  final CircleBackground? circleBackground;

  @Json<PERSON>ey(name: 'circle_type')
  final String circleType;

  @<PERSON>son<PERSON>ey(name: 'is_user_joined')
  final bool isUserJoined;

  @<PERSON><PERSON><PERSON>ey(name: 'members_count')
  final int membersCount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'photo')
  final Photo? photo;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'profile_photo')
  final Photo? profilePhoto;

  // @J<PERSON><PERSON><PERSON>(name: 'banner')
  // final String? banner;

  const CircleSearchItem({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.level,
    required this.levelVerbose,
    required this.typeOfOrganisation,
    required this.circleBackground,
    required this.circleType,
    required this.isUserJoined,
    required this.membersCount,
    required this.photo,
    required this.profilePhoto,
    // required this.banner,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        nameEn,
        level,
        levelVerbose,
        typeOfOrganisation,
        circleBackground,
        circleType,
        isUserJoined,
        membersCount,
        photo,
        profilePhoto,
        // banner,
      ];

  factory CircleSearchItem.fromJson(Map<String, dynamic> json) =>
      _$CircleSearchItemFromJson(json);

  Map<String, dynamic> toJson() => _$CircleSearchItemToJson(this);
}
