import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:praja/constants/hive_type_ids.dart';
import 'package:praja/services/hive.dart';
import 'package:praja/utils/utils.dart';
import 'package:yaml/yaml.dart';

import 'lang_code.dart';
import 'string_key.dart';

part 'string_store.g.dart';

class StringStore {
  final LangCode _langCode;
  final Box<String> _normalStrings;
  final Box<PluralizedString> _pluralizedStrings;

  StringStore(this._langCode, this._normalStrings, this._pluralizedStrings);

  LangCode get langCode => _langCode;

  String getString(StringKey stringKey,
      {Map<String, dynamic> parameters = const {}}) {
    String value = _normalStrings.get(stringKey.value) ?? '';
    parameters.forEach((key, paramValue) {
      value = value.replaceAll('%{$key}', paramValue.toString());
    });
    return value;
  }

  String getPluralizedString(StringKey stringKey, int count,
      {Map<String, dynamic> parameters = const {}}) {
    PluralizedString? value = _pluralizedStrings.get(stringKey.value);
    if (value == null) {
      return '';
    }
    String pluralizedValue;
    if (count == 0) {
      pluralizedValue = value.zero ?? value.other ?? '';
    } else if (count == 1) {
      pluralizedValue = value.one ?? value.other ?? '';
    } else {
      pluralizedValue = value.other ?? '';
    }
    parameters.forEach((key, value) {
      pluralizedValue = pluralizedValue.replaceAll('%{$key}', value.toString());
    });
    return pluralizedValue;
  }
}

extension StringStoreX on AssetBundle {
  Future<StringStore> loadForLocale(String isoCode) async {
    final hive = GetIt.I.get<HiveDelegate>();
    final normalStrings = await hive.openBox<String>('normal_strings_$isoCode');
    final pluralizedStrings =
        await hive.openBox<PluralizedString>('pluralized_strings_$isoCode');
    // load from assets yaml into boxes if not already done for the version
    final version = await Utils.getAppVersion();
    if (normalStrings.get('version') != version || kDebugMode) {
      await normalStrings.clear();
      await pluralizedStrings.clear();
      await _loadFromAssets(
          isoCode: isoCode,
          normalStrings: normalStrings,
          pluralizedStrings: pluralizedStrings);
      normalStrings.put('version', version);
    }

    return StringStore(
        LangCode.fromIsoCode(isoCode), normalStrings, pluralizedStrings);
  }

  Future<void> _loadFromAssets(
      {required String isoCode,
      required Box<String> normalStrings,
      required Box<PluralizedString> pluralizedStrings}) async {
    // load from assets yaml into boxes
    final assetFilePath = 'assets/strings/$isoCode.yaml';

    final yamlAsString = await loadString(assetFilePath);
    final parsed = (await compute(loadYaml, yamlAsString)) as YamlMap;
    parsed.forEach((key, value) {
      if (value is String) {
        normalStrings.put(key, value);
      } else if (value is YamlMap) {
        pluralizedStrings.put(key, PluralizedString.fromJson(value.toMap()));
      }
    });
  }
}

@JsonSerializable()
@HiveType(typeId: HiveTypeId.pluralizedStrings)
class PluralizedString {
  @HiveField(0)
  final String? zero;
  @HiveField(1)
  final String? one;
  @HiveField(2)
  final String? other;

  PluralizedString(
      {required this.zero, required this.one, required this.other});

  factory PluralizedString.fromJson(Map<String, dynamic> json) =>
      _$PluralizedStringFromJson(json);

  Map<String, dynamic> toJson() => _$PluralizedStringToJson(this);

  @override
  String toString() {
    return 'PluralizedString{zero: $zero, one: $one, other: $other}';
  }
}

extension on YamlMap {
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    forEach((key, value) {
      map[key.toString()] = value;
    });
    return map;
  }
}
