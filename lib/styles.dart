import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class Styles {
  static const MaterialColor circleIndigo = MaterialColor(
    0xFF3F51B5,
    <int, Color>{
      50: Color(0xFFE8EAF6),
      100: Color(0xFFC5CBE9),
      200: Color(0xFF9FA8DA),
      300: Color(0xFF7985CB),
      400: Color(0xFF5C6BC0),
      500: Color(0xFF3F51B5),
      600: Color(0xFF394AAE),
      700: Color(0xFF3140A5),
      800: Color(0xFF29379D),
      900: Color(0xFF1B278D),
    },
  );

  static const MaterialColor gold = MaterialColor(
    0xFFCFA500,
    <int, Color>{
      50: Color(0xFFFFFBEA),
      100: Color(0xFFFFF2BF),
      200: Color(0xFFFFE57E),
      300: Color(0xFFFFD83E),
      400: Color(0xFFFCCA00),
      500: Color(0xFFF4DA57),
      600: Color(0xFFB38F00),
      700: Color(0xFF866C00),
      800: Color(0xFF5A4800),
      900: Color(0xFF2D2400),
    },
  );

  static const MaterialColor silver = MaterialColor(
    0xFFA6A6A6,
    <int, Color>{
      50: Color(0xFFF9F9F9),
      100: Color(0xFFECECEC),
      200: Color(0xFFD2D2D2),
      300: Color(0xFFBFBFBF),
      400: Color(0xFFB3B3B3),
      500: Color(0xFFA6A6A6),
      600: Color(0xFF828282),
      700: Color(0xFF5F5F5F),
      800: Color(0xFF3B3B3B),
      900: Color(0xFF181818),
    },
  );

  static const Color circleOrange = Color(0xFFFF641A);
  static const Color resendGrey = Color(0xFFDCDCDC);
  static const Color whatsappColor = Color(0xFF00E676);
  static const Color whatsappDarkColor = Color(0xff00A650);

  // static const Color truecallerColor = Color(0xFF0088FF);

  static const Color badgeTextDark = Color(0xff515151);
  static const Color backgroundBlue = Color(0xffEAEFF2);

  static const Color dimIconColor = Color(0xFF8899A6);
  static const Color dimTextColor = Color(0xFF667887);

  static const LinearGradient goldLinearGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xffFFD64A),
        Color(0xffFEDE58),
        Color(0xffFFF5A8),
      ]);

  static const LinearGradient silverLinearGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xffC2C2C2),
        Color(0xffF0F0F0),
      ]);

  static Color hexToColor(String code) {
    return Color(int.parse(code.substring(0, 6), radix: 16) + 0xFF000000);
  }

  static String colorToHex(Color color) {
    return '#${color.value.toRadixString(16)}';
  }

  static Gradient backgroundGoldGradient() => const LinearGradient(
        colors: [
          Color(0xFFA68320),
          Color(0xFFE7B42D),
          Color(0xFFFFEA8E),
          Color(0xFFE7B42D),
          Color(0xFFA68320),
        ],
        stops: [0.0971, 0.2874, 0.496, 0.6663, 0.8912],
        begin: Alignment(0.8, 0.9),
        end: Alignment(-0.8, -0.9),
      );

  /// Gold Gradient for Border
  ///
  /// Usages: LeadSectionDetailsWidget Border
  static Gradient borderGoldGradient() => const LinearGradient(
        colors: [
          Color(0xFFA68320),
          Color(0xFFE7B42D),
          Color(0xFFFFFFFF),
          Color(0xFFE7B42D),
          Color(0xFFA68320),
        ],
        stops: [0.0917, 0.2874, 0.496, 0.6663, 0.8912],
        begin: Alignment(0.8, 0.9),
        end: Alignment(-0.8, -0.9),
      );

  /// Gold Gradient with Black Shades
  ///
  /// Usages: LeadSectionDetailsWidget Divider
  static Gradient dividerGoldGradient() => const LinearGradient(
        colors: [
          Color(0xFF171106),
          Color(0xFF7D6436),
          Color(0xFFE3B662),
          Color(0xFFFFFFFF),
          Color(0xFFB4904E),
          Color(0xFF7D6436),
          Color(0xFF201809),
        ],
        stops: [0.02, 0.115, 0.315, 0.535, 0.755, 0.935, 0.995],
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
      );

  /// Gold Gradient for Button in Premium Experience
  ///
  static Gradient premiumExperienceButtonGoldGradient() => const LinearGradient(
        transform: GradientRotation(-0.4),
        colors: [
          Color(0xffD8B141),
          Color(0xffCCA028),
          Color(0xffE7B42D),
          Color(0xffF7EFCC),
          Color(0xffE7B42D),
          Color(0xffA68320),
        ],
        stops: [0.0917, 0.175, 0.2874, 0.5601, 0.7796, 0.8912],
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
      );

  static TextStyle premiumScreenHeadingTextStyle() => GoogleFonts.anekTelugu(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.33,
        height: 2.2,
        color: const Color(0xffAA6F1C),
      );
}
