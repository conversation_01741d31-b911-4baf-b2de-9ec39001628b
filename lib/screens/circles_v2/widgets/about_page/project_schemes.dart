import 'package:flutter/material.dart';
import 'package:praja/models/project_section.dart';
import 'package:praja/screens/circles_v2/widgets/about_page/project_schemes_card.dart';

class ProjectAndSchemes extends StatelessWidget {
  final ProjectSection projectsSection;
  const ProjectAndSchemes({
    Key? key,
    required this.projectsSection,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 15.0, bottom: 22),
          child: Text(
            projectsSection.headerText.isNotEmpty
                ? projectsSection.headerText
                : 'Projects & Schemes',
            textScaleFactor: 1,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Container(
          height: 409,
          margin: EdgeInsets.only(bottom: 50, right: 15),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            itemCount: projectsSection.projects.length,
            itemBuilder: (context, index) {
              return ProjectSchemesCard(
                project: projectsSection.projects[index],
                projectSection: projectsSection,
                isProjectShareCard: false,
              );
            },
          ),
        ),
      ],
    );
  }
}
