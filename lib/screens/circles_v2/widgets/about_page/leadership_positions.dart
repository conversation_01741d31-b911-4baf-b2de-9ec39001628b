import 'package:flutter/material.dart';
import 'package:praja/enums/gradient_alignment.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/models/leadership_positions.dart';
import 'package:praja/presentation/app_icons.dart';
import 'package:praja/screens/circles_v2/widgets/about_page/leaders_positions_view_all.dart';
import 'package:praja/screens/circles_v2/widgets/about_page/leadership_position.dart';

class LeadershipPositions extends StatelessWidget {
  final LeaderPositions leadershipPositions;
  final Circle circle;
  final bool viewAllScreen;

  const LeadershipPositions({
    super.key,
    required this.circle,
    required this.leadershipPositions,
    this.viewAllScreen = false,
  });

  Alignment gradientsAlignment(GradientAlignment alignment) {
    double x = 0.0;
    double y = 0.0;
    switch (alignment) {
      case GradientAlignment.BEGIN:
        //set defaults
        x = 0.0;
        y = 1.0;
        final gradientDirection =
            leadershipPositions.gradients.gradientDirection;
        if (gradientDirection != null) {
          x = gradientDirection.beginX;
          y = gradientDirection.beginY;
        }
        break;
      case GradientAlignment.END:
        //set defaults
        x = 0.0;
        y = 1.0;
        final gradientDirection =
            leadershipPositions.gradients.gradientDirection;
        if (gradientDirection != null) {
          x = gradientDirection.endX;
          y = gradientDirection.endY;
        }
        break;
    }
    return Alignment(x, y);
  }

  getPositionsGradient() {
    final gradientValues =
        leadershipPositions.gradients.backgroundGradientValues;
    return LinearGradient(
      begin: gradientsAlignment(GradientAlignment.BEGIN),
      end: gradientsAlignment(GradientAlignment.END),
      colors: gradientValues != null && gradientValues.colors.isNotEmpty
          ? gradientValues.colors
          : [Colors.blue, Colors.blue],
      stops: gradientValues != null && gradientValues.stops != null
          ? gradientValues.stops
          : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 36.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 15,
              bottom: 22,
            ),
            child: Text(
              leadershipPositions.headerText.isNotEmpty
                  ? leadershipPositions.headerText
                  : 'Leadership Positions',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 15.0, right: 15.0),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(right: 36, left: 36),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.15),
                        offset: const Offset(1, 1),
                        blurRadius: 4,
                        spreadRadius: 1,
                      )
                    ],
                    gradient: getPositionsGradient(),
                  ),
                  width: MediaQuery.of(context).size.width,
                  child: leadershipPositions.positions.isNotEmpty
                      ? Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                  top: 20.0, bottom: 20.0),
                              child: ListView.separated(
                                separatorBuilder: (context, index) =>
                                    const SizedBox(height: 33),
                                itemCount: leadershipPositions.positions.length,
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  return LeadershipPosition(
                                    leaderPosition:
                                        leadershipPositions.positions[index],
                                  );
                                },
                              ),
                            ),
                            (leadershipPositions.viewAllPositions == true ||
                                    viewAllScreen)
                                ? const Divider(
                                    color: Colors.grey,
                                    thickness: 0.5,
                                  )
                                : const SizedBox(),
                            !viewAllScreen
                                ? leadershipPositions.viewAllPositions
                                    ? InkWell(
                                        onTap: () {
                                          AppAnalytics.logEvent(
                                            name: 'view_all_positions',
                                            parameters: {
                                              'circle_id': circle.id,
                                              'circle_name': circle.name,
                                            },
                                          );
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (BuildContext context) =>
                                                  ViewAllLeaderPositions(
                                                circle: circle,
                                              ),
                                            ),
                                          );
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.only(
                                              top: 14.0, bottom: 32),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              Text(
                                                leadershipPositions
                                                        .viewAllButtonText ??
                                                    "View all Positions",
                                                style: const TextStyle(
                                                  fontSize: 13,
                                                  color: Color(0xff3F51B5),
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              const Padding(
                                                padding: EdgeInsets.only(
                                                    left: 8.0, bottom: 2),
                                                child: Icon(
                                                  AppIcons.view_all_positions,
                                                  size: 10,
                                                  color: Color(0xff3F51B5),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                    : const SizedBox()
                                : InkWell(
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          bottom: 30.0, top: 14),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          const Padding(
                                            padding: EdgeInsets.only(
                                                right: 8.0, bottom: 2),
                                            child: Icon(
                                              AppIcons.back_icon,
                                              color: Color(0xff3F51B5),
                                              size: 10,
                                            ),
                                          ),
                                          Text(
                                            context.getString(
                                                StringKey.goBackLabel),
                                            style: const TextStyle(
                                              fontSize: 13,
                                              color: Color(0xff3F51B5),
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                          ],
                        )
                      : const SizedBox(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
