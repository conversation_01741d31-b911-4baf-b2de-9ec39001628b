import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_pagewise/flutter_pagewise.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/post/common/post_actions.dart';
import 'package:praja/features/post/enums/post_options.dart';
import 'package:praja/features/post/extensions/post_sheet.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/models/post.dart';
import 'package:praja/models/user.dart';
import 'package:praja/models/v2/comment.dart';
import 'package:praja/presentation/app_user_avatar.dart';
import 'package:praja/presentation/nav_bar.dart';
import 'package:praja/screens/posts/comment.dart';
import 'package:praja/screens/posts/list_item.dart';
import 'package:praja/screens/posts/speech_dialog.dart';
import 'package:praja/services/post/post_service.dart';
import 'package:praja/services/user.dart';
import 'package:praja/shimmers/post.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets.dart';
import 'post_bloc/post_bloc.dart';

class PostDetail extends BasePage {
  static const String tag = '/posts';

  final int? id;
  final String? hashId;
  final Post? preloadedPost;
  final Circle? circle;
  final bool isShare;
  final VoidCallback? onShare;
  final User? user;
  final String? source;
  final String heroTag;
  final bool requestCommentBoxFocusNode;

  const PostDetail({
    super.key,
    this.id,
    this.hashId,
    this.source,
    this.preloadedPost,
    this.circle,
    this.isShare = false,
    this.onShare,
    this.user,
    this.heroTag = "post_detail",
    this.requestCommentBoxFocusNode = false,
  });

  @override
  Widget buildContent(BuildContext context) {
    return PostDetailInner(
      id: id,
      hashId: hashId,
      preloadedPost: preloadedPost,
      circle: circle,
      isShare: isShare,
      onShare: onShare,
      user: user,
      source: source,
      heroTag: heroTag,
      requestCommentBoxFocusNode: requestCommentBoxFocusNode,
    );
  }

  @override
  String get pageName => "post";
}

class PostDetailInner extends StatefulWidget {
  final int? id;
  final String? hashId;
  final Post? preloadedPost;
  final Circle? circle;
  final bool isShare;
  final VoidCallback? onShare;
  final User? user;
  final String heroTag;
  final bool requestCommentBoxFocusNode;

  final String? source;

  const PostDetailInner({
    super.key,
    this.id,
    this.hashId,
    this.source,
    this.preloadedPost,
    this.circle,
    this.isShare = false,
    this.onShare,
    this.user,
    required this.heroTag,
    this.requestCommentBoxFocusNode = false,
  });

  @override
  State<PostDetailInner> createState() => _PostDetailInnerState();
}

class _PostDetailInnerState extends State<PostDetailInner> {
  late int? id;
  late String? hashId;
  late Post? preloadedPost;
  late Post _post;
  late TextEditingController textEditingController;
  late FocusNode commentFocusNode;
  late ScrollController _postScrollController;
  late GlobalKey _postEndExtentKey;
  late PagewiseLoadController _commentsPageLoadController;
  late PostBloc postBloc;
  late PostService _postService;

  int perPageCount = 10;
  int? lastCommentId;
  bool showCommentsBox = false;
  bool _loading = true;
  bool postDetailFailed = false;
  String errorMessage = "";
  Map<String, dynamic> _customProperties = {};
  List<String> _userPermissions = [];

  AssetImage uploadProfilePicImage =
      const AssetImage("assets/images/icons/Green-Blank-profile.png");
  AssetImage appShareIcon =
      const AssetImage("assets/images/icons/post-share-app-icon.png");

  @override
  void initState() {
    super.initState();
    id = widget.id;
    hashId = widget.hashId;
    preloadedPost = widget.preloadedPost;
    commentFocusNode = FocusNode();
    _postScrollController = ScrollController();
    _postEndExtentKey = GlobalKey();
    _postService = GetIt.I.get<PostService>();
    if (preloadedPost != null) {
      _post = preloadedPost!;
      _customProperties = preloadedPost!.customProperties;
    }
    postBloc = BlocProvider.of<PostBloc>(context);
    textEditingController = TextEditingController();
    _loadPost(showShareCardPopUp: true);
    _commentsPageLoadController = PagewiseLoadController(
      pageSize: perPageCount,
      pageFuture: _loadPostCommentsPage,
    );
    AppAnalytics.logEvent(name: "visited_post_page", parameters: {
      "source": widget.source,
      "post_id": id.toString(),
      "hash_id": hashId.toString(),
      ..._customProperties
    });
    Utils.getUserPermissions().then((list) {
      _userPermissions = list;
    });
  }

  Future<List<Comment>> _loadPostCommentsPage(int? pageIndex) async {
    final pageIndex0 = pageIndex ?? 0;
    try {
      return await _postService.fetchPostCommentsPage(
        _post.id,
        pageIndex0 * perPageCount,
        perPageCount,
        lastCommentId,
      );
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
      return [];
    }
  }

  @override
  void dispose() {
    commentFocusNode.dispose();

    super.dispose();
  }

  void _loadPost({bool showShareCardPopUp = false}) async {
    if (mounted) {
      setState(() => _loading = true);
    }

    Post? post;
    final id = this.id;
    final hashId = this.hashId;
    try {
      if (id != null && id != 0) {
        post = await _postService.fetchByID(id);
      } else if (hashId != null && hashId.isNotEmpty) {
        post = await _postService.fetchByHashID(hashId);
      }

      if (post != null) {
        //updating the post being fetched from api call in detail page so that, this post will also be  updated in feed
        // page also
        if (mounted) {
          postBloc.add(PostUpdated(post));
          setState(() {
            final Post? postInstance = post;
            if (postInstance != null) {
              _post = postInstance;
            }
            showCommentsBox = _post.commentsEnabled;
            _customProperties = _post.customProperties.isEmpty
                ? _customProperties
                : _post.customProperties;
            _loading = false;
          });
        }
        if (showShareCardPopUp) {
          showPostShareCard();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          errorMessage = localisedErrorMessage(e);
          postDetailFailed = true;
        });
      }
    }
  }

  afterProfileUpdateCallback(Post post) {
    setState(() {
      _post = post;
    });
  }

  showPostCardDialogue() async {
    if (mounted) {
      Utils.showPostShareCard(
          context, uploadProfilePicImage, appShareIcon, _post);
    }
  }

  showPostShareCard() {
    if (widget.source == "Post Creation" && _post.showPostShareCard) {
      WidgetsBinding.instance
          .addPostFrameCallback((_) => showPostCardDialogue());
    }
  }

  void scrollToComments() {
    RenderSliverToBoxAdapter? commentsBoxRenderBox =
        _postEndExtentKey.currentContext?.findRenderObject()
            as RenderSliverToBoxAdapter?;
    if (commentsBoxRenderBox == null || commentsBoxRenderBox.geometry == null) {
      return;
    }
    double offsetToCommentsBox = commentsBoxRenderBox.geometry!.scrollExtent;
    _postScrollController.animateTo(
      offsetToCommentsBox - 20,
      curve: Curves.easeOut,
      duration: const Duration(milliseconds: 300),
    );
  }

  Future<void> _confirmDelete() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            context.getString(StringKey.doYouWantToDeletePostText),
            style: const TextStyle(fontSize: 14.0),
          ),
          actions: <Widget>[
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Colors.black),
              child: Text(context.getString(StringKey.noAlternateLabel)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Colors.black),
              child: Text(context.getString(StringKey.yesLabel)),
              onPressed: () async {
                final navigation = Navigator.of(context);
                try {
                  AppAnalytics.logEvent(
                      name: "post_deletion_begin",
                      parameters: {"post_id": _post.id, "source": "post_page"});
                  final bool postDeleted = await _postService.deletePost(_post);
                  if (postDeleted) {
                    navigation.pop();
                    setState(() {
                      _post.active = false;
                    });
                    postBloc.add(PostDelete(_post));
                    AppAnalytics.logEvent(
                        name: "post_deletion_completed",
                        parameters: {
                          "post_id": _post.id,
                          "source": "post_page"
                        });
                  }
                } catch (e) {
                  Utils.showToast(localisedErrorMessage(e));
                }
              },
            ),
          ],
        );
      },
    );
  }

  userSelectedOption(PostOptions? selectedOption) async {
    if (selectedOption != null) {
      switch (selectedOption) {
        case PostOptions.reportPost:
          PostActions.reportReason(context: context, post: _post);
          break;
        case PostOptions.share:
          PostActions.postShare(
            post: _post,
            context: context,
            isShare: widget.isShare,
            onShare: widget.onShare,
          );
          break;
        case PostOptions.commentSettings:
          AppAnalytics.logEvent(
              name: "open_comment_options", parameters: {"source": "post"});
          PostActions.showCommentOptions(
              context: context, post: _post, postBloc: postBloc);
          break;
        case PostOptions.delete:
          _confirmDelete();
          break;
        case PostOptions.follow:
          try {
            await UserService.followUser(_post.user.id, "post_page");
            setState(() {
              _post.user.follows = true;
            });
            postBloc.add(PostUpdated(_post));
          } catch (e) {
            Utils.showToast(localisedErrorMessage(e));
          }
          break;
        case PostOptions.unFollow:
          try {
            await UserService.unfollowUser(_post.user.id, "post_page");
            setState(() {
              _post.user.follows = false;
            });
            postBloc.add(PostUpdated(_post));
          } catch (e) {
            Utils.showToast(localisedErrorMessage(e));
          }

          break;
        case PostOptions.blockUser:
          PostActions.userBlockReason(
            context: context,
            post: _post,
            postBloc: postBloc,
          );
          break;
        case PostOptions.reportUser:
          PostActions.userReportReason(post: _post, context: context);
          break;
        default:
          break;
      }
    } else {
      return;
    }
  }

  onDeletedCommentCallback(bool isDeleted) {
    if (isDeleted) {
      _commentsPageLoadController.reset();
      _loadPost();
    }
  }

  Widget _getBody() {
    if (_loading && preloadedPost == null && !postDetailFailed) {
      return ListView(
        children: const [
          PostShimmer(withDetails: true),
        ],
      );
    } else if (postDetailFailed) {
      return PostDetailFailedWidget(errorMessage: errorMessage);
    } else {
      return CustomScrollView(
        controller: _postScrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(0),
            sliver: SliverToBoxAdapter(
              key: _postEndExtentKey,
              child: Hero(
                tag: widget.heroTag,
                child: SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  child: PostListItem(
                    source: widget.source,
                    post: _post,
                    user: _post.user,
                    tappable: false,
                    commentFocusNode: commentFocusNode,
                    scrollToComments: scrollToComments,
                    fromPostDetailPage: true,
                    postBloc: postBloc,
                  ),
                ),
              ),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.all(0),
            sliver: PagewiseSliverList(
              pageLoadController: _commentsPageLoadController,
              loadingBuilder: Widgets.imageLoader,
              noItemsFoundBuilder: (context) => const SizedBox(),
              itemBuilder: (context, item, index) {
                lastCommentId = item.id;
                return Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: PostComment(
                    comment: item,
                    post: _post,
                    onCommentDeleted: onDeletedCommentCallback,
                    source: "Post Detail",
                  ),
                );
              },
            ),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(
              height: 70,
            ),
          ),
        ],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        title: Text(
          context.getPluralizedString(StringKey.postLabel, 1),
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.more_vert_rounded,
            ),
            onPressed: (_loading || !_post.active)
                ? null
                : () async {
                    final PostOptions? selectedOption =
                        await context.showPostOptions(
                            post: _post,
                            isSuperAdmin:
                                _userPermissions.contains("super_admin"));
                    userSelectedOption(selectedOption);
                  },
          ),
        ],
      ),
      bottomNavigationBar: const GlobalNavBar(isRoot: false),
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: () async {
              _loadPost();
              _commentsPageLoadController.reset();
              AppAnalytics.logEvent(name: "refresh_post_page", parameters: {
                "source": widget.source,
                "post_id": id.toString(),
                "hash_id": hashId.toString()
              });
            },
            child: _getBody(),
          ),
          Align(
              alignment: Alignment.bottomCenter,
              child: AnimatedSize(
                  duration: const Duration(milliseconds: 200),
                  alignment: Alignment.bottomCenter,
                  child: showCommentsBox
                      ? Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                                width: MediaQuery.of(context).size.width,
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black12,
                                      blurRadius: 4,
                                    )
                                  ],
                                ),
                                child: Center(
                                  child: Container(
                                    padding: const EdgeInsets.only(
                                        top: 10, bottom: 10),
                                    child: CommentBox(
                                      post: _post,
                                      requestCommentBoxFocusNode:
                                          widget.requestCommentBoxFocusNode,
                                      onSuccess: (Comment comment) {
                                        Map<String, dynamic> params = {
                                          "post_id": _post.id,
                                        };
                                        AppAnalytics.logEvent(
                                          name: "create_comment",
                                          parameters: params,
                                        );
                                        _commentsPageLoadController.reset();
                                        _loadPost();
                                      },
                                      rootWidgetName: "post_detail_page",
                                      focusNode: commentFocusNode,
                                    ),
                                  ),
                                ))
                          ],
                        )
                      : const SizedBox(width: double.infinity, height: 0)))
        ],
      ),
    );
  }
}

class CommentBox extends StatefulWidget {
  final Post post;
  final Function(Comment) onSuccess;
  final FocusNode? focusNode;
  final double visibilityThreshold;
  final bool shouldAnimate;
  final String rootWidgetName;
  final bool requestCommentBoxFocusNode;

  const CommentBox({
    super.key,
    required this.rootWidgetName,
    required this.post,
    required this.onSuccess,
    this.focusNode,
    this.visibilityThreshold = 0,
    this.shouldAnimate = false,
    this.requestCommentBoxFocusNode = false,
  });

  @override
  State<CommentBox> createState() => _CommentBoxState();
}

class _CommentBoxState extends State<CommentBox> with TickerProviderStateMixin {
  final TextEditingController _commentController = TextEditingController();
  bool _loading = false;
  late FocusNode _focusNode;
  bool _typingLogged = false;
  final PostService _postService = GetIt.I.get<PostService>();

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showFocusNode();
    });
  }

  void showFocusNode() {
    if (!mounted) return;
    if (widget.requestCommentBoxFocusNode) {
      _focusNode.requestFocus();
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  void _sendComment() async {
    var errorMsg = "";
    if (_commentController.text.isEmpty) {
      errorMsg = 'కామెంట్ రాయండి'; // Comment cannot blank
    }
    if (errorMsg.isNotEmpty) {
      return Utils.showToast(errorMsg);
    }
    if (mounted) setState(() => _loading = true);
    try {
      final response = await _postService.addComment(
        widget.post.id,
        _commentController.text,
      );
      widget.onSuccess(response);
      Utils.showToast("కామెంట్ చేయడం పూర్తయింది");
      if (mounted) {
        setState(() {
          _commentController.text = "";
          _loading = false;
        });
      }
    } catch (e) {
      if (mounted) setState(() => _loading = false);
      return Utils.showToast(localisedErrorMessage(e));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          const SizedBox(
            width: 70,
            child: Align(
              alignment: Alignment.centerRight,
              child: Padding(
                  padding: EdgeInsets.only(right: 18),
                  child: AppUserAvatar(size: 28)),
            ),
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: const Color(0xffDADADA), width: 2),
                  borderRadius: BorderRadius.circular(8)),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                      child: TextFormField(
                    style: const TextStyle(fontSize: 13),
                    focusNode: _focusNode,
                    controller: _commentController,
                    autofocus: false,
                    maxLines: 3,
                    minLines: 1,
                    keyboardType: TextInputType.text,
                    onChanged: (_) {
                      if (!_typingLogged) {
                        AppAnalytics.logEvent(
                          name: "create_comment_begin",
                          parameters: {"post_id": widget.post.id},
                        );
                        _typingLogged = true;
                      }
                    },
                    textAlignVertical: TextAlignVertical.top,
                    decoration: InputDecoration(
                        hintStyle: TextStyle(
                            fontSize: 12,
                            color: const Color(0xff000000).withOpacity(0.39)),
                        hintText: "మీ కామెంట్ ను తెలుపండి..",
                        // Share your comment
                        isDense: true,
                        contentPadding:
                            const EdgeInsets.only(left: 10, bottom: 6, top: 6),
                        border: InputBorder.none),
                  )),
                  InkWell(
                    onTap: () async {
                      AppAnalytics.logEvent(
                          name: "speech_mic_clicked",
                          parameters: {
                            "page": widget.rootWidgetName.toString()
                          });
                      bool? isExited =
                          await showSpeechDialog(context, _commentController);
                      if (isExited != null && !isExited) {
                        _focusNode.requestFocus();
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      height: 15,
                      child: Image.asset("assets/images/action/mic_icon.png"),
                    ),
                  ),
                  const SizedBox(
                    width: 6,
                  ),
                  const SizedBox(
                      height: 25,
                      child: VerticalDivider(
                        thickness: 2,
                        width: 2,
                      )),
                  InkWell(
                    onTap: _loading ? null : _sendComment,
                    child: SizedBox(
                        width: 45,
                        child: Center(
                          child: _loading
                              ? Widgets.buttonLoader(
                                  size: 14, color: Colors.grey)
                              : SizedBox(
                                  height: 23,
                                  child: Image.asset(
                                    "assets/images/action/send_icon.png",
                                  ),
                                ),
                        )),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(
            width: 12,
          )
        ]);
  }
}

class PostDetailFailedWidget extends StatelessWidget {
  final String? errorMessage;
  const PostDetailFailedWidget({super.key, this.errorMessage});

  @override
  Widget build(BuildContext context) {
    final errorMessage =
        this.errorMessage ?? context.getString(StringKey.postNotFoundText);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 60,
            color: Colors.grey,
          ),
          const SizedBox(
            height: 20,
          ),
          Text(
            errorMessage,
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}
