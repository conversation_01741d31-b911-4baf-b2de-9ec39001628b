import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/post.dart';
import 'package:praja/models/report_item.dart';
import 'package:praja/presentation/multi_select_chip.dart';
import 'package:praja/services/post/post_service.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets.dart';

class ReportDialog extends StatefulWidget {
  final Post post;

  const ReportDialog(this.post, {Key? key}) : super(key: key);

  @override
  State<ReportDialog> createState() => _ReportDialogState();
}

class _ReportDialogState extends State<ReportDialog> {
  ReportItem? _selectedReportChoice;
  bool _reporting = false;
  final PostService _postService = GetIt.I.get<PostService>();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        context.getString(StringKey.reportLabel), // Report
        style: const TextStyle(fontSize: 14.0),
      ),
      content: MultiSelectChip(
        list: Utils.getPostReportItems(),
        onSelected: (ReportItem value) {
          _selectedReportChoice = value;
        },
      ),
      actions: <Widget>[
        TextButton(
          style: TextButton.styleFrom(
            foregroundColor: Colors.black,
          ),
          child: Text(
            context.getString(StringKey.noLabel),
            style: const TextStyle(fontSize: 12.0),
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
          ),
          child: _reporting
              ? SizedBox(
                  width: 15,
                  child: Widgets.buttonLoader(size: 15.0),
                )
              : Text(
                  context.getString(StringKey.reportLabel), // Report
                  style: const TextStyle(fontSize: 12.0),
                ),
          onPressed: () async {
            final selectedReportChoice = _selectedReportChoice;
            if (selectedReportChoice != null) {
              setState(() => _reporting = true);

              AppAnalytics.logEvent(
                name: "report",
                parameters: {
                  "content_type": "post",
                  "item_id": widget.post.id.toString(),
                  "report_id": selectedReportChoice.id.toString(),
                },
              );
              try {
                Map<String, dynamic> result = await _postService.reportPost(
                    widget.post, selectedReportChoice);
                if (result['success']) {
                  Utils.showToast(result['message']);
                } else {
                  Utils.showToast(context.getString(
                      StringKey.reportFinishedToastText,
                      listen: false));
                }
              } catch (e) {
                Utils.showToast(localisedErrorMessage(e));
              }

              setState(() => _reporting = false);
              if (mounted) Navigator.of(context).pop();
            }
          },
        ),
      ],
    );
  }
}
