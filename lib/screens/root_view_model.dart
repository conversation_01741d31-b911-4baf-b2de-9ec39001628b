import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/app_event_bus.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/constants/AppConstants.dart';
import 'package:praja/features/app_icon_changer/app_icon_changer.dart';
import 'package:praja/features/deeplinks/deeplinks.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/deeplinks/popup_deeplink_handler.dart';
import 'package:praja/features/notifications/service/notification.dart';
import 'package:praja/features/posters/services/poster_preview_store.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/screens/notifications/notification_handler.dart';
import 'package:praja/services/app_initializer.dart';
import 'package:praja/services/circle/circle_identity_store.dart';
import 'package:praja/services/navigation.dart';
import 'package:praja/services/session_store.dart';
import 'package:praja/services/user/user_identity_store.dart';
import 'package:praja/services/user/user_service.dart';
import 'package:praja/services/user_store.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/services/user.dart' as old_user_service;
import 'package:praja/utils/utils.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';

@injectable
class RootViewModel extends ViewModel {
  final RegExp _prajaMWebRegex = RegExp(r'(http(s*):\/\/m\.praja\.buz+)');
  final RegExp _prajaDlRegex = RegExp(
      r'((praja-app)|(praja)|(praja-fAk8)):\/\/((buzz\.praja\.app)|(open))*');
  final RegExp _prajaBranchRegex = RegExp(r'(praja:\/\/open*)');
  final RegExp _prajaSingularRegex = RegExp(r'(praja:\/\/prajaapp.sng.link*)');
  final String _singularDeepLinkHost = "prajaapp.sng.link";

  final UserStore _userStore;
  final AppConstants _appConstants;
  final UserService _userService;
  final SessionStore _sessionStore;
  final PopupDeeplinkHandler _popupDeeplinkHandler;
  final NotificationService _notificationService;
  final AppInitializer _appInitializer;
  final NavigationService _navigationService;
  final AppEventBus _appEventBus;

  final MutableLiveData<RootState> _state =
      MutableLiveData(RootState.undetermined);
  LiveData<RootState> get state => _state;

  final MutableEventQueue<RootReadyEvent> _readyEventQueue =
      MutableEventQueue();
  EventQueue<RootReadyEvent> get readyEventQueue => _readyEventQueue;

  final MutableEventQueue<RootEvent> _rootEventQueue = MutableEventQueue();
  EventQueue<RootEvent> get rootEventQueue => _rootEventQueue;

  StreamSubscription? _appEventsSubscription;

  RootViewModel(
      this._userStore,
      this._appConstants,
      this._userService,
      this._sessionStore,
      this._popupDeeplinkHandler,
      this._notificationService,
      this._appInitializer,
      this._navigationService,
      this._appEventBus);

  bool _isInitialized = false;
  void _init() async {
    if (_isInitialized) {
      return;
    }
    _isInitialized = true;

    _appEventsSubscription = _appEventBus.stream.listen((event) {
      if (event is UserUnauthenticatedEvent) {
        _state.value = RootState.unauthenticated;
        _navigationService.popUntilFirst();
      }
    });

    initialize();

    GetIt.I.get<AppIconChanger>().startListening();
    GetIt.I.get<PosterPreviewStore>().listenToLogout();
    GetIt.I.get<CircleIdentityStore>().listenToLogout();
    GetIt.I.get<UserIdentityStore>().listenToLogout();
  }

  @override
  void onDispose() {
    _appEventsSubscription?.cancel();
    super.onDispose();
  }

  void initialize() async {
    String userToken = await _userStore.getUserToken();
    _appConstants.appInternetConnectivityType =
        await (Connectivity().checkConnectivity());
    logInfo(userToken);
    if (userToken.isEmpty) {
      AppAnalytics.logMixPanelEvent(name: "D_Si Login Screen");
      _state.value = RootState.unauthenticated;
    } else {
      AppUser? user = await _userService.getCurrentUser();
      if (user != null && user.id > 0) {
        AppAnalytics.setAppUser(user);

        AppAnalytics.logMixPanelEvent(
            name: "D_Si LoggedIn screen",
            parameters: {"attr1": user.id.toString()});

        await _getReady();
        _readyEventQueue.push(TrackReturnAppOpenEvent());
      } else {
        AppAnalytics.logMixPanelEvent(name: "D_Si Invalid user token state");
        _state.value = RootState.unauthenticated;
      }
    }
  }

  void onSignIn() async {
    _rootEventQueue.push(AskContactsPermissionEvent());
  }

  void afterContactsPermissionRequest() {
    _rootEventQueue.push(CheckAndShowPartySuggestionsEvent());
  }

  void afterPartySuggestions() async {
    await _getReady();
  }

  void onRetryInit() {
    _getReady();
  }

  Future<void> _getReady() async {
    _state.value = RootState.gettingReady;
    try {
      final initialData = await _appInitializer.initialize();
      if (!_sessionStore.hasSessionStarted()) {
        final referrer = await ReceiveSharingIntent.getReferrer();
        _startSession(referrer: referrer, source: sessionSourceAppLaunch);
      }
      AppAnalytics.logEvent(
          name: "initialized_app",
          parameters: initialData['app_open_analytics_params']);
      _onInitialDataReceived(initialData);
      _state.value = RootState.ready;
    } catch (e, st) {
      logNonFatalIfAppError("Error while initializing app", e, stackTrace: st);
      _rootEventQueue.push(InitializationFailedEvent(e));
    }
  }

  void _onInitialDataReceived(Map<String, dynamic> initialData) {
    final popupPath = initialData['popup_path'];
    if (popupPath != null) {
      logDebug("Event: OpenPopupEvent path: $popupPath");
      _readyEventQueue.push(OpenPopupEvent(popupPath));
    }

    _readyEventQueue.push(UpdateAppEvent(initialData));
  }

  void onShareMediaIntent(List<SharedMediaFile> sharedMediaFiles) async {
    if (sharedMediaFiles.isEmpty) {
      return;
    }

    _popupDeeplinkHandler.suppressInitialPopup();
    final String? sessionReferrer = await ReceiveSharingIntent.getReferrer();
    _startSession(referrer: sessionReferrer, source: sessionSourceShareMedia);
    _readyEventQueue.push(ShareFilesEvent(sharedMediaFiles));
  }

  void onIntent(String? data) async {
    final userToken = await _userStore.getUserToken();
    if (data != null && data.isNotEmpty) {
      if (_prajaMWebRegex.hasMatch(data) || _prajaDlRegex.hasMatch(data)) {
        Uri uri = Uri.parse(data);
        if (uri.path != "" &&
            uri.path != "/" &&
            uri.path.isNotEmpty &&
            uri.host != _singularDeepLinkHost) {
          // chat notifications trigger deeplinks as well
          // so sending this to notification handler to see if it is chat notifications
          // if not notifications, onDeeplinkPathResolved will be called with DeeplinkSource.intent
          NotificationHandler().onDeeplinkReceived(data);
        } else if (userToken.isEmpty) {
          String invitedBy = uri.queryParameters['invited_by'] ?? '';
          if (invitedBy.isNotEmpty) {
            Utils.setReferUserHashId(invitedBy);
          }
        }
      } else if (!_prajaBranchRegex.hasMatch(data) &&
          !_prajaSingularRegex.hasMatch(data)) {
        _popupDeeplinkHandler.suppressInitialPopup();
        String? sessionReferrer = await ReceiveSharingIntent.getReferrer();
        _startSession(
            referrer: sessionReferrer, source: sessionSourceShareText);
        _readyEventQueue.push(ShareTextEvent(data));
      }
    }
  }

  void onDeeplinkPathResolved(String path, DeeplinkSource source) async {
    final String? sessionReferrer = await ReceiveSharingIntent.getReferrer();
    _startSession(referrer: sessionReferrer, source: source.asString());
    final destination = DeeplinkDestination.fromRoute(path);
    if (destination == null) {
      logNonFatal("Invalid deeplink path $path",
          ArgumentError.value(path, "path", "Invalid deeplink path"));
      return;
    }

    // suppress initial popup if we are opening a deeplink if not exceptions
    final destinationExceptions = [
      FeedDeeplink,
      PostDeeplink,
      NotificationsTabDeeplink,
      PostersTabDeeplink
    ];
    final sourceExceptions = [
      DeeplinkSource.singularDeferred,
      DeeplinkSource.facebookDeferred
    ];
    if (!destinationExceptions
            .any((element) => element == destination.runtimeType) &&
        !sourceExceptions.any((element) => element == source)) {
      logDebug("Suppressing initial popup for deeplink $path, source: $source");
      _popupDeeplinkHandler.suppressInitialPopup();
    }
    _readyEventQueue.push(OpenDeeplinkDestinationEvent(destination, source));
  }

  void onFcmNotificationClicked(Map<String, dynamic> payload) async {
    final String? path = payload['path'];
    if (path == null || path.isEmpty) {
      return;
    }

    onDeeplinkPathResolved(path, DeeplinkSource.fcmNotification);

    String userToken = await _userStore.getUserToken();
    if (userToken.isEmpty) return;

    if (payload.containsKey('circle_notification_id')) {
      try {
        // for garuda notifications, circle_notification_id is UUID
        // don't need to mark as read for those
        // Ideally we shouldn't use `circle_notification_id` property for garuda notifications
        // it is for user notifications like user_follow, likes, comments etc.
        final int? circleNotificationId =
            int.tryParse(payload['circle_notification_id']);
        if (circleNotificationId != null) {
          await _notificationService.markAsRead(circleNotificationId);
        }
      } catch (e, stackTrace) {
        logNonFatalIfAppError(
            "Error While Performing Mark as Read Operation in Root", e,
            stackTrace: stackTrace);
      }
    }
  }

  void onCreatePostQuickAction() async {
    final referrer = await ReceiveSharingIntent.getReferrer();
    _startSession(
        referrer: referrer, source: DeeplinkSource.quickAction.asString());
    AppAnalytics.logEvent(
        name: "create_post_menu_click",
        parameters: {"source": "quick actions"});
    _readyEventQueue.push(OpenDeeplinkDestinationEvent(
        const CreatePostDeeplink(), DeeplinkSource.quickAction));
  }

  void onCirclesQuickAction() async {
    final referrer = await ReceiveSharingIntent.getReferrer();
    _startSession(
        referrer: referrer, source: DeeplinkSource.quickAction.asString());
    AppAnalytics.logEvent(
        name: "circle_menu_click", parameters: {"source": "quick actions"});
    _readyEventQueue.push(OpenDeeplinkDestinationEvent(
        const MyCirclesDeeplink(), DeeplinkSource.quickAction));
  }

  void onFacebookDeferredDeeplink(String pathAndQuery) async {
    AppAnalytics.logEvent(
        name: "facebook_deferred_deeplink", parameters: {"path": pathAndQuery});
    onDeeplinkPathResolved(pathAndQuery, DeeplinkSource.facebookDeferred);
  }

  void logout() async {
    await old_user_service.UserService.logout();
    AppAnalytics.logLogOut();
    _state.value = RootState.unauthenticated;
    _navigationService.popUntilFirst();

    // clearing popup suppression (if any) on logout
    _popupDeeplinkHandler.allowInitialPopup();
    // reattaching session properties for the upcoming user
    _startSession(
        referrer: _sessionStore.sessionReferrer,
        source: _sessionStore.sessionSource);
  }

  void _startSession({String? referrer, String? source}) {
    _sessionStore.startSession(
        sessionReferrer: referrer, sessionSource: source);
    AppAnalytics.registerSessionSuperProperties(_sessionStore.sessionAnalytics);
    AppAnalytics.logEvent(name: "attached_session_params");
  }
}

extension RootViewModelX on BuildContext {
  RootViewModel rootViewModel() => getViewModel<RootViewModel>().._init();
}

enum RootState {
  undetermined,
  unauthenticated,
  gettingReady,
  ready,
}

sealed class RootReadyEvent {}

class ShareFilesEvent extends RootReadyEvent {
  final List<SharedMediaFile> sharedMediaFiles;

  ShareFilesEvent(this.sharedMediaFiles);
}

class ShareTextEvent extends RootReadyEvent {
  final String text;

  ShareTextEvent(this.text);
}

class OpenDeeplinkDestinationEvent extends RootReadyEvent {
  final DeeplinkDestination destination;
  final DeeplinkSource source;

  OpenDeeplinkDestinationEvent(this.destination, this.source);
}

class TrackReturnAppOpenEvent extends RootReadyEvent {}

class OpenPopupEvent extends RootReadyEvent {
  final String popupPath;

  OpenPopupEvent(this.popupPath);
}

class UpdateAppEvent extends RootReadyEvent {
  final Map<String, dynamic> initialData;

  UpdateAppEvent(this.initialData);
}

sealed class RootEvent {}

class InitializationFailedEvent extends RootEvent {
  final dynamic error;

  InitializationFailedEvent(this.error);
}

class AskContactsPermissionEvent extends RootEvent {}

class CheckAndShowPartySuggestionsEvent extends RootEvent {}
