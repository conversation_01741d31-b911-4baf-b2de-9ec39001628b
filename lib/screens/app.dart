import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/flavor_banner.dart';
import 'package:praja/presentation/nav_bar_view_model.dart';
import 'package:praja/screens/root.dart';
import 'package:praja/services/app_state.dart';
import 'package:praja/services/navigation.dart';
import 'package:praja/services/user_store.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';

class PrajaApp extends StatefulWidget {
  const PrajaApp({super.key});

  @override
  State<PrajaApp> createState() => _PrajaAppState();
}

class _PrajaAppState extends State<PrajaApp> with WidgetsBindingObserver {
  late String appDisplayMode;
  String appInstallSource = '';
  late NavViewModel navViewModel;

  StreamSubscription<AppOpenState>? _appOpenStateSubscription;

  Future<void> _getInstallSource() async {
    appInstallSource = await Utils.getInstallationSource();
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    appDisplayMode =
        SchedulerBinding.instance.window.platformBrightness == Brightness.dark
            ? 'Dark Mode'
            : 'Light Mode';
    _fireLogOpenEvent();
    navViewModel = context.globalNavViewModel;

    _appOpenStateSubscription = AppState.appOpenStateStream.listen(
      _onAppOpenStateChanged,
    );
  }

  void _fireLogOpenEvent() async {
    await _getInstallSource();
    final user = await GetIt.I.get<UserStore>().getAppUser();
    if (user != null) {
      AppAnalytics.setSingularUserId(user.id.toString());
    }
    AppAnalytics.logAppOpen(appDisplayMode, appInstallSource);
  }

  void _onAppOpenStateChanged(AppOpenState appOpenState) {
    logDebug('AppOpenState Changed: $appOpenState');
    switch (appOpenState) {
      case AppOpenState.open:
        AppAnalytics.logAppOpen(appDisplayMode, appInstallSource);
        break;
      case AppOpenState.closed:
        AppAnalytics.logAppClose();
        AppAnalytics.flush();
        break;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _appOpenStateSubscription?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    printDebug('AppLifecycleState: $state');
    AppState.currentState = state;
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: "Praja",
      debugShowCheckedModeBanner: true,
      theme: ThemeData(
        useMaterial3: false,
        scaffoldBackgroundColor: Colors.white,
        primarySwatch: Styles.circleIndigo,
        indicatorColor: Styles.circleIndigo,
        tabBarTheme: const TabBarTheme(
          indicatorColor: Styles.circleIndigo,
          labelColor: Colors.black,
          unselectedLabelColor: Colors.grey,
          labelStyle: TextStyle(
              fontWeight: FontWeight.w500, fontFamily: 'NotoSansTelugu'),
          unselectedLabelStyle: TextStyle(fontFamily: 'NotoSansTelugu'),
        ),
        colorScheme: ColorScheme.fromSwatch(
          backgroundColor: Colors.white,
          primarySwatch: Styles.circleIndigo,
        ),
        textTheme: ThemeData.light().textTheme.apply(
          fontFamily: 'Roboto',
          fontFamilyFallback: ['NotoSansTelugu'],
        ),
        appBarTheme: AppBarTheme(
          shadowColor: Colors.black.withOpacity(0.5),
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarBrightness: Brightness.light,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
          ),
          elevation: 1,
          color: Colors.white,
          titleTextStyle: const TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
            fontFamilyFallback: ['NotoSansTelugu'],
            fontFamily: 'Roboto',
          ),
          iconTheme: const IconThemeData(
            color: Colors.black,
          ),
        ),
      ),
      themeMode: ThemeMode.light,
      navigatorObservers: [PageViewReporter.routeObserver],
      navigatorKey: GetIt.I<NavigationService>().navigatorKey,
      home: FlavorBanner(
        child: const Root(),
      ),
    );
  }
}
