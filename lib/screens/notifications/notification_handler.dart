import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart'
    hide Message;
import 'package:get_it/get_it.dart';
import 'package:praja/common/deeplink_params.dart';
import 'package:praja/core/di/injection.dart';
import 'package:praja/features/direct_messaging/api/models/message.dart';
import 'package:praja/features/direct_messaging/service/messaging_service.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/services/device_token_service.dart';
import 'package:praja/services/event_flag.dart';
import 'package:praja/services/permissions.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/uri_utils.dart';
import 'package:singular_flutter_sdk/singular.dart';

late FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;

class NotificationHandler {
  static final NotificationHandler _instance = NotificationHandler._internal();

  factory NotificationHandler() {
    return _instance;
  }

  AuthorizationStatus _authorizationStatus = AuthorizationStatus.notDetermined;
  FirebaseMessaging get _firebaseMessaging => FirebaseMessaging.instance;
  Stream get _fcmTokenStream => FirebaseMessaging.instance.onTokenRefresh;

  final StreamController<Map<String, dynamic>> _onFCMNotificationTapController =
      StreamController<Map<String, dynamic>>();
  final StreamController<DeeplinkParams> _onDeeplinkPathResolved =
      StreamController();

  NotificationHandler._internal();

  init() async {
    final permissionStatus = await _requestPermission();
    AppAnalytics.onNotificationPermissionChanged(
      authorizationStatus:
          await PermissionsService().getNotificationPermissionStatus(),
    );

    if (permissionStatus == AuthorizationStatus.authorized) {
      await _initLocalNotifications(_onLocalNotificationClick);
      await _listenToNotifications();
    }
  }

  Stream<Map<String, dynamic>> get onFCMNotificationTap =>
      _onFCMNotificationTapController.stream.map(_onNotificationClicked);

  Stream<DeeplinkParams> get onDeeplinkPathResolved =>
      _onDeeplinkPathResolved.stream;

  void onDeeplinkReceived(String url) {
    printDebug("Deeplink Received: $url");
    Uri uri = Uri.parse(url);
    final parameters = {"path": uri.path, "domain": uri.host};
    parameters.addAll(uri.queryParameters);

    final source = uri.queryParameters["source"];
    _onDeeplinkPathResolved.add(DeeplinkParams(
        path: uri.pathAndQuery(),
        source: (source != null && source == "chat")
            ? DeeplinkSource.fcmNotification
            : DeeplinkSource.intent));
    AppAnalytics.logEvent(name: "on_deeplink_clicked", parameters: parameters);
  }

  Map<String, dynamic> _onNotificationClicked(Map<String, dynamic> payload) {
    final parameters = {
      "path": payload["path"],
      "notification_id": payload["circle_notification_id"]
    };
    GetIt.I.get<EventFlags>().isNotification = true;
    GetIt.I.get<EventFlags>().notificationSource = "FCM";
    AppAnalytics.logEvent(name: "fcm_push_click", parameters: parameters);
    return payload;
  }

  Future<AuthorizationStatus> _requestPermission() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    _authorizationStatus = settings.authorizationStatus;

    return _authorizationStatus;
  }

  Future<void> _setupTokenListener() async {
    try {
      final token = await _firebaseMessaging.getToken();
      _onFCMTokenReceived(token);
    } catch (e, stackTrace) {
      logNonFatalIfAppError("Error While Getting FCM Token", e,
          stackTrace: stackTrace);
    }
    _fcmTokenStream.listen((token) {
      _onFCMTokenReceived(token);
    });
  }

  _onFCMTokenReceived(String? token) async {
    try {
      if (token != null) {
        if (Platform.isAndroid) {
          Singular.registerDeviceTokenForUninstall(token);
        }

        logInfo("FCM Token: $token");
        AppAnalytics.onRefreshFCMToken(token);
        await GetIt.I.get<DeviceTokenService>().onTokenReceived(token);
      }
    } catch (e, stackTrace) {
      logNonFatalIfAppError("Error on receiving FCM Token", e,
          stackTrace: stackTrace);
    }
  }

  Future<void> _showLocalNotifications(RemoteMessage message,
      NotificationDetails platformChannelSpecifics) async {
    if (Platform.isIOS) {
      return;
    }
    String payload = '{}';
    payload = json.encode(message.data);
    printDebug("Showing local notification with payload: $payload");
    int notificationId = message.data["notification_id"] ?? 0;
    RemoteNotification? notification = message.notification;
    if (notification != null) {
      await _flutterLocalNotificationsPlugin.show(
        notificationId,
        notification.title,
        notification.body,
        platformChannelSpecifics,
        payload: payload,
      );
    }
  }

  Future<void> _listenToNotifications() async {
    try {
      await _setupTokenListener();
    } catch (e, stackTrace) {
      logNonFatalIfAppError("Error While Setting Up FCM Token Listener", e,
          stackTrace: stackTrace);
      return;
    }

    // enable foreground notifications in iOS
    // https://firebase.flutter.dev/docs/messaging/notifications#ios-configuration
    await _firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    //when in foreground use local notifications
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      printDebug("FCM Message Received: ${message.data}");
      _onNotificationReceived(message);

      // Nothing to do in iOS
      // System handles showing the notification
      if (Platform.isIOS) {
        return;
      }

      // DM Notification
      if (message.data.containsKey("message") && Platform.isAndroid) {
        // nothing to do here
        // chat notifications are handled natively by PrajaFCMService
        return;
      }

      // Non DM notifications
      //setup channel name
      String channelID = Channel.general.id;
      String channelName = Channel.general.name;
      String channelDescription = Channel.general.description;

      if (message.data.containsKey("channel_id")) {
        channelID = message.data["channel_id"]!;
      }

      if (message.data.containsKey("channel_name")) {
        channelName = message.data["channel_name"]!;
      }

      if (message.data.containsKey("channel_description")) {
        channelDescription = message.data["channel_description"]!;
      }

      final androidPlatformChannelSpecifics = AndroidNotificationDetails(
        channelID,
        channelName,
        channelDescription: channelDescription,
        importance: Importance.max,
        priority: Priority.high,
        ticker: 'ticker',
      );

      const iOSPlatformChannelSpecifics = DarwinNotificationDetails();
      final platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _showLocalNotifications(message, platformChannelSpecifics);
    });

    FirebaseMessaging.onBackgroundMessage(_backgroundNotificationCallback);

    // Was an fcm notification clicked to open the app from terminated state
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      _onFCMNotificationTapController.add(initialMessage.data);
    }

    // Notification clicks in foreground / background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      _onFCMNotificationTapController.add(message.data);
    });

    // Was a flutter local notification clicked to open the app from terminated state
    final notificationDetails = await _flutterLocalNotificationsPlugin
        .getNotificationAppLaunchDetails();
    if (notificationDetails == null) {
      return;
    }
    final resposne = notificationDetails.notificationResponse;
    if (resposne != null) {
      _onLocalNotificationClick(resposne);
    }
  }

  Future<void> _onLocalNotificationClick(NotificationResponse response) async {
    final notificationPayload = response.payload;
    if (notificationPayload != null) {
      _onFCMNotificationTapController.add(json.decode(notificationPayload));
    }
  }
}

void _onNotificationReceived(RemoteMessage message,
    {bool isBackground = false}) {
  final notificationData = message.data;

  AppAnalytics.onNotificationReceived(
      source: "fcm",
      appState: isBackground ? "background" : "foreground",
      props: notificationData);
}

@pragma('vm:entry-point')
Future<void> _backgroundNotificationCallback(RemoteMessage message) async {
  configureDependencies();
  await initLogger();

  // set the on error only if it's not set
  // from [exceptions like this](https://console.firebase.google.com/project/praja-007/crashlytics/app/android:buzz.praja.app/issues/626e4d31d6776aef19df50306e85462a?time=last-seven-days&versions=2307.04.04%20(23070404);2307.04.05%20(23070405)&sessionEventKey=64AE7FF3033400017CB2898FD216B0CB_1833325834401784634)
  // it looks like this callback can be invoked in the main app's isolate
  PlatformDispatcher.instance.onError ??= _onError;

  await AppAnalytics.initialize();
  _onNotificationReceived(message, isBackground: true);
  printDebug("Background Notification Callback");
  printDebug(
      "Message Notification: {title: ${message.notification?.title}, body: ${message.notification?.body}}");
  printDebug("Message Data: ${message.data}");
  final notificationPayload = message.data;
  if (notificationPayload.containsKey("message")) {
    // display of the notification is already handled by PrajaFCMService in Android, and by the OS in iOS

    final dm = Message.fromJson(json.decode(notificationPayload["message"]));
    final messageService = GetIt.I<MessagingService>();
    await messageService.handleMessageFromNotification(dm);
  }
}

Future<void> _initLocalNotifications(
    DidReceiveNotificationResponseCallback? clickCallback) async {
  _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  const initializationSettingsAndroid =
      AndroidInitializationSettings('ic_stat_onesignal_default');
  const initializationSettingsIOS = DarwinInitializationSettings();
  const initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsIOS,
  );

  await _flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: clickCallback,
  );

  final channels = [Channel.general, Channel.messages];
  for (Channel channel in channels) {
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(
          AndroidNotificationChannel(
            channel.id,
            channel.name,
            description: channel.description,
            importance: channel.importance,
          ),
        );
  }
}

bool _onError(Object error, StackTrace stackTrace) {
  if (kDebugMode) {
    // Print the full stacktrace in debug mode.
    printDebug("Uncaught error",
        stackTrace: stackTrace,
        error: error,
        level: LogLevel.error,
        showToast: true);
  } else {
    FirebaseCrashlytics.instance.recordError(error, stackTrace, fatal: true);
  }
  return true;
}

class Channel {
  final String id;
  final String name;
  final String description;
  final Importance importance;

  const Channel(this.id, this.name, this.description, this.importance);

  @override
  String toString() =>
      'Channel{id: $id, name: $name, description: $description, importance: $importance}';

  static const Channel general = Channel(
    "general",
    "General",
    "Default channel for generic notifications",
    Importance.high,
  );

  static const Channel messages = Channel(
    "message",
    "Messages",
    "Messages from Praja Chat",
    Importance.max,
  );
}
