import 'dart:math';

import 'package:flutter/material.dart' hide Badge;
import 'package:get_it/get_it.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/direct_messaging/presentation/chat_page/chat_page.dart';
import 'package:praja/features/home/<USER>/home.dart';
import 'package:praja/features/intl/intl.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/user/follow/state/user_follow_view_model.dart';
import 'package:praja/features/user/ui/app_user_editable_avatar.dart';
import 'package:praja/features/verification/verification_explainer_sheet.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/badge.dart';
import 'package:praja/models/user.dart';
import 'package:praja/models/user_details.dart';
import 'package:praja/presentation/avatar.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/presentation/user_avatar.dart';
import 'package:praja/screens/circles_v2/circle_item.dart';
import 'package:praja/screens/users/followers_list.dart';
import 'package:praja/screens/users/following_list.dart';
import 'package:praja/screens/users/likes.dart';
import 'package:praja/screens/users/posts.dart';
import 'package:praja/screens/users/share.dart';
import 'package:praja/screens/users/user.dart';
import 'package:praja/services/user_store.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/color_utils.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets.dart';
import 'package:praja/utils/widgets/badge_banner_tag.dart';
import 'package:praja/utils/widgets/badge_strip_profile.dart';
import 'package:praja/utils/widgets/page_transition_widget.dart';
import 'package:praja/services/user/user_service.dart' as new_user_service;

class ProfileInfo extends StatefulWidget {
  final UserDetails user;
  final Function? callback;
  final EdgeInsets padding;
  final ScrollController? scrollController;
  final String source;

  const ProfileInfo({
    required this.user,
    super.key,
    this.callback,
    this.scrollController,
    this.padding = EdgeInsets.zero,
    this.source = "profile_info",
  });

  @override
  State<ProfileInfo> createState() => _ProfileInfoState();
}

class _ProfileInfoState extends State<ProfileInfo>
    with SingleTickerProviderStateMixin {
  late UserDetails user;
  late bool follows;
  late int followersCount;

  double get randHeight => Random().nextInt(100).toDouble();
  bool isWhatsappShareLoading = false;
  bool isShareLoading = false;
  bool hasUserPostsLoaded = false;
  final HomeService _homeService = GetIt.I.get<HomeService>();
  static const superAdmin = 'super_admin';
  bool isUnblocking = false;
  late UserFollowViewModel userFollowViewModel;

  @override
  void initState() {
    super.initState();
    user = widget.user;
    follows = user.follows;
    followersCount = user.followersCount;
    userFollowViewModel =
        context.userFollowViewModel(widget.user.id, widget.user.follows);
    trackProfileVisit();
  }

  trackProfileVisit() async {
    final loggedInUserId = (await GetIt.I.get<UserStore>().getAppUserId());
    AppAnalytics.logProfileVisit(
      loggedInUserId,
      user.id,
      source: widget.source,
    );
  }

  trackProfileShareOrInvite(String type) async {
    final loggedInUserId = (await GetIt.I.get<UserStore>().getAppUserId());
    if (user.id == loggedInUserId) {
      //checks if the current user is sharing his or her profile
      AppAnalytics.logEvent(
          name: "Invite",
          parameters: {"profile_id": user.id.toString(), "type": type});
    } else {
      AppAnalytics.logEvent(name: "Share_profile", parameters: {
        "shared_profile_id": user.id.toString(),
        "sharer_id": loggedInUserId.toString(),
        "type": type
      });
    }
  }

  void _openCirclePage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) =>
            CircleV2Item(id: user.village!.id, source: "User profile"),
      ),
    );
  }

  Future<void> _openVerificationExplainerSheet(String source) async {
    final userBadge = user.badge;
    if (userBadge == null) return;
    AppAnalytics.onVerificationExplainerSheetClicked(
      badgeId: userBadge.id,
      roleDescription: userBadge.description,
      verificationStatus: userBadge.verificationStatus.name,
      source: source,
    );
    await VerificationExplainerSheet.show(
      context,
      user: user.toIdentity(),
    );
  }

  isSilverStrip(Badge badge) {
    return (badge.badgeBanner == BadgeBanner.silver) ? true : false;
  }

  isGoldStrip(Badge badge) {
    return (badge.badgeBanner == BadgeBanner.gold) ? true : false;
  }

  void _onFollow() {
    setState(() {
      if (widget.callback != null) {
        widget.callback!(true);
      }
      follows = true;
      followersCount++;
    });
  }

  getStripPart({required String stripPart, required Badge badge}) {
    switch (badge.badgeBanner) {
      case BadgeBanner.gold:
        return "assets/images/badges/g_$stripPart.png";
      case BadgeBanner.silver:
        return "assets/images/badges/s_$stripPart.png";
      default:
        return "assets/images/badges/s_$stripPart.png";
    }
  }

  void _onUnFollow() {
    setState(() {
      if (widget.callback != null) {
        widget.callback!(false);
      }
      follows = false;
      if (followersCount > 0) {
        followersCount--;
      }
    });
  }

  bool get _isUnverifiedUser =>
      user.badge?.verificationStatus == VerificationStatus.unverified;

  Widget getCommonBadgeBanner(User user) {
    final badge = user.badge;
    if (badge != null && badge.badgeBanner == BadgeBanner.none) {
      if (badge.description.isNotEmpty) {
        return Column(
          children: [
            Container(
              constraints: const BoxConstraints(minWidth: 90, maxWidth: 300),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 1, horizontal: 15),
                child: IntrinsicWidth(
                  child: Center(
                    child: Text(
                      badge.description.toString(),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }
    }
    return const SizedBox();
  }

  Widget getReferralButton() {
    return user.internalJournalist
        ? Align(
            alignment: Alignment.topRight,
            child: InkWell(
              onTap: () async {
                final String? token = await Utils.getEncryptedToken();
                if (token != null && mounted) {
                  Utils.showReferralScreen(context, token);
                }
              },
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Material(
                    borderRadius: BorderRadius.circular(8),
                    elevation: 5,
                    child: Container(
                      decoration: BoxDecoration(
                          gradient: const LinearGradient(
                              colors: [Color(0xff3c58a7), Color(0xff8cabff)]),
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8)),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 10),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                                height: 20,
                                child: Image.asset(
                                    "assets/images/action/coin.png")),
                            const SizedBox(
                              width: 5,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      user.referralPoints.toString(),
                                      style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold),
                                    ),
                                    const Icon(
                                      Icons.keyboard_arrow_right_rounded,
                                      color: Colors.white,
                                      size: 15,
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
        : const SizedBox();
  }

  pageLoadedCallBack() {
    setState(() {
      hasUserPostsLoaded = true;
    });
  }

  Future<void> preCacheRequiredAssets(BuildContext context) async {
    await Future.wait([
      precacheImage(
          const AssetImage("assets/images/logo-transparent.png"), context),
      precacheImage(
          const AssetImage("assets/images/praja-full-logo.png"), context),
    ]);
    if (user.badge != null) {
      await Future.wait([
        precacheImage(
            const AssetImage("assets/images/badge_congo/congo_bg.png"),
            context),
        precacheImage(
            const AssetImage("assets/images/badge_congo/congo_bg_cropped.png"),
            context),
      ]);
      if (selectBadgeBannerColor(user.badge!.badgeBanner) == "g") {
        await Future.wait([
          precacheImage(
              const AssetImage("assets/images/badges/g_left_light.png"),
              context),
          precacheImage(
              const AssetImage("assets/images/badges/g_right_light.png"),
              context),
          precacheImage(
              const AssetImage("assets/images/badges/g_center_light.png"),
              context),
        ]);
      } else {
        await Future.wait([
          precacheImage(
              AssetImage(
                  "assets/images/badges/${selectBadgeBannerColor(user.badge!.badgeBanner)}_left.png"),
              context),
          precacheImage(
              AssetImage(
                  "assets/images/badges/${selectBadgeBannerColor(user.badge!.badgeBanner)}_right.png"),
              context),
          precacheImage(
              AssetImage(
                  "assets/images/badges/${selectBadgeBannerColor(user.badge!.badgeBanner)}_center.png"),
              context),
        ]);
      }
      await Future.wait([
        precacheImage(
            const AssetImage("assets/images/badge_congo/silver_ring.png"),
            context),
        precacheImage(
            const AssetImage("assets/images/badge_congo/gold_ring.png"),
            context),
        precacheImage(
            const AssetImage("assets/images/badge_congo/ribbon.png"), context),
        precacheImage(
            const AssetImage("assets/images/badge_congo/upload_pic.png"),
            context),
        precacheImage(
            const AssetImage("assets/images/badge_congo/3_stars.png"), context),
        precacheImage(
            const AssetImage("assets/images/badge_congo/5_stars.png"), context),
      ]);
    }
  }

  Widget _youAreBlockedUI(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 24),
        child: Column(
          children: [
            Icon(
              Icons.block,
              color: Colors.grey.shade600,
              size: 48,
            ),
            const SizedBox(
              height: 8,
            ),
            Text(context.getString(StringKey.blockedByUserText),
                textScaler: const TextScaler.linear(1.0),
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade800,
                  fontSize: 18,
                )),
          ],
        ));
  }

  Widget _unblockUserUI(BuildContext context) {
    final unblockButton = ElevatedButton.icon(
      //padding: EdgeInsets.all(5),
      style: ButtonStyle(
        overlayColor: MaterialStateProperty.all((Colors.grey[200])),
        elevation: MaterialStateProperty.all(0),
        shape: MaterialStateProperty.all(RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        )),
        backgroundColor: MaterialStateProperty.all((Colors.grey.shade200)),
      ),
      label: Padding(
        padding: const EdgeInsets.only(top: 2),
        child: Text(
          context.getString(StringKey.unblockUserText),
          textScaler: const TextScaler.linear(1.0),
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 14, color: Color(0xFFF44A4A)),
        ),
      ),
      icon: isUnblocking
          ? const SizedBox(
              width: 14,
              height: 14,
              child: CircularProgressIndicator(color: Colors.grey))
          : const Icon(
              Icons.do_not_disturb_off,
              size: 14,
              color: Color(0xFFF44A4A),
            ),
      onPressed: () async {
        setState(() {
          isUnblocking = true;
        });
        final userService = GetIt.I.get<new_user_service.UserService>();
        await userService.unblockUser(user.id);
        if (mounted) {
          Navigator.of(context).pop();
          Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => UserPage(
                    id: user.id,
                    source: widget.source,
                  )));
        }
      },
    );

    return Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
        child: Column(
          children: [
            Text(context.getString(StringKey.blockedUserText),
                textScaler: const TextScaler.linear(1.0),
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                )),
            unblockButton,
          ],
        ));
  }

  Widget _otherProfileActions(BuildContext context) {
    if (user.blocked) {
      return _unblockUserUI(context);
    }

    if (user.loggedInUserBlocked) {
      return _youAreBlockedUI(context);
    }

    final messageButton = SizedBox(
        height: 35,
        child: ElevatedButton.icon(
            style: ButtonStyle(
                overlayColor: MaterialStateProperty.all((Colors.grey[200])),
                elevation: MaterialStateProperty.all(0),
                shape: MaterialStateProperty.all(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                )),
                backgroundColor:
                    MaterialStateProperty.all((Colors.grey.shade200))),
            onPressed: () {
              AppAnalytics.onMessageClickedInProfile(userId: user.id);
              Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => ChatPage.withUser(user.id)));
            },
            label: Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Text(context.getString(StringKey.messageLabel),
                    textScaler: const TextScaler.linear(1.0),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.black,
                    ))),
            icon: const Icon(PrajaIcons.message, color: Colors.black)));

    final shareButton = SizedBox(
        height: 35,
        child: ElevatedButton.icon(
            style: ButtonStyle(
                overlayColor: MaterialStateProperty.all((Colors.grey[200])),
                elevation: MaterialStateProperty.all(0),
                shape: MaterialStateProperty.all(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                )),
                backgroundColor:
                    MaterialStateProperty.all((Colors.grey.shade200))),
            onPressed: () async {
              try {
                await preCacheRequiredAssets(context);
                setState(() {
                  isShareLoading = true;
                });
                final refer =
                    await _homeService.getProfileShareText(widget.user.id);
                trackProfileShareOrInvite("share");
                if (!mounted) return;
                if (refer.generateInviteCard) {
                  Navigator.push(
                    context,
                    PageSlideRight(
                      page: ProfileShare(
                        user: user,
                        toWhatsApp: false,
                        refer: refer,
                        onShare: () {
                          setState(() {
                            isShareLoading = false;
                          });
                        },
                      ),
                    ),
                  );
                } else {
                  Utils().referApp(context, "followers", toWhatsApp: false);
                }
                if (mounted) {
                  setState(() {
                    isShareLoading = false;
                  });
                }
              } catch (e, st) {
                if (mounted) {
                  setState(() {
                    isShareLoading = false;
                  });
                }
                Utils.showToast(localisedErrorMessage(e));
                logNonFatalIfAppError("Error while sharing profile", e,
                    stackTrace: st);
              }
            },
            label: Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Text(context.getString(StringKey.shareProfileLabel),
                  textAlign: TextAlign.center,
                  textScaler: const TextScaler.linear(1.0),
                  style: const TextStyle(
                    color: Colors.black,
                  )),
            ),
            icon: isShareLoading
                ? Widgets.buttonLoader(size: 14, color: Colors.blue)
                : Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationY(pi),
                    child:
                        const Icon(Icons.reply, color: Colors.black, size: 22),
                  )));

    final followButton = SizedBox(
        height: 35,
        child: ElevatedButton.icon(
          //padding: EdgeInsets.all(5),
          style: ButtonStyle(
            elevation: MaterialStateProperty.all(0),
          ),
          label: Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Text(
              context.getString(StringKey.followUserLabel),
              textScaler: const TextScaler.linear(1.0),
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          icon: const Icon(
            Icons.add,
            size: 14,
          ),
          onPressed: () async {
            _onFollow();
            AppAnalytics.logEvent(
              name: "follow_user",
              parameters: {
                "source": "user_profile",
                "followee": widget.user.id.toString(),
              },
            );
            final sucess = await userFollowViewModel.followUser("user_profile");
            if (!sucess) {
              _onUnFollow();
            }
          },
        ));

    final unfollowButton = SizedBox(
        height: 35,
        child: ElevatedButton.icon(
          style: ButtonStyle(
            elevation: MaterialStateProperty.all(0),
            backgroundColor: MaterialStateProperty.all(Colors.white),
            shape: MaterialStateProperty.all(RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
                side: const BorderSide(color: Styles.circleIndigo))),
          ),
          label: Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Text(
              context.getString(StringKey.followingUserLabel),
              maxLines: 1,
              style: const TextStyle(
                fontSize: 14,
                color: Styles.circleIndigo,
              ),
            ),
          ),
          icon: const Icon(
            Icons.check,
            size: 14,
            color: Styles.circleIndigo,
          ),
          onPressed: () async {
            _onUnFollow();
            AppAnalytics.logEvent(
              name: "unfollow_user",
              parameters: {
                "source": "user_profile",
                "user_id": widget.user.id.toString(),
              },
            );
            final successs = await userFollowViewModel.unfollowUser();
            if (!successs) {
              _onFollow();
            }
          },
        ));

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: user.showMessageAction
          ? Column(children: [
              Row(children: [
                Expanded(
                    flex: 1,
                    child: LiveDataBuilder<UserFollowState>(
                        liveData: userFollowViewModel.state,
                        builder: (_, state) {
                          if (state is InitialisedUserFollowState &&
                              !state.isFollowing) {
                            return followButton;
                          } else {
                            return unfollowButton;
                          }
                        })),
              ]),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(flex: 1, child: messageButton),
                  const SizedBox(width: 5),
                  Expanded(flex: 1, child: shareButton)
                ],
              )
            ])
          : Row(
              children: [
                Expanded(
                    flex: 1, child: follows ? unfollowButton : followButton),
                const SizedBox(width: 5),
                Expanded(flex: 1, child: shareButton)
              ],
            ),
    );
  }

  Widget _myProfileActions(BuildContext context) {
    final whatsappButton = ElevatedButton.icon(
        style: ButtonStyle(
            overlayColor: MaterialStateProperty.all((Colors.grey[200])),
            elevation: MaterialStateProperty.all(0),
            shape: MaterialStateProperty.all(RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
                side: const BorderSide(color: Color(0xff47A04A)))),
            backgroundColor: MaterialStateProperty.all((Colors.white))),
        onPressed: () async {
          await preCacheRequiredAssets(context);
          setState(() {
            isWhatsappShareLoading = true;
          });
          try {
            final refer = await _homeService.getReferText();
            trackProfileShareOrInvite("whatsapp");
            if (!mounted) return;
            if (refer.generateInviteCard) {
              Navigator.push(
                context,
                PageSlideRight(
                  page: ProfileShare(
                    user: user,
                    toWhatsApp: true,
                    refer: refer,
                    onShare: () {
                      setState(() {
                        isWhatsappShareLoading = false;
                      });
                    },
                  ),
                ),
              );
            } else {
              Utils().referApp(context, "followers");
            }
            if (mounted) {
              setState(() {
                isWhatsappShareLoading = false;
              });
            }
          } catch (e, st) {
            setState(() {
              isWhatsappShareLoading = false;
            });
            Utils.showToast(localisedErrorMessage(e));
            logNonFatalIfAppError("Error while sharing profile in whatsapp", e,
                stackTrace: st);
          }
        },
        label: Text(context.getString(StringKey.inviteLabel),
            textScaler: const TextScaler.linear(1.0),
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
            )),
        icon: SizedBox(
            height: 18,
            width: 18,
            child: isWhatsappShareLoading
                ? Widgets.buttonLoader(size: 14, color: Colors.green)
                : Image.asset("assets/images/action/whatsapp.png")));

    final facebookButton = ElevatedButton.icon(
        style: ButtonStyle(
            overlayColor: MaterialStateProperty.all((Colors.grey[200])),
            elevation: MaterialStateProperty.all(0),
            shape: MaterialStateProperty.all(RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
                side: const BorderSide(color: Color(0xff4C9EEB)))),
            backgroundColor: user.loggedInUser
                ? MaterialStateProperty.all((Colors.white))
                : MaterialStateProperty.all((Colors.white))),
        onPressed: () async {
          if (mounted) {
            setState(() {
              isShareLoading = true;
            });
            trackProfileShareOrInvite("facebook");
            await Utils.facebookShare(
                userId: user.id, context: context, source: "profile");
            setState(() {
              isShareLoading = false;
            });
          }
        },
        label: Text(context.getString(StringKey.inviteLabel),
            textAlign: TextAlign.center,
            textScaler: const TextScaler.linear(1.0),
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
            )),
        icon: SizedBox(
            height: 18,
            width: 18,
            child: isShareLoading
                ? Widgets.buttonLoader(size: 14, color: Colors.blue)
                : Image.asset("assets/images/action/fb_icon.png")));

    return Row(
      children: [
        Expanded(
          flex: 1,
          child: SizedBox(
              //width: double.infinity,
              height: 35,
              //margin: EdgeInsets.only(top: 10),
              child: whatsappButton),
        ),
        const SizedBox(width: 5),
        Expanded(flex: 1, child: SizedBox(height: 35, child: facebookButton))
      ],
    );
  }

  String selectBadgeBannerColor(BadgeBanner banner) {
    switch (banner) {
      case BadgeBanner.gold:
        return "g";
      case BadgeBanner.white:
        return "w";
      default:
        return "s";
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<Tab> myTabHeaders = <Tab>[
      Tab(text: context.getPluralizedString(StringKey.postLabel, 0)),
      Tab(text: context.getPluralizedString(StringKey.trendLabel, 0)),
    ];

    final myTabWidgets = <Widget>[
      UserPosts(
        user: user,
        pageLoaded: pageLoadedCallBack,
        padding: widget.padding,
      ),
//      UserMentions(user: this.user),
      UserLikes(user: user, padding: widget.padding),
      // UserFollowers(user: this.user),
      // UserFollowing(userId: this.user.id),
    ];

    List<String> permissions = [];
    Utils.getUserPermissions().then((list) {
      permissions = list;
    });
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: DefaultTabController(
        length: myTabHeaders.length,
        child: NestedScrollView(
          controller: widget.scrollController,
          physics: const BouncingScrollPhysics(parent: BouncingScrollPhysics()),
          // allows you to build a list of elements that would be scrolled away till the body reached the top
          headerSliverBuilder: (context, _) {
            return [
              SliverList(
                delegate: SliverChildListDelegate([
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 15, horizontal: 20),
                    child: Stack(
                      children: [
                        getReferralButton(),
                        Column(
                          children: [
                            Container(
                              child: user.loggedInUser
                                  ? const AppUserEditableAvatar(
                                      source: "profile",
                                      size: 120,
                                      showCameraAnimation: true,
                                      uploadIconSize: 36)
                                  : user.loggedInUserBlocked
                                      ? NameAvatar(
                                          name: user.name,
                                          size: 120,
                                          color: HexColor.fromHex(
                                              user.avatarColor),
                                        )
                                      : GestureDetector(
                                          onTap: () {
                                            AppAnalytics.logEvent(
                                                name: 'clicked_image_profile',
                                                parameters: {
                                                  "user_id": user.id
                                                });
                                            Utils.openUserPhoto(
                                                context, user.photo);
                                          },
                                          child: UserAvatar.fromIdentity(
                                            user.toIdentity(),
                                            size: 120,
                                          ),
                                        ),
                            ),
                            const SizedBox(height: 5),
                            (user.name.isNotEmpty)
                                ? LimitedBox(
                                    maxWidth:
                                        MediaQuery.of(context).size.width - 15,
                                    child: InkWell(
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      onTap: () {
                                        if (permissions.contains(superAdmin)) {
                                          showDialog<void>(
                                            context: context,
                                            barrierDismissible:
                                                false, // user must tap button!
                                            builder: (BuildContext context) {
                                              return AlertDialog(
                                                title: const Text(
                                                    'Super Admin Info'),
                                                content: SingleChildScrollView(
                                                  child: Table(
                                                    children: [
                                                      TableRow(
                                                        children: [
                                                          const TableCell(
                                                            child: Text(
                                                              "User ID",
                                                            ),
                                                          ),
                                                          TableCell(
                                                            child: Text(
                                                              user.id
                                                                  .toString(),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      TableRow(
                                                        children: [
                                                          const TableCell(
                                                            child: Text(
                                                              "Phone",
                                                            ),
                                                          ),
                                                          TableCell(
                                                            child: Text(
                                                              user.phone
                                                                  .toString(),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      if (user.createdAt !=
                                                          null)
                                                        TableRow(
                                                          children: [
                                                            const TableCell(
                                                              child: Text(
                                                                "Joined At",
                                                              ),
                                                            ),
                                                            TableCell(
                                                              child: Text(
                                                                user.createdAt!
                                                                    .toLocal()
                                                                    .toString(),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      TableRow(
                                                        children: [
                                                          const TableCell(
                                                            child:
                                                                Text("Circles"),
                                                          ),
                                                          TableCell(
                                                            child: Text(
                                                              // ignore: avoid_hardcoded_strings_in_ui
                                                              "'${user.circles.map((c) => c.name).join("', '")}'",
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                actions: <Widget>[
                                                  TextButton(
                                                    style: TextButton.styleFrom(
                                                        foregroundColor:
                                                            Colors.black),
                                                    child: const Text('Close'),
                                                    onPressed: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                  ),
                                                ],
                                              );
                                            },
                                          );
                                        }
                                      },
                                      child: Text(
                                        user.name,
                                        softWrap: true,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center,
                                        textScaler:
                                            const TextScaler.linear(1.0),
                                        style: const TextStyle(
                                          fontSize: 20.0,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                            (user.badge != null)
                                ? (user.badge!.badgeBanner != BadgeBanner.none)
                                    ? GestureDetector(
                                        onTap: () async {
                                          AppAnalytics.logEvent(
                                              name: 'clicked_badge',
                                              parameters: {
                                                "user_id": user.id,
                                                "source": user.loggedInUser
                                                    ? "my_profile"
                                                    : "user_profile",
                                                "badge_role":
                                                    user.badge!.badgeRole,
                                                "badge_description":
                                                    user.badge!.description,
                                                "badge_banner": user
                                                    .badge!.badgeBanner.name,
                                                "verification_status": user
                                                    .badge!
                                                    .verificationStatus
                                                    .name,
                                              });
                                          if (user.badge!.verificationText
                                              .isEmpty) return;
                                          await _openVerificationExplainerSheet(
                                              "badge");
                                        },
                                        child: Column(
                                          children: [
                                            const SizedBox(height: 3),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                // Padding left : icon size(18) + Space Between badge strip and icon (16)_isUnverifiedUser
                                                Padding(
                                                  padding: EdgeInsets.only(
                                                      left: _isUnverifiedUser
                                                          ? 34.0
                                                          : 0.0),
                                                  child: BadgeStripProfile(
                                                    user.badge,
                                                    showShimmer: true,
                                                    enableShimmer:
                                                        hasUserPostsLoaded,
                                                  ),
                                                ),
                                                if (_isUnverifiedUser) ...[
                                                  const SizedBox(width: 16),
                                                  InkWell(
                                                    onTap: () async {
                                                      await _openVerificationExplainerSheet(
                                                          "info_icon");
                                                    },
                                                    child: const Padding(
                                                      padding: EdgeInsets.only(
                                                          top: 8.0),
                                                      child: Icon(
                                                        Icons.info_outline,
                                                        color: Colors.grey,
                                                        size: 18,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                            const SizedBox(height: 5),
                                          ],
                                        ),
                                      )
                                    : const SizedBox()
                                : const SizedBox(),
                            BadgeBannerTag(
                              badge: user.badge,
                              isProfilePage: true,
                            ),
                            //getCommonBadgeBanner(user),
                            if (user.shortBio.isNotEmpty) ...[
                              const SizedBox(height: 5),
                              Align(
                                alignment: Alignment.center,
                                child: Text(
                                  user.shortBio.toString(),
                                  maxLines: 2,
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                  softWrap: true,
                                  style: const TextStyle(
                                      //fontStyle: FontStyle.,
                                      ),
                                ),
                              )
                            ],
                            const SizedBox(height: 5),
                            user.village != null && !user.loggedInUserBlocked
                                ? InkWell(
                                    onTap: () => _openCirclePage(),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: <Widget>[
                                        Icon(
                                          Icons.location_on,
                                          size: 20.0,
                                          color: Colors.grey.shade600,
                                        ),
                                        const SizedBox(width: 2),
                                        Text(
                                          user.village!.name.toString(),
                                          style: const TextStyle(
                                            fontSize: 14.0,
                                            fontWeight: FontWeight.normal,
                                            color: Colors.black87,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : Container(),
                            const SizedBox(height: 5),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: <Widget>[
                                          Text(
                                            user.postsCount.toDisplayFormat(),
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                            context.getPluralizedString(
                                                StringKey.postLabel, 0),
                                            style:
                                                const TextStyle(fontSize: 14),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: InkWell(
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (BuildContext context) =>
                                                  UserFollowers(
                                                userId: user.id,
                                                loggedInUser: user.loggedInUser,
                                              ),
                                            ),
                                          );
                                        },
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Text(
                                              followersCount.toDisplayFormat(),
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(
                                              height: 2,
                                            ),
                                            Text(
                                              context.getPluralizedString(
                                                  StringKey
                                                      .userFollowersCountSuffixText,
                                                  0),
                                              style:
                                                  const TextStyle(fontSize: 14),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: InkWell(
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (BuildContext context) =>
                                                  UserFollowing(
                                                userId: user.id,
                                              ),
                                            ),
                                          );
                                        },
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: <Widget>[
                                            Text(
                                              user.followingCount
                                                  .toDisplayFormat(),
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(
                                              height: 2,
                                            ),
                                            Text(
                                              context.getString(
                                                  StringKey.followingUserLabel),
                                              style:
                                                  const TextStyle(fontSize: 14),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                user.loggedInUser
                                    ? _myProfileActions(context)
                                    : _otherProfileActions(context),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  )
                ]),
              ),
            ];
          },
          // You tab view goes here
          body: user.loggedInUserBlocked
              ? const SizedBox()
              : Container(
                  color: Colors.white,
                  child: Column(
                    children: <Widget>[
                      const Divider(
                        indent: 0,
                        thickness: 1,
                        height: 0,
                      ),
                      TabBar(
                        tabs: myTabHeaders,
                        labelPadding: EdgeInsets.zero,
                      ),
                      const Divider(
                        indent: 0,
                        thickness: 1,
                        height: 0,
                      ),
                      Expanded(
                        child: TabBarView(
                          children: myTabWidgets,
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }
}
