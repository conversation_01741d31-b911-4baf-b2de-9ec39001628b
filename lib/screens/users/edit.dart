import 'dart:core';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get_it/get_it.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart' as p;
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/media_picker/media_picker.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/user_gender_item.dart';
import 'package:praja/models/v2/local_image.dart';
import 'package:praja/screens/users/bloc/profile_bloc.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/services/exceptions.dart';
import 'package:praja/services/user/user_service.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets.dart';

enum PhotoSource {
  camera,
  gallery,
}

enum AvatarType {
  newImage,
  oldImage,
  initials,
}

class UserEdit extends StatefulWidget {
  final int? id;

  const UserEdit({
    super.key,
    this.id,
  });

  @override
  State<UserEdit> createState() => _UserEditState();
}

class _UserEditState extends State<UserEdit> {
  late AppUser _user;

  final nameController = TextEditingController();
  final shortBioController = TextEditingController();
  final dobController = TextEditingController();
  final aliasController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();

  bool _loading = true;
  bool _saveLoading = false;
  AvatarType avatarType = AvatarType.initials;

  DateTime? dob;
  LocalImage? _localImage;

  bool isGenderChanged = false;
  bool isPhotoRemoved = false;

  final UserService _userService = GetIt.I.get<UserService>();

  String _getLabel(String key) {
    if (key == "male") {
      return context.getString(StringKey.maleLabel);
    } else if (key == "female") {
      return context.getString(StringKey.femaleLabel);
    } else {
      return context.getString(StringKey.otherLabel);
    }
  }

  List<UserGenderItem> genderList = [
    UserGenderItem(id: 0, name: 'male'),
    UserGenderItem(id: 1, name: "female"),
    UserGenderItem(id: 2, name: "other"),
  ];

  _UserEditState();

  @override
  void dispose() {
    super.dispose();

    // Clean up the controller when the Widget is disposed
    nameController.dispose();
    shortBioController.dispose();
    dobController.dispose();
    aliasController.dispose();
    emailController.dispose();
    phoneController.dispose();
  }

  @override
  void initState() {
    super.initState();

    _loadUserDetails();
  }

  Future<void> _loadUserDetails() async {
    try {
      final user = await _userService.fetchCurrentLoggedInUser(widget.id);

      if (mounted) {
        setState(() {
          _user = user;
          _loading = false;
        });

        nameController.text = _user.name;
        shortBioController.text = _user.shortBio;
        phoneController.text = _user.phone.toString();
        final dob = _user.dob;
        dobController.text =
            dob != null ? DateFormat("dd/MM/yyyy").format(dob) : "";
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();
      }

      return Utils.showToast(localisedErrorMessage(e));
    }
  }

  Future<void> _saveProfileRequest() async {
    var errorMsg = "";
    if (nameController.text.isEmpty) {
      errorMsg = 'మీ పేరు ఖాళీ గా ఉండకూడదు'; // Name cannot be blank
    }

    if (errorMsg.isNotEmpty) {
      return Utils.showToast(errorMsg);
    }

    try {
      setState(() => _saveLoading = true);

      AppUser requestUser = _user.copyWith(
        name: nameController.text,
        shortBio: shortBioController.text,
        dob: () => dob,
      );
      await _userService.saveUserDetails(requestUser, localImage: _localImage);
      AppAnalytics.logEvent(name: "save_edit_profile", parameters: {
        "is_name_changed": nameController.text != _user.name,
        "is_bio_changed": shortBioController.text != _user.shortBio,
        "is_gender_changed": isGenderChanged,
        "is_photo_changed": isPhotoRemoved || _localImage != null,
        "is_dob_changed": dob != _user.dob,
      });

      setState(() => _saveLoading = false);
      //Reloads the profile page
      if (mounted) {
        BlocProvider.of<ProfileBloc>(context).add(FetchProfile());
        Navigator.of(context).pop();
      }
    } catch (e) {
      AppAnalytics.logEvent(name: "save_edit_profile_failed", parameters: {
        "error": e.toString(),
      });
      if (mounted) {
        setState(() => _saveLoading = false);
      }
      return Utils.showToast(localisedErrorMessage(e));
    }
  }

  Future _chooseImage() async {
    try {
      final MediaPickerResult? result = await pickMedia(
          context: context, allowVideo: false, allowMultipleImages: false);

      if (result == null) {
        throw NoImageSelectedException();
      }

      final imageList = result.files;
      if (imageList.isNotEmpty) {
        File originalFile = File(imageList.first.path);
        String filename = p.basenameWithoutExtension(originalFile.path);
        String ext = p.extension(originalFile.path);
        String filePath = p.dirname(originalFile.path);

        String newFilePath = "$filePath/$filename-compressed$ext";

        CompressFormat? format;
        if (ext == ".jpg" || ext == ".jpeg") {
          format = CompressFormat.jpeg;
        } else if (ext == ".png") {
          format = CompressFormat.png;
        } else if (ext == ".heic") {
          format = CompressFormat.heic;
        } else if (ext == ".webp") {
          format = CompressFormat.webp;
        }

        if (format == null) {
          throw UnsupportedError("Unsupported file extension format");
        }

        final result = await FlutterImageCompress.compressAndGetFile(
          originalFile.path,
          newFilePath,
          quality: await Utils.getImageCompressionQuality(),
          format: format,
        );

        ///compression result could be null.
        ///original file is used in that case
        if (result != null) {
          originalFile = File(result.path);
        }

        var photo = LocalImage(path: originalFile.path);

        if (context.mounted) {
          CroppedFile? croppedFile = await ImageCropper().cropImage(
              aspectRatio: const CropAspectRatio(
                ratioX: 1,
                ratioY: 1,
              ),
              sourcePath: photo.path,
              uiSettings: [
                AndroidUiSettings(
                  toolbarColor: Theme.of(context).primaryColor,
                  toolbarWidgetColor: Colors.white,
                  statusBarColor: Theme.of(context).primaryColor,
                  activeControlsWidgetColor: Theme.of(context).primaryColor,
                  cropStyle: CropStyle.circle,
                ),
                IOSUiSettings(
                  cropStyle: CropStyle.circle,
                  aspectRatioLockEnabled: true,
                ),
              ]);
          if (croppedFile != null) {
            photo = photo.copyWith(path: croppedFile.path);
            setState(() {
              avatarType = AvatarType.newImage;
              _localImage = photo;
            });
          }
        }
      }
    } catch (e, st) {
      if (e is NoImageSelectedException) {
        Utils.showToast("చిత్రం ఎంచుకోబడలేదు!");
      } else {
        Utils.showToast(localisedErrorMessage(e));
        logNonFatalIfAppError(
            "Error while picking + compressing + cropping profile picture", e,
            stackTrace: st);
      }
    }
  }

  void _selectDate() async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 12)),
      firstDate: DateTime(1930),
      lastDate: DateTime.now().subtract(const Duration(days: 365 * 12)),
      builder: (BuildContext context, Widget? child) {
        if (child == null) return Container();
        return Theme(
          data: ThemeData.dark(useMaterial3: false),
          child: child,
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        dob = selectedDate;
        dobController.text = DateFormat("dd/MM/yyyy").format(selectedDate);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Widgets.renderCircularLoader(context);
    }

    final name = TextFormField(
      controller: nameController,
      autofocus: false,
      keyboardType: TextInputType.text,
      decoration: InputDecoration(
        hintText: context.getString(StringKey.nameLabel),
        contentPadding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );

    final phone = TextFormField(
      enabled: false,
      controller: phoneController,
      autofocus: false,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        hintText: context.getString(StringKey.phoneNumberLabel),
        contentPadding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );

    final shortBio = TextFormField(
      controller: shortBioController,
      autofocus: false,
      keyboardType: TextInputType.text,
      decoration: InputDecoration(
        hintText: context.getString(StringKey.shortBioLabel),
        contentPadding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );

    final dobField = InkWell(
      onTap: _selectDate,
      borderRadius: BorderRadius.circular(5),
      child: IgnorePointer(
        child: TextFormField(
          controller: dobController,
          decoration: InputDecoration(
            hintText: context.getString(StringKey.birthdayLabel),
            contentPadding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
            ),
          ),
          maxLength: 10,
          enabled: false,
        ),
      ),
    );

    final genderDropDown = DropdownButtonFormField<int>(
      value: _user.gender,
      hint: Text(context.getString(StringKey.genderLabel)),
      onChanged: (int? newValue) {
        if (newValue != null) {
          setState(() {
            isGenderChanged = _user.gender != newValue;
            _user = _user.copyWith(gender: newValue);
          });
        }
      },
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.fromLTRB(20.0, 0.0, 20.0, 0.0),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
        ),
      ),
      items: genderList.map((UserGenderItem value) {
        return DropdownMenuItem<int>(
          value: value.id,
          child: Text(_getLabel(value.name)),
        );
      }).toList(),
    );

    final submitButton = SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
          ),
          padding: const EdgeInsets.all(12),
        ),
        onPressed: !_saveLoading ? _saveProfileRequest : () {},
        child: _saveLoading
            ? Widgets.buttonLoader()
            : Text(
                context.getString(StringKey.saveProfileChangesButtonLabel),
                style: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(color: Colors.white),
              ),
      ),
    );

    Widget? avatar;
    ImageProvider? backgroundImage;

    if (_localImage != null) {
      avatarType = AvatarType.newImage;
    } else if (_user.photo != null) {
      avatarType = AvatarType.oldImage;
    }

    switch (avatarType) {
      case AvatarType.initials:
        avatar = Text(
          Utils.firstLetterExtraction(_user.name),
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 35.0),
        );
        break;

      case AvatarType.oldImage:
        backgroundImage = CachedNetworkImageProvider(
          _user.photo!.url,
          cacheManager: AppCacheManager.instance,
        );
        break;
      case AvatarType.newImage:
        backgroundImage =
            _localImage != null ? FileImage(File(_localImage!.path)) : null;
        break;
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: Builder(
        // Create an inner BuildContext so that the onPressed methods
        // can refer to the Scaffold with Scaffold.of().
        builder: (BuildContext context) {
          return Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    SizedBox(
                      width: 100,
                      height: 100,
                      child: Stack(
                        children: <Widget>[
                          CircleAvatar(
                            radius: 50.0,
                            backgroundImage: backgroundImage,
                            child: avatar,
                          ),
                          Container(
                            decoration: BoxDecoration(
                              color: const Color.fromRGBO(0, 0, 0, 0.25),
                              borderRadius: BorderRadius.circular(50.0),
                            ),
                            child: Center(
                              child: IconButton(
                                icon: const Icon(
                                  Icons.camera_alt,
                                  color: Colors.white,
                                ),
                                onPressed: _chooseImage,
                              ),
                            ),
                          ),
                          _user.photo != null
                              ? Positioned(
                                  top: 65,
                                  left: 65,
                                  child: IconButton(
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        isPhotoRemoved = true;
                                        avatarType = AvatarType.initials;
                                        _localImage = null;
                                        _user = _user.copyWith(
                                          photoProvider: () => null,
                                        );
                                      });
                                    },
                                  ),
                                )
                              : Container(),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10.0),
                    name,
                    const SizedBox(height: 10.0),
                    // email,
                    // SizedBox(height: 10.0),
                    phone,
                    const SizedBox(height: 10.0),
                    shortBio,
                    const SizedBox(height: 10.0),
                    genderDropDown,
                    const SizedBox(height: 10.0),
                    dobField,
                    // alias,
                    // SizedBox(height: 20.0),
                    submitButton,
//                    SizedBox(height: 20.0),
//                    signOutButton,
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
