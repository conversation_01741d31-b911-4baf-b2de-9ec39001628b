import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/report_item.dart';
import 'package:praja/presentation/multi_select_chip.dart';
import 'package:praja/services/user/user_service.dart';
import 'package:praja/utils/utils.dart';

class UserBlockDialog extends StatefulWidget {
  final int userId;

  const UserBlockDialog(this.userId, {Key? key}) : super(key: key);

  @override
  State<UserBlockDialog> createState() => _UserBlockDialogState();
}

class _UserBlockDialogState extends State<UserBlockDialog> {
  ReportItem? _selectedReportChoice;
  bool _reporting = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Padding(
          padding: const EdgeInsets.only(left: 4),
          child: Text(
            context.getString(StringKey.blockLabel), // block
            style: const TextStyle(
              fontSize: 18.0,
              fontWeight: FontWeight.bold,
            ),
          )),
      content: MultiSelectChip(
        list: Utils.getUserReportItems(),
        onSelected: (ReportItem value) {
          _selectedReportChoice = value;
        },
      ),
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(100, 35),
            backgroundColor: Colors.white,
            foregroundColor: Theme.of(context).textTheme.bodyMedium?.color,
          ),
          child: Text(
            context.getString(StringKey.noLabel),
            style: const TextStyle(fontSize: 12.0),
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(100, 35),
            backgroundColor: Theme.of(context).primaryColor,
          ),
          child: _reporting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    color: Colors.white,
                  ))
              : Text(
                  context.getString(StringKey.blockLabel), // block
                  style: const TextStyle(fontSize: 12.0),
                ),
          onPressed: () async {
            final navigator = Navigator.of(context);
            final choice = _selectedReportChoice;
            if (choice != null) {
              setState(() => _reporting = true);

              AppAnalytics.logEvent(
                name: "block",
                parameters: {
                  "content_type": "user",
                  "user_id": widget.userId.toString(),
                  "report_reason": choice.name.toString(),
                },
              );
              bool isSuccess = false;
              final userService = GetIt.I.get<UserService>();
              try {
                final message =
                    await userService.blockUser(widget.userId, choice);
                isSuccess = true;
                Utils.showToast(message);
              } catch (e) {
                Utils.showToast(localisedErrorMessage(e));
                isSuccess = false;
              }
              setState(() => _reporting = false);
              navigator.pop(isSuccess);
            } else {
              Utils.showToast(context.getString(StringKey.selectReasonToast,
                  listen: false));
            }
          },
        ),
      ],
    );
  }
}
