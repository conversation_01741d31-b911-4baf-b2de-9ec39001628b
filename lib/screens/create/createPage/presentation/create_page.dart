import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/common/app_event_bus.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/post/create/models/post_attachment_creative.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/models/comment_option.dart';
import 'package:praja/models/hashtag.dart';
import 'package:praja/models/post.dart';
import 'package:praja/presentation/media_carousel.dart';
import 'package:praja/screens/create/createPage/bloc/create_bloc.dart';
import 'package:praja/screens/create/createPage/bloc/create_bloc_events.dart';
import 'package:praja/screens/create/createPage/bloc/create_bloc_states.dart';
import 'package:praja/screens/create/createPage/presentation/widgets/comment_options_widget.dart';
import 'package:praja/screens/create/createPage/presentation/widgets/content_textfield.dart';
import 'package:praja/screens/create/createPage/presentation/widgets/photo_item.dart';
import 'package:praja/screens/create/createPage/presentation/widgets/tag_cirlce_avatar.dart';
import 'package:praja/screens/create/createPage/presentation/widgets/video_item.dart';
import 'package:praja/screens/create/tagPage/presentation/tag_page.dart';
import 'package:praja/screens/create/tagPage/presentation/widgets/taggedItem.dart';
import 'package:praja/screens/posts/list_item.dart';
import 'package:praja/screens/posts/share_card/post_share_card_screen.dart';
import 'package:praja/screens/posts/speech_dialog.dart';
import 'package:praja/services/post/post_service.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets.dart';
import 'package:shimmer/shimmer.dart';

import 'widgets/crop_button.dart';
import 'widgets/remove_button.dart';

class CreatePostPage extends BasePage {
  const CreatePostPage(
      {super.key,
      this.shareData = const {},
      this.hashtag,
      this.source,
      this.onSuccess,
      this.resharedPost,
      this.attachmentCreative,
      this.postToCircle});
  final Map<String, dynamic> shareData;
  final Post? resharedPost;
  final void Function()? onSuccess;
  final Circle? postToCircle;
  final String? source;
  final Hashtag? hashtag;
  final PostAttachmentCreative? attachmentCreative;

  @override
  String get pageName => 'create_post';
  @override
  Map<String, dynamic> get pageParams => {
        'hashtag': hashtag?.name,
        'source': source,
        'reshared_post_id': resharedPost?.id,
        'circle_id': postToCircle?.id,
        'attachment_creative_id': attachmentCreative?.id,
      };

  @override
  Widget buildContent(BuildContext context) {
    return BlocProvider(
      create: (context) => CreateBloc()
        ..add(CreateBegin(
            shareData: shareData,
            hashtag: hashtag,
            postToCircle: postToCircle,
            resharedPost: resharedPost,
            attachmentCreative: attachmentCreative)),
      child: CreatePostView(
        onSuccess: onSuccess,
      ),
    );
  }
}

final myImageListKey = GlobalKey<AnimatedListState>();

class CreatePostView extends StatelessWidget {
  final void Function()? onSuccess;

  CreatePostView({super.key, this.onSuccess});

  final PostService _postService = GetIt.I.get<PostService>();

  topWidget(BuildContext context) {
    final bloc = BlocProvider.of<CreateBloc>(context);
    final createBlocState = bloc.state;
    return Container(
      decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(
        width: 0.7,
        color: Colors.grey.shade200,
      ))),
      width: MediaQuery.of(context).size.width,
      height: 55,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 10, right: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () async {
                    if (bloc.canPopDirectly()) {
                      Navigator.of(context).pop();
                    } else {
                      final value = await _onWillPop(context);
                      if (value != null && (value == true) && context.mounted) {
                        Navigator.of(context).pop();
                      }
                    }
                  },
                  borderRadius: BorderRadius.circular(5),
                  child: const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Icon(Icons.arrow_back_rounded),
                  ),
                ),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: (createBlocState.initialDataLoaded)
                      ? !createBlocState.submissionInProcess
                          ? InkWell(
                              borderRadius: BorderRadius.circular(5),
                              onTap: () {
                                bloc.add(SubmitPost(context: context));
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 2, horizontal: 10),
                                child: Transform.rotate(
                                  angle: 1.5,
                                  child: Icon(
                                    Icons.navigation_rounded,
                                    color: Theme.of(context).primaryColor,
                                    size: 35,
                                  ),
                                ),
                              ),
                            )
                          : Widgets.buttonLoader()
                      : const SizedBox(),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  _onWillPop(BuildContext context) async {
    final createBloc = BlocProvider.of<CreateBloc>(context);

    createBloc.textContentFocusNode.unfocus();

    bool? shouldReturn = await showModalBottomSheet<bool>(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10), topRight: Radius.circular(10))),
        context: context,
        builder: (context) {
          return Wrap(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.only(
                    top: 20, left: 20, right: 20, bottom: 30),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.getString(StringKey.wantToLeaveText),
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.bold),
                    ), // Are you sure to leave?
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      context.getString(StringKey.youWillLoseChangesText),
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[500]),
                    ), //
                    const SizedBox(
                      height: 20,
                    ),
                    LayoutBuilder(builder: (context, constraints) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ElevatedButton(
                            style: ButtonStyle(
                              backgroundColor:
                                  MaterialStateProperty.all(Colors.grey[200]),
                              minimumSize: MaterialStateProperty.all(
                                  Size(constraints.maxWidth / 2.12, 0)),
                              elevation: MaterialStateProperty.all(0),
                            ),
                            onPressed: () {
                              Navigator.of(context).pop(false);
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: Text(
                                context.getString(StringKey.noAlternateLabel),
                                style: TextStyle(
                                    fontSize: 15,
                                    color: Colors.grey[800],
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                          ElevatedButton(
                              style: ButtonStyle(
                                  elevation: MaterialStateProperty.all(0),
                                  backgroundColor: MaterialStateProperty.all(
                                      Colors.redAccent),
                                  minimumSize: MaterialStateProperty.all(
                                      Size(constraints.maxWidth / 2.12, 0))),
                              onPressed: () {
                                Navigator.of(context).pop(true);
                              },
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 10),
                                child: Text(
                                    context.getString(StringKey.yesLabel),
                                    style: const TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.bold)),
                              ))
                        ],
                      );
                    })
                  ],
                ),
              ),
            ],
          );
        });
    shouldReturn ??= false;
    return shouldReturn;
  }

  postCircleWidget({required BuildContext context}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Align(
        alignment: Alignment.topLeft,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: const Color(0xffD6DBE5))),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                'assets/images/create/map_icon.png',
                width: 22,
              ),
              const SizedBox(
                width: 6,
              ),
              Text(
                context.getString(StringKey.publicLabel),
                style: const TextStyle(fontSize: 12, color: Color(0xff7C8494)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  postPreviewWidget(BuildContext context, CreateState state,
      {required Function(CreateEvent) onEvent}) {
    final reSharedPost = state.resharedPost;
    if (state.isReshare && reSharedPost != null) {
      return Padding(
        padding:
            const EdgeInsets.only(left: 60, right: 20, bottom: 10, top: 10),
        child: Stack(
          children: [
            IgnorePointer(
                child: ParentPostPreview(
                    parentPost: reSharedPost,
                    width: MediaQuery.of(context).size.width - 80)),
            Positioned(
                right: 0,
                top: 5,
                child: RemoveButton(
                  onTap: () {
                    onEvent(RemoveReSharedPost());
                  },
                )),
          ],
        ),
      );
    }
    return const SizedBox();
  }

  postContentWidget(
      {required BuildContext context,
      required CreateState state,
      required Function(CreateEvent) onEvent,
      required FocusNode textContentFocusNode,
      required TextEditingController textController}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Container(
        padding: const EdgeInsets.only(left: 0, right: 0, top: 5, bottom: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 5, top: 0),
                    child: ContentTextField(
                        textController: textController,
                        textContentFocusNode: textContentFocusNode,
                        onChanged: (text) {
                          onEvent(HashtagSearch(text));
                        }),
                  ),
                ),
                InkWell(
                  borderRadius: BorderRadius.circular(5),
                  onTap: () {
                    AppAnalytics.logEvent(
                        name: "speech_mic_clicked",
                        parameters: {"source": state.source});
                    showSpeechDialog(context, textController);
                  },
                  child: Container(
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(5)),
                    child: const Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      child: Icon(
                        Icons.mic,
                        size: 22,
                        color: Color(0xffC6C6C6),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssetRemoveButton(
      BuildContext context, int index, CreateBloc bloc) {
    return AssetRemoveButton(
      onTap: () {
        bloc.add(RemoveImage(bloc.state, index));
      },
    );
  }

  Widget _buildCropButton(BuildContext context, int index, CreateBloc bloc) {
    final state = bloc.state;
    final photos = state.photosList;
    final photo = photos[index];

    return CropButton(photo: photo);
  }

  Widget getMediaWidget(
      BuildContext context, CreateState state, CreateBloc bloc) {
    final photos = state.photosList;
    final videos = state.videosList;
    final attachmentCreative = state.attachmentCreative;

    if (videos.isNotEmpty) {
      List<Widget> media = [];
      List<StaggeredTile> mediaStaggeredTiles = [];

      for (int i = 0; i < videos.length; i++) {
        media.add(VideoItem(
          video: videos[i],
          index: i,
        ));
        mediaStaggeredTiles.add(const StaggeredTile.count(2, 1.2));
      }

      final mediaWidget = Padding(
        padding: const EdgeInsets.only(top: 0, left: 70, right: 20),
        child: StaggeredGridView.count(
          shrinkWrap: true,
          primary: false,
          crossAxisCount: 2,
          mainAxisSpacing: 4.0,
          crossAxisSpacing: 4.0,
          physics: const NeverScrollableScrollPhysics(),
          staggeredTiles: mediaStaggeredTiles,
          padding: EdgeInsets.zero,
          children: media,
        ),
      );

      return mediaWidget;
    } else if (photos.isNotEmpty) {
      final mediaCarouselItems =
          photos.map((photo) => photo.toMediaCarouselItem()).toList();

      return MediaCarousel(
        items: mediaCarouselItems,
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.only(top: 0, left: 70, right: 20),
        topRightOptionBuilder: (BuildContext context, int index) {
          return _buildAssetRemoveButton(context, index, bloc);
        },
        bottomRightOptionBuilder: (BuildContext context, int index) {
          return _buildCropButton(context, index, bloc);
        },
      );
    } else if (attachmentCreative != null) {
      final mediaCarouselItems = [attachmentCreative.toMediaCarouselItem()];
      return MediaCarousel(
        items: mediaCarouselItems,
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.only(top: 0, left: 70, right: 20),
        topRightOptionBuilder: (BuildContext context, int index) {
          return AssetRemoveButton(
            onTap: () {
              bloc.add(RemoveCreativeAttachment(bloc.state));
            },
          );
        },
      );
    } else {
      return const SizedBox();
    }
  }

  postImagesList(BuildContext context, CreateState state) {
    final photos = state.photosList;
    return Container(
      padding: const EdgeInsets.only(right: 20),
      width: MediaQuery.of(context).size.width / 1.2,
      child: AnimatedList(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          key: myImageListKey,
          itemBuilder: (context, index, animation) {
            return Column(
              children: [
                ScaleTransition(
                  scale: animation,
                  child: PhotoItem(
                    photo: photos[index],
                    index: index,
                  ),
                ),
                const SizedBox(
                  height: 10,
                )
              ],
            );
          },
          initialItemCount: photos.length),
    );
  }

  postVideoAttachmentsList(BuildContext context, CreateState state) {
    final videos = state.videosList;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        transitionBuilder: (child, animation) {
          return ScaleTransition(
            scale: animation,
            child: child,
          );
        },
        child: videos.isEmpty
            ? const SizedBox()
            : SizedBox(
                width: MediaQuery.of(context).size.width / 1.2,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: videos.length,
                  physics: const BouncingScrollPhysics(),
                  scrollDirection: Axis.vertical,
                  itemBuilder: (context, index) {
                    return VideoItem(video: videos[index], index: index);
                  },
                ),
              ),
      ),
    );
  }

  postAttachedMediaList(BuildContext context, CreateState state) {
    final bloc = BlocProvider.of<CreateBloc>(context);
    return getMediaWidget(context, state, bloc);
  }

  postAttachmentsActionWidget(BuildContext context, CreateState state,
      {required Function(CreateEvent) onEvent}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 18),
      child: Row(
        children: [
          attachmentItemWidget('', Icons.camera_alt_outlined, () {
            onEvent.call(AddImagesFromGallery(state, context));
          }, context, isDisabled: state.disableImage),
          const SizedBox(
            width: 20,
          ),
          attachmentItemWidget('', Icons.videocam_outlined, () {
            onEvent.call(AddVideoFromGallery(state, context));
          }, context, isDisabled: state.disableVideo),
          const SizedBox(
            width: 20,
          ),
        ],
      ),
    );
  }

  attachmentItemWidget(String title, IconData iconData, void Function()? onTap,
      BuildContext context,
      {bool containsNewTag = false,
      bool showImageIcon = false,
      Widget? imageWidget,
      bool isDisabled = false}) {
    return Opacity(
      opacity: isDisabled ? 0.3 : 1,
      child: InkWell(
        borderRadius: BorderRadius.circular(100),
        onTap: isDisabled ? null : onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
          child: Column(
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  Material(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(100)),
                    child: Container(
                      height: 46,
                      width: 46,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                      ),
                      child: !showImageIcon
                          ? Icon(
                              iconData,
                              size: 25,
                            )
                          : imageWidget,
                    ),
                  ),
                  containsNewTag
                      ? Positioned(
                          top: -10,
                          right: -10,
                          child: BlinkWrapper(
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 5, vertical: 2),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(3),
                                color: Colors.blue[400],
                              ),
                              child: const Text(
                                'New',
                                style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.white,
                                    letterSpacing: 1),
                              ),
                            ),
                          ),
                        )
                      : const SizedBox()
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  tagGroupsWidget(
      {required BuildContext context,
      required CreateState state,
      required FocusNode textContentFocusNode,
      required Function(CreateEvent) onEvent}) {
    final taggedCircles = state.taggedCircles;
    final tagData = state.tagData;
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: state.initialDataLoaded
          ? Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: Column(
                children: [
                  InkWell(
                    onTap: () async {
                      if (textContentFocusNode.hasFocus) {
                        textContentFocusNode.unfocus();
                      }
                      AppAnalytics.logEvent(
                          name: "add_groups",
                          parameters: {"source": "post_creation"});
                      if (tagData != null) {
                        final TagPageResult? result =
                            await Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => TagPage(
                              tagData: tagData,
                              preselectedCircles: taggedCircles,
                            ),
                          ),
                        );
                        if (result != null) {
                          onEvent(AddTaggedCircles(
                              taggedCircles: result.taggedCircles));

                          if (result.hasAcceptedRules) {
                            onEvent(UserAgreedRules());
                          }

                          if (result.hasAcceptedWarning) {
                            onEvent(UserAgreedWarning());
                          }
                        }
                      }
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: MediaQuery.of(context).size.width,
                          height: 0.5,
                          color: Colors.grey[200],
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 20, right: 15),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Expanded(
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    tagData == null
                                        ? const SizedBox()
                                        : Text(
                                            tagData.tagHeaderText,
                                            style: const TextStyle(height: 2),
                                          ),
                                    const Icon(
                                      Icons.arrow_forward_ios_rounded,
                                      size: 15,
                                    ),
                                  ],
                                ),
                              ),
                              KeyboardVisibilityBuilder(builder:
                                  (BuildContext context,
                                      bool isKeyboardVisible) {
                                return isKeyboardVisible
                                    ? Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10),
                                        height: 30,
                                        child: ListView.separated(
                                          shrinkWrap: true,
                                          scrollDirection: Axis.horizontal,
                                          separatorBuilder: (context, index) {
                                            return const SizedBox(
                                              width: 10,
                                            );
                                          },
                                          itemBuilder: (context, index) {
                                            return TagCircleAvatar(
                                              tagCircle: taggedCircles[index],
                                              avatarRadius: 14,
                                            );
                                          },
                                          itemCount: taggedCircles.length,
                                        ))
                                    : const SizedBox(
                                        width: 0,
                                      );
                              }),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width,
                          height: 0.5,
                          color: Colors.grey[200],
                        ),
                      ],
                    ),
                  ),
                  Builder(builder: (context) {
                    return KeyboardVisibilityBuilder(
                      builder: (BuildContext context, bool isKeyboardVisible) {
                        return !isKeyboardVisible
                            ? ConstrainedBox(
                                constraints: BoxConstraints(
                                  minHeight: 0,
                                  maxHeight:
                                      MediaQuery.of(context).size.height * 0.2,
                                ),
                                child: ListView.separated(
                                    physics: const BouncingScrollPhysics(),
                                    shrinkWrap: true,
                                    itemBuilder: (context, index) {
                                      return Column(
                                        children: [
                                          TaggedItem(
                                            tagCircle: taggedCircles[index],
                                            isRemovable: true,
                                            onRemoveTap: () {
                                              BlocProvider.of<CreateBloc>(
                                                      context)
                                                  .add(RemoveTaggedCircle(
                                                      tagCircle: taggedCircles[
                                                          index]));
                                            },
                                          ),
                                          Container(
                                            height: 0.7,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            color: Colors.grey[200],
                                          )
                                        ],
                                      );
                                    },
                                    separatorBuilder: (context, index) {
                                      return const SizedBox();
                                    },
                                    itemCount: taggedCircles.length),
                              )
                            : const SizedBox();
                      },
                    );
                  }),
                  Container(
                    width: MediaQuery.of(context).size.width,
                    height: 0.5,
                    color: Colors.grey[200],
                  ),
                ],
              ),
            )
          : const SizedBox(),
    );
  }

  hashtagsWidget(
      {required BuildContext context,
      required CreateState state,
      required ScrollController hashtagScrollController}) {
    return Padding(
      padding: const EdgeInsets.only(left: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          hashtagsListWidget(
              context: context,
              state: state,
              hashtagScrollController: hashtagScrollController)
        ],
      ),
    );
  }

  hashtagsListWidget(
      {required BuildContext context,
      required CreateState state,
      required ScrollController hashtagScrollController}) {
    bool hashtagsLoaded = state.hashtagsLoaded;

    return SizedBox(
      height: 30,
      width: MediaQuery.of(context).size.width,
      child: ListView.separated(
        controller: hashtagScrollController,
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 0),
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        itemCount: hashtagsLoaded ? state.hashtagsList.length : 5,
        itemBuilder: (context, index) {
          return AnimatedSwitcher(
              transitionBuilder: (child, animation) {
                return FadeTransition(
                  opacity: animation,
                  //opacity: animation,
                  child: child,
                );
              },
              duration: const Duration(milliseconds: 300),
              child: Row(children: [
                hashtagsItem(
                    hashtag: hashtagsLoaded ? state.hashtagsList[index] : null,
                    forShimmer: hashtagsLoaded ? false : true,
                    context: context),
                (index == (hashtagsLoaded ? state.hashtagsList.length - 1 : 4))
                    ? const SizedBox(
                        width: 10,
                      )
                    : const SizedBox()
              ]));
        },
        separatorBuilder: (_, index) {
          return const SizedBox(
            width: 10,
          );
        },
      ),
    );
  }

  hashtagsItem(
      {Hashtag? hashtag,
      bool forShimmer = false,
      required BuildContext context}) {
    final hashtagName = hashtag?.name;
    return InkWell(
      onTap: hashtag == null
          ? null
          : () {
              BlocProvider.of<CreateBloc>(context).add(HashtagSelect(hashtag));
            },
      child: Container(
          key: UniqueKey(),
          padding: EdgeInsets.symmetric(horizontal: hashtag == null ? 0 : 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            color: const Color(0xffEDEDED),
          ),
          height: 28,
          width: hashtag == null ? MediaQuery.of(context).size.width / 3 : null,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Image.asset(
                'assets/images/create/Vector.png',
                height: 15,
              ),
              SizedBox(
                width: hashtag == null ? 0 : 10,
              ),
              hashtag == null
                  ? Shimmer.fromColors(
                      baseColor: Colors.grey.shade300,
                      highlightColor: Colors.grey.shade100,
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        height: 13,
                        width: 60,
                      ))
                  : hashtagName != null
                      ? Text(
                          // ignore: avoid_hardcoded_strings_in_ui
                          "#$hashtagName",
                          style: const TextStyle(fontSize: 11),
                        )
                      : const SizedBox(),
            ],
          )),
    );
  }

  contentWidget(
      {required BuildContext context,
      required CreateState state,
      required Function(CreateEvent) onEvent,
      required FocusNode textContentFocusNode,
      required TextEditingController textController}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        const SizedBox(
          height: 10,
        ),
        postCircleWidget(context: context),
        const SizedBox(
          height: 10,
        ),
        postContentWidget(
            context: context,
            state: state,
            onEvent: onEvent,
            textController: textController,
            textContentFocusNode: textContentFocusNode),
        const SizedBox(
          height: 10,
        ),
        postAttachedMediaList(context, state),
        const SizedBox(
          height: 10,
        ),
        postPreviewWidget(context, state, onEvent: onEvent)
      ],
    );
  }

  showPostCardDialogue(int id, BuildContext context) async {
    AssetImage uploadProfilePicImage =
        const AssetImage("assets/images/icons/Green-Blank-profile.png");
    AssetImage appShareIcon =
        const AssetImage("assets/images/icons/post-share-app-icon.png");
    try {
      Post post = await _postService.fetchByID(id);
      await precacheImage(uploadProfilePicImage, context);
      await precacheImage(appShareIcon, context);
      if (post.showPostShareCard && context.mounted) {
        await showGeneralDialog(
          context: context,
          barrierColor: Colors.black38,
          barrierLabel: 'PostShareCard',
          barrierDismissible: true,
          pageBuilder: (_, __, ___) => Center(
            child: Container(
              color: Colors.transparent,
              child: Material(
                  color: Colors.transparent,
                  child: PostShareCardScreen(
                    screenWidth: MediaQuery.of(context).size.width,
                    post: post,
                    user: post.user,
                    appPostShareCardIcon: appShareIcon,
                    uploadProfilePicImage: uploadProfilePicImage,
                  )),
            ),
          ),
        );
        bool hasPic = post.user.photo != null;
        AppAnalytics.logEvent(
            name: "post_share_card_shown",
            parameters: {"has_profile_picture": hasPic});
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  showCommentOptions(BuildContext context, CreateState state,
      {required Function(CreateEvent) onEvent}) async {
    final selectedCommentOption = state.selectedCommentOption;
    final commentsConfig = state.commentsConfig;
    if (selectedCommentOption == null || commentsConfig == null) {
      return;
    }
    final value = await showModalBottomSheet(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(10), topLeft: Radius.circular(10))),
        context: context,
        builder: (context) {
          return CommentOptionsWidget(
            commentsConfig: commentsConfig,
            selectedCommentOption: selectedCommentOption,
          );
        });

    if (value is CommentOption) {
      onEvent.call(UpdateCommentOption(selectedCommentOption: value));
    }
  }

  commentOptions(BuildContext context, CreateState state,
      {required Function(CreateEvent) onEvent}) {
    final selectedCommentOption = state.selectedCommentOption;
    return AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        child: !state.initialDataLoaded
            ? const SizedBox()
            : Padding(
                padding: const EdgeInsets.only(right: 5),
                child: InkWell(
                  borderRadius: BorderRadius.circular(100),
                  onTap: () {
                    final commentsConfig = state.commentsConfig;
                    if (commentsConfig == null) return;
                    showCommentOptions(context, state, onEvent: onEvent);
                    AppAnalytics.logEvent(
                        name: "open_comment_options",
                        parameters: {"source": "create"});
                  },
                  child: Container(
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(100)),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Flexible(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              SizedBox(
                                height: 15,
                                width: 15,
                                child: Image.asset(
                                    "assets/images/action/comment_icon.png"),
                              ),
                              const SizedBox(
                                width: 5,
                              ),
                              selectedCommentOption == null
                                  ? const SizedBox()
                                  : Flexible(
                                      child: Text(
                                        selectedCommentOption.displayText,
                                        maxLines: 1,
                                        softWrap: false,
                                        overflow: TextOverflow.fade,
                                        style: const TextStyle(
                                          color: Colors.grey,
                                          height: 1,
                                        ),
                                      ),
                                    ),
                              const SizedBox(
                                width: 10,
                              ),
                              const Icon(
                                Icons.keyboard_arrow_down_rounded,
                                color: Colors.grey,
                                size: 20,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ));
  }

  getMenu(
      {required BuildContext context,
      required CreateState state,
      required FocusNode textContentFocusNode,
      required ScrollController hashtagScrollController,
      required Function(CreateEvent) onEvent}) {
    return GestureDetector(
      onVerticalDragEnd: (details) {
        final primaryVelocity = details.primaryVelocity;
        if (primaryVelocity != null) {
          if (primaryVelocity > 200) {
            if (textContentFocusNode.hasFocus) {
              textContentFocusNode.unfocus();
            }
          }
        }
      },
      child: Container(
        color: Colors.transparent,
        child: Column(
          children: [
            Align(
              alignment: Alignment.topRight,
              child: commentOptions(context, state, onEvent: onEvent),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(
              height: 0.7,
              width: MediaQuery.of(context).size.width,
              color: Colors.grey[200],
            ),
            const SizedBox(
              height: 5,
            ),
            postAttachmentsActionWidget(context, state, onEvent: onEvent),
            const SizedBox(
              height: 5,
            ),
            tagGroupsWidget(
                context: context,
                state: state,
                onEvent: onEvent,
                textContentFocusNode: textContentFocusNode),
            const SizedBox(
              height: 10,
            ),
            hashtagsWidget(
                context: context,
                state: state,
                hashtagScrollController: hashtagScrollController),
            const SizedBox(
              height: 10,
            ),
          ],
        ),
      ),
    );
  }

  progressBar(CreateState state) {
    return const SizedBox(
      height: 2,
    );
  }

  getContent(
      {required BuildContext context,
      required CreateState state,
      required ScrollController hashtagScrollController,
      required TextEditingController textController,
      required FocusNode textContentFocusNode,
      required Function(CreateEvent) onEvent}) {
    if (state.initialDataLoaded) {
      return Stack(children: [
        Positioned.fill(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            topWidget(context),
            progressBar(state),
            Expanded(
              child: IgnorePointer(
                ignoring: state.submissionInProcess,
                child: Column(
                  children: [
                    Expanded(
                        child: GestureDetector(
                      onVerticalDragEnd: (details) {
                        if (textContentFocusNode.hasFocus) {
                          textContentFocusNode.unfocus();
                        }
                      },
                      onTap: () async {
                        bool isKeyboardVisible =
                            KeyboardVisibilityController().isVisible;
                        if (!isKeyboardVisible) {
                          textContentFocusNode.unfocus();
                          await Future.delayed(
                              const Duration(milliseconds: 10));
                          textContentFocusNode.requestFocus();
                        } else {
                          if (textContentFocusNode.hasFocus) {
                            textContentFocusNode.unfocus();
                          }
                        }
                      },
                      child: ListView(
                        physics: const BouncingScrollPhysics(),
                        shrinkWrap: true,
                        children: [
                          Container(
                            child: contentWidget(
                                context: context,
                                state: state,
                                onEvent: onEvent,
                                textContentFocusNode: textContentFocusNode,
                                textController: textController),
                          ),
                        ],
                      ),
                    )),
                    getMenu(
                        context: context,
                        state: state,
                        onEvent: onEvent,
                        textContentFocusNode: textContentFocusNode,
                        hashtagScrollController: hashtagScrollController),
                  ],
                ),
              ),
            )
          ],
        )),
        if (state.submissionInProcess)
          Positioned.fill(
              child: Container(
                  color: Colors.white.withOpacity(0.5),
                  child: const Center(
                      child:
                          CircularProgressIndicator.adaptive(strokeWidth: 2))))
      ]);
    } else {
      return Center(
        child: Widgets.renderCircularLoader(context),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<CreateBloc>(context);
    return Scaffold(
        backgroundColor: Colors.white,
        body: BlocConsumer<CreateBloc, CreateState>(
          bloc: bloc,
          builder: (context, state) {
            return WillPopScope(
                onWillPop: () async {
                  if (bloc.canPopDirectly()) {
                    return true;
                  }
                  return await _onWillPop(context);
                },
                child: SafeArea(
                    child: getContent(
                        context: context,
                        state: state,
                        hashtagScrollController: bloc.hashtagScrollController,
                        textContentFocusNode: bloc.textContentFocusNode,
                        textController: bloc.textController,
                        onEvent: bloc.add)));
          },
          listener: (context, state) {
            if (state.pendingFailureMessage.isNotEmpty) {
              Utils.showToast(state.pendingFailureMessage);
              BlocProvider.of<CreateBloc>(context).add(CreateErrorShown());
            }

            if (state.submitted) {
              final creationSuccessCallback = onSuccess;
              if (creationSuccessCallback != null) {
                creationSuccessCallback();
              }
              GetIt.I.get<AppEventBus>().fire(CreatePostSavedEvent());
            }
          },
        ));
  }
}

class BlinkWrapper extends StatefulWidget {
  const BlinkWrapper({super.key, required this.child, this.duration});
  final Widget child;
  final Duration? duration;

  @override
  State<BlinkWrapper> createState() => _BlinkWrapperState();
}

class _BlinkWrapperState extends State<BlinkWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    _animationController = AnimationController(
        vsync: this, duration: widget.duration ?? const Duration(seconds: 1));
    _animationController.repeat(reverse: true, min: 0.5, max: 1);
    super.initState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(opacity: _animationController, child: widget.child);
  }
}

typedef CreateBlocEventCallback = void Function(CreateEvent);

extension on NewPhoto {
  MediaCarouselItem toMediaCarouselItem() {
    return LocalMediaCarouselItem(data.path, width, height);
  }
}

extension PostAttachmentCreativeX on PostAttachmentCreative {
  MediaCarouselItem toMediaCarouselItem() {
    final aspectRatio = photo.aspectRatio;
    final int width;
    final int height;
    if (aspectRatio == 0) {
      width = 400;
      height = 400;
    } else {
      width = 400;
      height = (400 / aspectRatio).round();
    }
    return RemoteMediaCarouselItem(
        photo.placeholderUrl ?? photo.url, width, height);
  }
}
