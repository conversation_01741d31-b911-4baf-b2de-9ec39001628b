import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:praja/features/post/create/create_post_api.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/tag_alert_popup_data.dart';
import 'package:praja/models/tag_circle.dart';
import 'package:praja/models/tag_data.dart';
import 'package:praja/screens/create/tagPage/tagBloc/tag_bloc_events.dart';
import 'package:praja/screens/create/tagPage/tagBloc/tag_bloc_states.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:rxdart/rxdart.dart';

class TagBloc extends Bloc<TagEvents, TagState> with ChangeNotifier {
  TagBloc() : super(const TagState()) {
    on<TagBegin>(_mapTagBeginToState);
    on<TagFetchFollowingCircles>(_mapTagFetchFollowingCirclesToState);
    on<TagFetchSuggestedCircles>(_mapTagFetchSuggestedCirclesToState);
    on<TagAddItem>(_mapTagAddItemToState);
    on<TagRemoveItem>(_mapTagRemoveItemToState);
    on<TagWarningShown>(_mapTagWarningShownToState);
    on<TagSearchTextChange>(_mapTagSearchCircleToState);
    on<TagSearchResultChange>(_mapTagSearchResultsToState);
    on<TagSearchInProgress>(_mapTagSearchInProgressToState);
    on<TagSearchComplete>(_mapTagSearchCompleteToState);
    on<TagShowWarning>(_mapTagShowWarningToState);
    on<TagRulesAccepted>(_mapTagRulesAcceptedToState);
    on<TagWarningAccepted>(_mapTagWarningAcceptedToState);
  }

  final CreatePostApi _createPostApi = GetIt.I.get<CreatePostApi>();

  void _mapTagWarningAcceptedToState(
      TagWarningAccepted event, Emitter emit) async {
    final currentTagData = state.tagData;
    final circleToAddAfterWarning = state.circleToAddAfterWarning;
    if (currentTagData == null) return;

    await _createPostApi.tagWarningAccepted();
    TagAlertPopUpData tagAlertPopUpData =
        currentTagData.tagAlertPopUpData.copyWith(enabled: false);
    TagData tagData =
        currentTagData.copyWith(tagAlertPopUpData: tagAlertPopUpData);
    emit(state.copyWith(tagData: tagData, hasAcceptedWarning: true));
    if (circleToAddAfterWarning != null) {
      add(TagAddItem(
          tagCircle: circleToAddAfterWarning,
          tagSource: state.circleToAddAfterWarningSource));
    }
  }

  void _mapTagRulesAcceptedToState(TagRulesAccepted event, Emitter emit) async {
    try {
      emit(state.copyWith(hasAcceptedRules: true));
      await _createPostApi.tagRulesAccepted();
    } catch (e, stackTrace) {
      logNonFatalIfAppError("Error while accepting tag rules", e,
          stackTrace: stackTrace);
    }
  }

  void _mapTagShowWarningToState(TagShowWarning event, Emitter emit) async {
    emit(state.copyWith(
        showWarningPopup: true,
        circleToAddAfterWarning: event.tagCircle,
        circleToAddAfterWarningSource: event.source));
  }

  final StreamController<String> _searchTextController =
      StreamController<String>();
  StreamSubscription<List<TagCircle>>? _searchTextSubscription;
  void _mapTagSearchCircleToState(
      TagSearchTextChange event, Emitter emit) async {
    _searchTextController.add(event.searchText);
  }

  Future<void> _mapTagSearchResultsToState(
      TagSearchResultChange event, Emitter<TagState> emit) async {
    emit(state.copyWith(searchResults: event.searchResults));
  }

  Future<void> _mapTagSearchInProgressToState(
      TagSearchInProgress event, Emitter<TagState> emit) async {
    emit(state.copyWith(isSearchInProgress: true));
  }

  Future<void> _mapTagSearchCompleteToState(
      TagSearchComplete event, Emitter<TagState> emit) async {
    emit(state.copyWith(isSearchInProgress: false));
  }

  void _listenToSearchTextChanges() {
    _searchTextSubscription?.cancel();
    _searchTextSubscription = _searchTextController.stream
        .debounceTime(const Duration(milliseconds: 500))
        .distinct()
        .switchMap((searchText) async* {
      add(TagSearchInProgress());
      try {
        yield await _createPostApi.tagCircleSearch(searchText);
      } catch (e, stackTrace) {
        logNonFatalIfAppError("Error while searching tag circles", e,
            stackTrace: stackTrace);
      } finally {
        add(TagSearchComplete());
      }
    }).listen((tagCircles) {
      add(TagSearchResultChange(searchResults: tagCircles));
    });
  }

  void _mapTagWarningShownToState(TagWarningShown event, Emitter emit) async {
    emit(state.copyWith(
      showWarningPopup: false,
    ));
  }

  void _mapTagAddItemToState(TagAddItem event, Emitter emit) async {
    final currentTagData = state.tagData;
    if (currentTagData == null) return;

    if ((state.taggedCircles.length == currentTagData.maxCirclesCount - 1) &&
        currentTagData.tagAlertPopUpData.enabled) {
      add(TagShowWarning(tagCircle: event.tagCircle, source: event.tagSource));
    } else {
      AppAnalytics.logEvent(name: 'selected_group', parameters: {
        "group_name": event.tagCircle.name,
        "group_type": event.tagCircle.circleLevel.toString(),
        "source": event.tagSource
      });
      final List<TagCircle> taggedList = [];
      taggedList.addAll(state.taggedCircles);
      final tagCircleLevel = event.tagCircle.circleLevel;
      if (!taggedList.contains(event.tagCircle)) {
        taggedList.add(event.tagCircle);
        final circleLevelFilledPositions =
            state.circleLevelFilledPositions[tagCircleLevel.toString()] ?? 0;
        state.circleLevelFilledPositions[tagCircleLevel.toString()] =
            circleLevelFilledPositions + 1;
      }
      bool blocCircleTag = false;
      if (taggedList.length == currentTagData.maxCirclesCount) {
        blocCircleTag = true;
      }

      if (currentTagData.circleLevelsCountConfig.isEmpty) return;

      final levelConfigs = currentTagData.circleLevelsCountConfig
          .where((element) => element.level == event.tagCircle.circleLevel);
      if (levelConfigs.isEmpty) return;
      final levelConfig = levelConfigs.first;

      if (state.circleLevelFilledPositions[tagCircleLevel.toString()] ==
          levelConfig.maxCount) {
        state.eligibleCircleLevels.remove(tagCircleLevel);
        Utils.showSnackBarMessage(event.context, levelConfig.toastText);
      }
      final newState = state.copyWith(
          taggedCircles: taggedList,
          blocCircleTag: blocCircleTag,
          eligibleCircleLevels: state.eligibleCircleLevels,
          circleLevelFilledPositions: state.circleLevelFilledPositions);
      emit(newState);

      if (blocCircleTag) {
        await Future.delayed(const Duration(seconds: 1));
        Utils.showToast(currentTagData.maxCirclesReachedToast);
      }
    }
  }

  void _mapTagRemoveItemToState(TagRemoveItem event, Emitter emit) async {
    final currentTagData = state.tagData;
    if (currentTagData == null) return;

    final List<TagCircle> taggedList = [];
    taggedList.addAll(state.taggedCircles);
    taggedList.remove(event.tagCircle);

    bool blockCircleTag = state.blocCircleTag;
    AppAnalytics.logEvent(name: 'removed_group', parameters: {
      "group_name": event.tagCircle.name,
      "group_type": event.tagCircle.circleLevel.toString(),
      "source": event.removeSource
    });
    if (taggedList.length < currentTagData.maxCirclesCount) {
      blockCircleTag = false;
    }

    if (!state.eligibleCircleLevels.contains(event.tagCircle.circleLevel)) {
      state.eligibleCircleLevels.add(event.tagCircle.circleLevel);
    }
    final tagCircleLevel = event.tagCircle.circleLevel.toString();
    state.circleLevelFilledPositions[tagCircleLevel] =
        (state.circleLevelFilledPositions[tagCircleLevel] ?? 1) - 1;

    final newState = state.copyWith(
        taggedCircles: taggedList,
        blocCircleTag: blockCircleTag,
        eligibleCircleLevels: state.eligibleCircleLevels,
        circleLevelFilledPositions: state.circleLevelFilledPositions);
    emit(newState);
  }

  void _mapTagFetchSuggestedCirclesToState(
      TagFetchSuggestedCircles event, Emitter emit) async {
    try {
      final suggestedCircles = await _createPostApi.fetchSuggestedTagCircles(
          event.perPageCount, event.perPageCount * event.offset);

      if (suggestedCircles.length < event.perPageCount) {
        suggestedPagingController.appendLastPage(suggestedCircles);
      } else {
        suggestedPagingController.appendPage(
            suggestedCircles, event.offset + 1);
      }
      final newState = state.copyWith(suggestedCircles: suggestedCircles);
      emit(newState);
    } catch (e, stackTrace) {
      logNonFatalIfAppError("Error While Fetching Suggested Circles", e,
          stackTrace: stackTrace);
    }
  }

  void _mapTagFetchFollowingCirclesToState(
      TagFetchFollowingCircles event, Emitter emit) async {
    try {
      final followingCircles = await _createPostApi.fetchFollowingTagCircles(
          event.perPageCount, event.perPageCount * event.offset);

      if (followingCircles.length < event.perPageCount) {
        followingPagingController.appendLastPage(followingCircles);
      } else {
        followingPagingController.appendPage(
            followingCircles, event.offset + 1);
      }
      final newState = state.copyWith(followingCircles: followingCircles);
      emit(newState);
    } catch (e, stackTrace) {
      logNonFatalIfAppError("Error While Fetching Following Circles", e,
          stackTrace: stackTrace);
    }
  }

  final PagingController<int, TagCircle> followingPagingController =
      PagingController(firstPageKey: 0);

  final PagingController<int, TagCircle> suggestedPagingController =
      PagingController(firstPageKey: 0);
  void _mapTagBeginToState(TagBegin event, Emitter emit) async {
    followingPagingController.addPageRequestListener((pageKey) {
      add(TagFetchFollowingCircles(offset: pageKey, perPageCount: 10));
    });

    suggestedPagingController.addPageRequestListener((pageKey) {
      add(TagFetchSuggestedCircles(offset: pageKey, perPageCount: 10));
    });

    final List<String> eligibleCircleLevels = [];
    final Map<String, int> circleLevelFilledPositions = {};

    for (var element in event.tagData.circleLevelsCountConfig) {
      eligibleCircleLevels.add(element.level);
      circleLevelFilledPositions[element.level.toString()] = 0;
    }

    final state = this.state.copyWith(
        showRulesPopup: event.tagData.tagInfoPopUpData.enabled,
        tagData: event.tagData,
        status: TagStatus.loaded,
        eligibleCircleLevels: eligibleCircleLevels,
        taggedCircles: [],
        blocCircleTag: false,
        circleLevelFilledPositions: circleLevelFilledPositions);
    emit(state);

    emit(this.state.copyWith(showRulesPopup: false));

    for (var element in event.preselectedCircles) {
      add(TagAddItem(tagCircle: element, tagSource: "preselected"));
    }

    _listenToSearchTextChanges();
  }
}
