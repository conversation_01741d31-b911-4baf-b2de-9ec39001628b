import 'dart:async';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/core/di/injection.dart';
import 'package:praja/core/di/viewmodel_factory.dart';
import 'package:praja/core/ui/global_view_model_scope.dart';
import 'package:praja/cubits/HomeTutorialCubit.dart';
import 'package:praja/enums/app_environment.dart';
import 'package:praja/errors/ignored_errors.dart';
import 'package:praja/features/bg_downloader/bg_downloader.dart';
import 'package:praja/features/intl/intl.dart';
import 'package:praja/features/localization/localization_container.dart';
import 'package:praja/features/localization/string_store.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/screens/app.dart';
import 'package:praja/screens/posts/post_bloc/post_bloc.dart';
import 'package:praja/screens/users/bloc/profile_bloc.dart';
import 'package:praja/utils/logger.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  _configure();

  _initializeApp().then(_runApp);
}

void _configure() {
  // This captures errors reported by the Flutter framework(UI errors)
  FlutterError.onError = _onFlutterError;

  // This captures all the other dart errors
  PlatformDispatcher.instance.onError = _onError;

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  configureDependencies();
}

Future<StringStore> _initializeApp() async {
  await Future.wait([
    initLogger(),
    AppEnvironment.initialize(),
    AppAnalytics.initialize(),
    initIntl(),
    initializeBgDownloader(),
  ]);
  // initializing localization here to continue showing the splash screen while string store is loaded
  // The opinion is that splash screen ui is the right waiting screen for this loading
  return await initLocalization();
}

void _runApp(StringStore stringStore) {
  final viewModelFactory = AppViewModelFactory();
  runApp(
    MultiBlocProvider(
        providers: [
          BlocProvider(create: (_) => HomeTutorialCubit()),
          BlocProvider(create: (_) => ProfileBloc()),
          BlocProvider(create: (_) => PostBloc()),
        ],
        child: BgDownloaderHost(
            child: ViewModelFactoryProvider(
          viewModelFactory: viewModelFactory,
          child: GlobalViewModelScope(
              child: LocalizationContainer(
                  initialStringStore: stringStore, child: const PrajaApp())),
        ))),
  );
}

bool _onError(Object error, StackTrace stackTrace) {
  if (!kReleaseMode) {
    // Print the full stacktrace in debug mode.
    printDebug("Uncaught error",
        stackTrace: stackTrace,
        error: error,
        level: LogLevel.error,
        showToast: true);
  } else {
    FirebaseCrashlytics.instance.recordError(error, stackTrace, fatal: true);
  }
  return true;
}

void _onFlutterError(FlutterErrorDetails details) {
  if (kDebugMode) {
    // In development mode, simply print to console.
    FlutterError.dumpErrorToConsole(details, forceReport: true);
  } else {
    if (details.isIgnored) {
      return;
    }
    FirebaseCrashlytics.instance.recordFlutterError(details,
        fatal: !(details.silent || details.shouldBeMarkedAsSilent()));
  }
}
