import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/enums/circle_section.dart';
import 'package:praja/utils/widgets/feed_player/multi_manager/flick_multi_manager.dart';

@lazySingleton
class AppConstants {
  bool trendTutorial = false;
  bool trendFeedBack = false;
  int trendTutorialTriggerPosts = 3;
  int trendTutorialTimeInSeconds = 3;
  Set seenPostIds = {};
  String trendTutorialText =
      "ఈ పోస్ట్‌ని ట్రెండ్ చేయడానికి ఇక్కడ క్లిక్ చేయండి";
  String trendFeedbackText = "మీరు పోస్ట్‌ను ట్రెండ్ చేసారు.";
  bool videoAutoPlay = true;
  bool videoLoopAfterCompletion = true;

  String taggedPostsSheetText = "సంబంధిత సర్కిల్స్";
  FlickMultiManager flickMultiManager = FlickMultiManager();
  bool isVideoMuted = true;
  bool isVideoPosterMuted = true;

  CircleSection defaultCircleSection = CircleSection.myCircles;

  List<ConnectivityResult> appInternetConnectivityType = [];

  bool showPartySuggestions = false;
  bool skipPartyMemberDecision = true;
  bool exclusivePartyJoin = false;

  int? postIdFromDeepLink;
  bool showDeeplinkPostInFeed = true;

  static bool _premiumPosterCameraShown = false;

  /// To Show Camera Icon on Premium Poster to update user cutout photo
  /// With static variable we will show one time per app session
  bool get isPremiumPosterCameraShown => _premiumPosterCameraShown;

  onPremiumPosterCameraShown() {
    _premiumPosterCameraShown = true;
  }

  /// Used to show the trend tutorial after user has seen enough posts
  bool ifSeenCountExceeded() {
    return seenPostIds.length >= trendTutorialTriggerPosts;
  }

  /// Used to show the trend tutorial after user has seen enough posts
  void onPostSeen(int postId) {
    if (trendTutorial && !ifSeenCountExceeded()) {
      seenPostIds.add(postId);
    }
  }

  @override
  String toString() {
    return 'AppConstants{trendTutorial: $trendTutorial, trendFeedBack: $trendFeedBack, trendTutorialTriggerPosts: $trendTutorialTriggerPosts, trendTutorialTimeInSeconds: $trendTutorialTimeInSeconds, seenPostIds: $seenPostIds, trendTutorialText: $trendTutorialText, trendFeedbackText: $trendFeedbackText}';
  }
}
