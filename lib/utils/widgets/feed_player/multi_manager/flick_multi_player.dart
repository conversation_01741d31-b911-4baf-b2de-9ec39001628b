import 'package:cached_network_image/cached_network_image.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/constants/AppConstants.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/post.dart';
import 'package:praja/models/video.dart';
import 'package:praja/utils/widgets/feed_player/multi_manager/flick_multi_manager.dart';
import 'package:praja/utils/widgets/feed_player/video_page.dart';
import 'package:praja/utils/widgets/page_transition_widget.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

class FlickMultiPlayer extends StatefulWidget {
  const FlickMultiPlayer({
    super.key,
    required this.loop,
    required this.autoPlay,
    required this.video,
    required this.image,
    required this.flickMultiManager,
    required this.post,
    this.source,
    required this.buildContext,
    this.isFirstPostVideo = false,
  });

  final Video video;
  final bool isFirstPostVideo;
  final Post post;
  final String? source;
  final String image;
  final bool autoPlay;
  final bool loop;
  final FlickMultiManager flickMultiManager;
  final BuildContext buildContext;

  @override
  State<FlickMultiPlayer> createState() => _FlickMultiPlayerState();
}

class _FlickMultiPlayerState extends State<FlickMultiPlayer> {
  late FlickManager flickManager;
  static bool isFirstPostVideoPlaying = false;
  bool videoSeek = true;
  bool isOpeningVideoPreview = false;
  // variable to check first video of the first post is playing when two videos occurs on the screen on initial page
  @override
  void initState() {
    super.initState();
    if (mounted) {
      if (widget.autoPlay) {
        AppAnalytics.logEvent(name: "video_auto_play", parameters: {
          "source": widget.source,
          "post_id": widget.post.id,
          "url": widget.video.url,
        });
      }
    }
    flickManager = FlickManager(
      autoInitialize: widget.autoPlay,
      videoPlayerController: VideoPlayerController.networkUrl(
          Uri.parse(widget.video.url),
          videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true))
        ..setLooping(widget.loop),
      autoPlay: false,
    );
    flickManager.flickVideoManager?.videoPlayerController
        ?.addListener(_videoPlayerListener);
  }

  bool _hasLoggedCompletion = false;
  void _videoPlayerListener() {
    //if not initialized then return
    final initialized = flickManager
        .flickVideoManager?.videoPlayerController?.value.isInitialized;
    if (initialized == null || !initialized) {
      return;
    }
    final position = flickManager.flickVideoManager?.videoPlayerValue?.position;
    final duration = flickManager.flickVideoManager?.videoPlayerValue?.duration;

    final params = {
      "source": "post",
      "video_player_source": "feed",
      "post_id": widget.post.id,
      "video_id": widget.video.id,
      "video_aspect_ratio":
          flickManager.flickVideoManager?.videoPlayerValue?.aspectRatio,
      "post_author_id": widget.post.user.id,
      "post_author_has_badge": widget.post.user.badge != null,
      "post_author_badge_role": widget.post.user.badge?.description,
    };

    if (position != null &&
        duration != null &&
        position >= duration &&
        !_hasLoggedCompletion) {
      _hasLoggedCompletion = true;

      params["video_duration_secs"] = duration.inSeconds;

      AppAnalytics.logEvent(name: "watched_video", parameters: params);
    }
  }

  @override
  void dispose() {
    widget.flickMultiManager.remove(flickManager);
    flickManager.flickVideoManager?.videoPlayerController
        ?.removeListener(_videoPlayerListener);
    super.dispose();
  }

  previewVideoPageDisposed(bool isDisposed) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          videoSeek = isDisposed;
          isOpeningVideoPreview = false;
        });
      }
      //Newly added for session wise audio
      if (GetIt.I.get<AppConstants>().isVideoMuted) {
        flickManager.flickControlManager?.mute();
      } else {
        flickManager.flickControlManager?.unmute();
      }
      //Newly added for session wise audio
    });
  }

  autoPlayCallback(bool pauseVideo) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (pauseVideo && mounted) {
        setState(() {
          flickManager.flickControlManager?.pause();
        });
      }
    });
  }

  videoPauseIcon() {
    return Image.asset(
      "assets/images/action/play_icon.png",
      height: 43,
      width: 43,
    );
  }

  _openPostVideoPlayer() {
    setState(() {
      isOpeningVideoPreview = true;
    });
    AppAnalytics.logEvent(
        name: "clicked_play_video", parameters: {"post_id": widget.post.id});
    Navigator.push(
        widget.buildContext,
        PageSlideRight(
          page: VideoPreview(
            fm: flickManager,
            flickMultiManager: widget.flickMultiManager,
            video: widget.video,
            source: widget.source,
            post: widget.post,
            disposeCallback: previewVideoPageDisposed,
            autoPlayCallback: autoPlayCallback,
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: ObjectKey(flickManager),
      onVisibilityChanged: (visibilityInfo) {
        if (visibilityInfo.visibleFraction == 0 && !isOpeningVideoPreview) {
          if (mounted) {
            if (!widget.autoPlay) {
              flickManager.flickControlManager?.pause();
            }
          }
          widget.flickMultiManager
              .flickVideosDurationAndMutedMap[widget.video.url] = {
            "is_muted": flickManager.flickControlManager?.isMute,
            "duration":
                flickManager.flickVideoManager?.videoPlayerValue?.position,
            "video_initialized": false,
          };
        }
        if (visibilityInfo.visibleFraction > 0.99 && widget.autoPlay) {
          if (widget.flickMultiManager.flickVideosDurationAndMutedMap
                  .isNotEmpty &&
              widget.flickMultiManager.flickVideosDurationAndMutedMap
                  .containsKey(widget.video.url)) {
            widget.flickMultiManager.init(
                flickManager,
                widget.flickMultiManager.flickVideosDurationAndMutedMap[
                    widget.video.url]!['duration'],
                widget.flickMultiManager.flickVideosDurationAndMutedMap[
                    widget.video.url]!['is_muted'],
                videoSeek,
                widget.flickMultiManager.flickVideosDurationAndMutedMap[
                    widget.video.url]!['video_initialized'],
                widget.video.url);
            setState(() {
              widget.flickMultiManager.flickVideosDurationAndMutedMap[
                  widget.video.url]!['video_initialized'] = true;
            });
          } else {
            widget.flickMultiManager.init(
                flickManager,
                Duration.zero,
                true,
                videoSeek,
                widget.flickMultiManager.flickVideosDurationAndMutedMap
                            .isNotEmpty &&
                        widget.flickMultiManager.flickVideosDurationAndMutedMap
                            .containsKey(widget.video.url)
                    ? !widget.flickMultiManager.flickVideosDurationAndMutedMap[
                        widget.video.url]!['video_initialized']
                    : true,
                widget.video.url);
          }
          widget.flickMultiManager.flickManagersVisibility[flickManager] =
              visibilityInfo.visibleFraction;
          if (!isFirstPostVideoPlaying) {
            flickManager.flickControlManager?.play();
            widget.flickMultiManager.play(flickManager);
          }
          if (widget.isFirstPostVideo) {
            isFirstPostVideoPlaying = true;
          }
        } else if (widget.autoPlay) {
          isFirstPostVideoPlaying = false;
          if (!isOpeningVideoPreview) {
            widget.flickMultiManager.pause(flickManager);
          }
          widget.flickMultiManager.removeWithoutDispose(flickManager);
        } else {
          if (!widget.autoPlay) {
            if (mounted) {
              flickManager.flickControlManager?.pause();
            }
          }
          if (mounted) {
            flickManager.flickControlManager?.pause();
          }
          if (!widget.autoPlay && isOpeningVideoPreview) {
            flickManager.flickControlManager?.play();
          }
        }
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: FlickVideoPlayer(
          flickManager: flickManager,
          flickVideoWithControls: FlickVideoWithControls(
            playerErrorFallback: VideoPlayerError(
              videoId: widget.video.id.toString(),
              source: widget.source,
              postId: widget.post.id.toString(),
              flickManager: flickManager,
              playerType: PlayerType.feedPlayer,
            ),
            playerLoadingFallback: Positioned.fill(
              child: Stack(
                alignment: Alignment.center,
                children: <Widget>[
                  CachedNetworkImage(
                      imageUrl: widget.image,
                      imageBuilder: (context, imageProvider) {
                        return Container(
                            width: double.infinity,
                            height: double.infinity,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: imageProvider,
                                fit: BoxFit.cover,
                              ),
                            ));
                      },
                      progressIndicatorBuilder:
                          (context, url, downloadProgress) => Center(
                              child: CircularProgressIndicator(
                                  value: downloadProgress.progress,
                                  color: Colors.black)),
                      errorWidget: (context, url, error) {
                        AppAnalytics.logEvent(
                            name: "post_video_preview_image_load_error",
                            parameters: {
                              "url": url,
                              "post": widget.post.id,
                              "source": widget.source,
                              "error": error.toString(),
                            });
                        return const Icon(Icons.error);
                      }),
                  GestureDetector(
                    onTap: () {
                      _openPostVideoPlayer();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.black.withOpacity(0.5),
                      ),
                      child: const Padding(
                          padding: EdgeInsets.all(4),
                          child: Icon(
                            Icons.play_arrow_rounded,
                            color: Colors.white,
                            size: 32,
                          )),
                    ),
                  )
                ],
              ),
            ),
            controls: GestureDetector(
              onTap: () {
                _openPostVideoPlayer();
              },
              child: Stack(
                children: [
                  Container(
                    color: Colors.transparent,
                    alignment: Alignment.bottomCenter,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      children: <Widget>[
                        FlickAutoHideChild(
                          showIfVideoNotInitialized: false,
                          autoHide: false,
                          child: Container(
                            margin: const EdgeInsets.only(bottom: 6, left: 6),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 4, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const FlickLeftDuration(),
                          ),
                        ),
                        FlickAutoHideChild(
                          autoHide: false,
                          showIfVideoNotInitialized: false,
                          child: Container(
                            color: Colors.transparent,
                            padding: const EdgeInsets.all(6),
                            alignment: Alignment.bottomRight,
                            child: CircleAvatar(
                              radius: 13,
                              backgroundColor: Colors.black54,
                              child: FlickSoundToggle(
                                size: 20,
                                color: Colors.white,
                                toggleMute: () {
                                  if (mounted) {
                                    setState(() {
                                      GetIt.I.get<AppConstants>().isVideoMuted =
                                          !GetIt.I
                                              .get<AppConstants>()
                                              .isVideoMuted;
                                      if (GetIt.I
                                          .get<AppConstants>()
                                          .isVideoMuted) {
                                        flickManager.flickControlManager
                                            ?.mute();
                                      } else {
                                        flickManager.flickControlManager
                                            ?.unmute();
                                      }
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Center(
                      child: FlickAutoHideChild(
                          showIfVideoNotInitialized: false,
                          autoHide: false,
                          child: FlickPlayToggle(
                            replayChild: const CircleAvatar(
                              radius: 20,
                              backgroundColor: Colors.black54,
                              child: Icon(
                                Icons.replay,
                                color: Colors.white,
                              ),
                            ),
                            pauseChild: const SizedBox(),
                            playChild: GestureDetector(
                                onTap: () {
                                  _openPostVideoPlayer();
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.black.withOpacity(0.5),
                                  ),
                                  child: const Padding(
                                      padding: EdgeInsets.all(4),
                                      child: Icon(
                                        Icons.play_arrow_rounded,
                                        color: Colors.white,
                                        size: 32,
                                      )),
                                )),
                          ))),
                ],
              ),
            ),
          ),
          flickVideoWithControlsFullscreen: FlickVideoWithControls(
            playerLoadingFallback: Center(
                child: Image.network(
              widget.image,
              fit: BoxFit.fitWidth,
            )),
            controls: const FlickLandscapeControls(),
            iconThemeData: const IconThemeData(
              size: 40,
              color: Colors.white,
            ),
            textStyle: const TextStyle(fontSize: 16, color: Colors.white),
          ),
        ),
      ),
    );
  }
}
