import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/common/widgets/adaptive_back_arrow_icon.dart';
import 'package:praja/constants/AppConstants.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/post.dart';
import 'package:praja/models/video.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/widgets/feed_player/multi_manager/flick_multi_manager.dart';
import 'package:praja/errors/ignored_errors.dart';

class VideoPreview extends StatefulWidget {
  const VideoPreview(
      {super.key,
      required this.fm,
      required this.flickMultiManager,
      required this.video,
      this.disposeCallback,
      this.autoPlayCallback,
      required this.post,
      this.source});
  final FlickManager fm;
  final Post post;
  final String? source;
  final FlickMultiManager flickMultiManager;
  final Video video;
  final Function? disposeCallback;
  final Function? autoPlayCallback;

  @override
  State<VideoPreview> createState() => _VideoPreviewState();
}

class _VideoPreviewState extends State<VideoPreview> {
  bool bufferTracked = false;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.fm.flickControlManager?.unmute();
      widget.fm.flickControlManager?.play();
    });
    if (!(widget.fm.flickVideoManager?.isVideoInitialized ?? false)) {
      widget.fm.flickVideoManager?.videoPlayerController
          ?.initialize()
          .catchError((e, stackTrace) {
        if (e is! Object) {
          return;
        }

        if (!e.isIgnored) {
          logNonFatal("Error during video player controller initialization", e,
              stackTrace: stackTrace);
        }
      });
    }
    widget.fm.flickVideoManager?.addListener(_videoListener);
    widget.fm.flickVideoManager?.videoPlayerController
        ?.addListener(_videoPlayerListener);
  }

  @override
  didChangeDependencies() {
    super.didChangeDependencies();
  }

  /// ChangeNotifier listener for FlickVideoManager x VideoPlayerController
  _videoListener() {
    if (widget.fm.flickVideoManager?.videoPlayerValue?.isBuffering ?? false) {
      if (!bufferTracked) {
        bufferTracked = true;
        AppAnalytics.logEvent(name: "video_buffer", parameters: {
          "source": widget.source.toString(),
          "post_id": widget.post.id.toString(),
          "video_id": widget.video.id.toString(),
          "player": PlayerType.fullScreenPlayer.name
        });
      }
    } else {
      bufferTracked = false;
    }
  }

  bool _hasLoggedCompletion = false; // flag to track completion

  void _videoPlayerListener() {
    final initialized =
        widget.fm.flickVideoManager?.videoPlayerController?.value.isInitialized;
    if (initialized == null || !initialized) {
      return;
    }
    final position = widget.fm.flickVideoManager?.videoPlayerValue?.position;
    final duration = widget.fm.flickVideoManager?.videoPlayerValue?.duration;

    final params = {
      "source": "post",
      "video_player_source": "full_screen_player",
      "post_id": widget.post.id,
      "video_id": widget.video.id,
      "video_aspect_ratio":
          widget.fm.flickVideoManager?.videoPlayerValue?.aspectRatio,
      "post_author_id": widget.post.user.id,
      "post_author_has_badge": widget.post.user.badge != null,
      "post_author_badge_role": widget.post.user.badge?.description,
    };

    if (position != null &&
        duration != null &&
        position >= duration &&
        !_hasLoggedCompletion) {
      _hasLoggedCompletion = true;

      params["video_duration_secs"] = duration.inSeconds;

      AppAnalytics.logEvent(name: "watched_video", parameters: params);
    }
  }

  addBufferListener() {}

  @override
  void dispose() {
    if (widget.disposeCallback != null) {
      widget.disposeCallback!(false);
    }
    if (!GetIt.I.get<AppConstants>().videoAutoPlay &&
        widget.autoPlayCallback != null) {
      widget.autoPlayCallback!(true);
    }
    widget.fm.flickVideoManager?.removeListener(_videoListener);
    widget.fm.flickVideoManager?.videoPlayerController
        ?.removeListener(_videoPlayerListener);

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: SystemUiOverlay.values);
    super.dispose();
  }

  onBackPressed() {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: SystemUiOverlay.values);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (bool didPop) {
        if (didPop) {
          onBackPressed();
        }
      },
      child: Theme(
        data: ThemeData.dark(useMaterial3: false).copyWith(
          colorScheme: ColorScheme.fromSwatch(
            brightness: Brightness.dark,
          ),
        ),
        child: AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle.light.copyWith(
              systemNavigationBarColor: Colors.black,
              systemNavigationBarIconBrightness: Brightness.light),
          child: Scaffold(
            body: Stack(
              children: [
                Positioned.fill(
                  child: Container(
                      color: Colors.white,
                      child: FlickVideoPlayer(
                          flickVideoWithControls: FlickVideoWithControls(
                            videoFit: BoxFit.contain,
                            playerErrorFallback: VideoPlayerError(
                              videoId: widget.video.id.toString(),
                              source: widget.source,
                              postId: widget.post.id.toString(),
                              flickManager: widget.fm,
                              playerType: PlayerType.fullScreenPlayer,
                            ),
                            controls: FlickCustomPortraitControls(
                              onBackPressCallback: onBackPressed,
                              fm: widget.fm,
                              post: widget.post,
                              video: widget.video,
                              source: widget.source,
                            ),
                          ),
                          preferredDeviceOrientationFullscreen: const [
                            DeviceOrientation.landscapeLeft,
                            DeviceOrientation.landscapeRight
                          ],
                          // preferredDeviceOrientation: DeviceOrientation.values.toList(),
                          flickManager: widget.fm)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class FlickCustomPortraitControls extends StatefulWidget {
  final FlickManager fm;
  final Post post;
  final Video video;
  final String? source;
  final Function? onBackPressCallback;

  const FlickCustomPortraitControls({
    super.key,
    this.iconSize = 20,
    this.fontSize = 12,
    this.progressBarSettings,
    required this.fm,
    this.source,
    required this.video,
    required this.post,
    this.onBackPressCallback,
  });

  /// Icon size.
  ///
  /// This size is used for all the player icons.
  final double iconSize;

  /// Font size.
  ///
  /// This size is used for all the text.
  final double fontSize;

  /// [FlickProgressBarSettings] settings.
  final FlickProgressBarSettings? progressBarSettings;

  @override
  State<FlickCustomPortraitControls> createState() =>
      _FlickCustomPortraitControlsState();
}

class _FlickCustomPortraitControlsState
    extends State<FlickCustomPortraitControls> {
  bool _isFullScreen = false;

  logVideoFullScreenClick(VideoType videoType) {
    AppAnalytics.logEvent(name: "video_full_screen_tap", parameters: {
      "source": widget.source.toString(),
      "post_id": widget.post.id.toString(),
      "video_id": widget.video.id.toString(),
      "video_type": videoType.name.toString()
    });
  }

  toggleFullScreen(FlickManager flickManager) {
    double aspectRatio =
        flickManager.flickVideoManager?.videoPlayerValue?.aspectRatio ?? 1.0;
    setState(() {
      _isFullScreen = !_isFullScreen;
    });
    if (_isFullScreen) {
      logVideoFullScreenClick(VideoType.landscape);
      if (aspectRatio > 1.0) {
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight
        ]);
      } else {
        SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      }
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky,
          overlays: [SystemUiOverlay.top]);
    } else {
      logVideoFullScreenClick(VideoType.portrait);
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
          overlays: SystemUiOverlay.values);
    }
  }

  getFullScreenIcon() {
    return _isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Positioned.fill(
          child: FlickShowControlsAction(
            child: FlickSeekVideoAction(
              child: Center(
                child: FlickVideoBuffer(
                  child: FlickAutoHideChild(
                    showIfVideoNotInitialized: false,
                    child: FlickPlayToggle(
                      size: 30,
                      color: Colors.black,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white70,
                        borderRadius: BorderRadius.circular(40),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        Positioned.fill(
          child: FlickAutoHideChild(
            // autoHide: false,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                SafeArea(
                  child: InkWell(
                    onTap: () {
                      if (widget.onBackPressCallback != null) {
                        widget.onBackPressCallback!();
                      }
                      Navigator.of(context).pop();
                    },
                    child: Align(
                      alignment: Alignment.topLeft,
                      child: Container(
                        margin: const EdgeInsets.only(
                          left: 10,
                          top: 20,
                        ),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.black.withOpacity(0.5),
                        ),
                        child: const AdaptiveBackArrowIcon(),
                      ),
                    ),
                  ),
                ),
                Expanded(
                    child: IgnorePointer(
                  child: Container(),
                )),
                Container(
                    color: Colors.black54,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(children: [
                        FlickVideoProgressBar(
                          flickProgressBarSettings: widget.progressBarSettings,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            FlickPlayToggle(
                              togglePlay: () {
                                widget.fm.flickControlManager?.togglePlay();
                              },
                              size: widget.iconSize,
                            ),
                            Expanded(
                              child: Container(),
                            ),
                            FlickSoundToggle(
                              size: widget.iconSize,
                            ),
                            SizedBox(
                              width: widget.iconSize / 2,
                            ),
                            IconButton(
                              icon: Icon(getFullScreenIcon(),
                                  color: Colors.white, size: widget.iconSize),
                              onPressed: () {
                                toggleFullScreen(widget.fm);
                              },
                            ),
                            SizedBox(
                              width: widget.iconSize / 2,
                            ),
                            Row(
                              children: <Widget>[
                                FlickCurrentPosition(
                                  fontSize: widget.fontSize,
                                ),
                                FlickAutoHideChild(
                                  autoHide: false,
                                  child: Text(
                                    ' / ',
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: widget.fontSize),
                                  ),
                                ),
                                FlickTotalDuration(
                                  fontSize: widget.fontSize,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ]),
                    )),
                Container(
                  color: Colors.black54,
                  height: _isFullScreen ? 0 : 100,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class VideoPlayerError extends StatefulWidget {
  final String postId;
  final String videoId;
  final String? source;
  final PlayerType playerType;
  final FlickManager flickManager;
  const VideoPlayerError(
      {super.key,
      required this.postId,
      this.source,
      required this.flickManager,
      required this.videoId,
      required this.playerType});

  @override
  State<VideoPlayerError> createState() => _VideoPlayerErrorState();
}

class _VideoPlayerErrorState extends State<VideoPlayerError> {
  @override
  void initState() {
    super.initState();
    final errorMessage = _getErrorMessage();
    AppAnalytics.logEvent(name: "video_player_error", parameters: {
      "source": widget.source.toString(),
      "post_id": widget.postId.toString(),
      "video_id": widget.videoId,
      "player": widget.playerType.name,
      "error": errorMessage,
    });
  }

  String _getErrorMessage() {
    final controller =
        widget.flickManager.flickVideoManager?.videoPlayerController;

    if (controller == null) {
      return "No controller found";
    }

    if (controller.value.hasError) {
      return controller.value.errorDescription ?? "Null error description";
    }

    return "Unknown error";
  }

  @override
  Widget build(BuildContext context) {
    return const Center(
        child: Icon(
      Icons.error,
      color: Colors.white,
    ));
  }
}

enum PlayerType { feedPlayer, fullScreenPlayer }

enum VideoType { landscape, portrait }
