import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:path/path.dart' as p;
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:praja/exceptions/api_exception.dart';
import 'package:praja/features/payments/service/juspay/juspay_exception.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/utils/utils.dart';

const String logTag = "praja.default";

enum LogLevel { info, debug, error }

extension _LogLevelValue on LogLevel {
  /// Source of the values - [logging/level.dart](https://github.com/dart-lang/logging/blob/master/lib/src/level.dart)
  int get value {
    switch (this) {
      case LogLevel.debug:
        return 400;
      case LogLevel.info:
        return 800;
      case LogLevel.error:
        return 1200;
    }
  }
}

class CrashlyticsLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    if (event.level == Level.warning) {
      // uncaught errors and non fatals are being logged to Firebase Crashlytics separately with stackTrace
      FirebaseCrashlytics.instance.log(event.lines.join('\n'));
    }
  }
}

bool _loggerInitialized = false;
late Logger _logger;

Future<void> initLogger() async {
  if (_loggerInitialized) {
    return;
  }

  final debugLogsFile = File(p.join(
      (await getApplicationDocumentsDirectory()).path, 'debug-logs.txt'));
  var output = kDebugMode
      ? MultiOutput([ConsoleOutput(), FileOutput(file: debugLogsFile)])
      : CrashlyticsLogOutput();

  _logger = Logger(
    printer: SimplePrinter(),
    filter: ProductionFilter(),
    level: kDebugMode ? Level.debug : Level.warning,
    output: output,
  );
  _loggerInitialized = true;

  printDebug("Log file path: ${debugLogsFile.path}");
}

void logNonFatal(String message, Object? error, {StackTrace? stackTrace}) {
  printDebug(message, level: LogLevel.error, error: error);
  StackTrace? st = stackTrace;
  if (error is Error && st == null) {
    st = error.stackTrace;
  }
  FirebaseCrashlytics.instance.recordError(error, st, information: [message]);
}

void logNonFatalIfAppError(String message, Object? error,
    {StackTrace? stackTrace}) {
  if ((error is ApiException && !error.isAppError()) ||
      error is JuspayException) {
    printDebug(message,
        error: error, level: LogLevel.error, stackTrace: stackTrace);
  } else {
    logNonFatal(message, error, stackTrace: stackTrace);
  }
}

extension LogNonFatalsX<T> on Stream<T> {
  Stream<T> logNonFatalsIfAppError(String message) {
    return handleError((error, stackTrace) {
      logNonFatalIfAppError(message, error, stackTrace: stackTrace);
    });
  }
}

///record - if true, records true sends event to mixpanel
void logError(String message, Object? error,
    {bool record = true, String? errorcode, StackTrace? stackTrace}) {
  printDebug(message,
      level: LogLevel.error, stackTrace: stackTrace, error: error);

  if (record) {
    if (errorcode == null) {
      printDebug("Error code is null, add error code to track errors",
          showToast: true);
    }

    AppAnalytics.logEvent(
      name: errorcode ?? message,
      parameters: {"source": "public", "message": message},
    );
  }
}

void logDebug(String message) {
  printDebug(message, level: LogLevel.debug);
}

void logInfo(String message, {bool showToast = false}) {
  printDebug(message, level: LogLevel.info, showToast: showToast);
}

void printDebug(String message,
    {String tag = logTag,
    LogLevel level = LogLevel.debug,
    StackTrace? stackTrace,
    Object? error,
    bool showToast = false}) {
  if (kReleaseMode) return;
  if (showToast) Utils.showToast(message, toastLength: Toast.LENGTH_LONG);

  switch (level) {
    case LogLevel.info:
      developer.log(
        message,
        level: level.value,
        name: tag,
      );
      _logger.i(message);
      break;
    case LogLevel.debug:
      developer.log(
        message,
        name: tag,
        level: level.value,
      );
      _logger.d(message);
      break;
    case LogLevel.error:
      StackTrace fallbackStackTrace = StackTrace.current;
      if (error != null && error is Error) {
        fallbackStackTrace = error.stackTrace ?? fallbackStackTrace;
      }
      developer.log(
        message,
        name: tag,
        zone: Zone.current.errorZone,
        level: level.value,
        stackTrace: stackTrace ?? fallbackStackTrace,
        error: error,
      );
      _logger.e(message,
          error: error, stackTrace: stackTrace ?? fallbackStackTrace);
      break;
  }
}

class TimeLogger {
  late int _startTime;
  late int _intervalTime;
  final String tag;

  TimeLogger(this.tag);

  void startTimer() {
    _startTime = DateTime.now().millisecondsSinceEpoch;
    _intervalTime = _startTime;
    printDebug("Started timer", tag: tag);
  }

  int record({String message = "Check for lapsed time"}) {
    var currentTime = DateTime.now().millisecondsSinceEpoch;
    int timeLapsed = currentTime - _startTime;
    printDebug(
        "$message, Time from last record ${currentTime - _intervalTime}."
        " Total Time lapsed - $timeLapsed milliseconds",
        tag: tag);
    _intervalTime = currentTime;
    return timeLapsed;
  }

  int stopTimer({String message = "Stopped timer"}) {
    var currentTime = DateTime.now().millisecondsSinceEpoch;
    int timeLapsed = currentTime - _startTime;
    printDebug(
        "$message. Time from last record ${currentTime - _intervalTime}. "
        "Total Time lapsed - $timeLapsed milliseconds",
        tag: tag);
    _startTime = 0;
    _intervalTime = 0;
    return timeLapsed;
  }
}
