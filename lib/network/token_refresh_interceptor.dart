import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/network/network_constants.dart';
import 'package:praja/services/home.dart';
import 'package:praja/services/user_store.dart';

@injectable
class TokenRefreshInterceptor extends InterceptorsWrapper {
  final Dio _dio;
  final UserStore _userStore;
  TokenRefreshInterceptor(@Named(base) this._dio, this._userStore);

  @override
  Future onError(DioError err, handler) async {
    final response = err.response;
    if (response != null && response.statusCode == 401) {
      try {
        await Home.refreshAccessToken();
      } catch (e) {
        // ignore
        return handler.reject(err);
      }

      AppAnalytics.logEvent(
        name: "user_token_refreshed",
        parameters: {
          "reason": "401 UnAuthorized",
          "endpoint": err.requestOptions.path
        },
      );

      final oldRequest = err.requestOptions;
      final newHeaders = oldRequest.headers
        ..update('Authorization', (_) {
          return 'Bearer ${_userStore.getUserToken()}';
        }, ifAbsent: () {
          return 'Bearer ${_userStore.getUserToken()}';
        });
      final newRequest = oldRequest.copyWith(headers: newHeaders);
      try {
        final newResponse = await _dio.fetch(newRequest);
        return handler.resolve(newResponse);
      } on DioError catch (e) {
        return handler.reject(e);
      }
    }
    return handler.next(err);
  }
}
