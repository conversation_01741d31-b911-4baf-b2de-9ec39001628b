import 'package:flutter/material.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/mixins/analytics.dart';

class RetryUI extends StatelessWidget {
  final String? displayMessage;
  final String? buttonText;
  final Function() onRetry;
  final String source;
  final TextStyle textStyle;

  const RetryUI({
    super.key,
    required this.onRetry,
    required this.source,
    this.displayMessage,
    this.buttonText,
    this.textStyle = const TextStyle(
      fontSize: 16,
      color: Colors.black,
      fontWeight: FontWeight.bold,
    ),
  });

  @override
  Widget build(BuildContext context) {
    final displayMessage = this.displayMessage ??
        context.getString(StringKey.somethingWentWrongText);
    final buttonText =
        this.buttonText ?? context.getString(StringKey.retryLabel);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(displayMessage, style: textStyle),
          const SizedBox(height: 20),
          ElevatedButton(
              style: ButtonStyle(
                  fixedSize: MaterialStateProperty.all(
                      Size(MediaQuery.of(context).size.width * 0.7, 50))),
              onPressed: () {
                AppAnalytics.logEvent(name: "retry_button_click", parameters: {
                  "source": source,
                });
                onRetry();
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.refresh),
                  const SizedBox(width: 10),
                  Text(
                    buttonText,
                    style: const TextStyle(fontSize: 17),
                  ),
                ],
              ))
        ],
      ),
    );
  }
}
