import 'package:praja/models/feed_item_abstract_class.dart';
import 'package:praja/test/mocks/premium_experience_screen_mocks.dart';

final List<Map<String, dynamic>> _feedItems = [
  offerFeedItemJson,
  faqsFeedItemMock,
  upcomingEventsMock,
  subscriptionToastMock,
  relationManagerMock,
  usageCountsMock,
  premiumMembersMock,
  profileViewsMock,
  myPostersStylesMock,
  eventsMediaCarouselMock,
  paymentFeedItemMock,
];

Map<String, dynamic> offerFeedItemJson = {
  'text': 'మకర సంక్రాంతి ప్రత్యేక బహుమతి!',
  'sub_text': 'ప్రజా ప్రీమియం సభ్యుల కోసం ప్రత్యేకంగా',
  'button_text': 'తెరవండి',
  'deeplink': '/pay-wall?source=test',
  'feed_type': 'offer',
  'feed_item_id': 'test1234',
};

//Parse all feed item json
List<FeedItem> getFeedItemsMocks() {
  return _feedItems.map((e) => FeedItem.fromJson(e)).toList();
}
