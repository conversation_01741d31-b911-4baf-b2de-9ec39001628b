import 'package:json_annotation/json_annotation.dart';
import 'package:praja_posters/praja_posters.dart';

part 'badge.g.dart';

@JsonSerializable()
class Badge {
  final int id;
  final bool active;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'icon_url')
  final String? badgeIconUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'badge_text', defaultValue: '')
  final String badgeRole;

  @<PERSON>son<PERSON>ey(name: 'badge_banner', defaultValue: BadgeBanner.none)
  final BadgeBanner badgeBanner;

  @<PERSON>sonKey(name: 'badge_ring', defaultValue: BadgeRing.noRing)
  final BadgeRing badgeRing;

  @<PERSON><PERSON><PERSON><PERSON>(
      name: 'verification_status', defaultValue: VerificationStatus.unverified)
  final VerificationStatus verificationStatus;

  @JsonKey(name: 'description', defaultValue: '')
  final String description;

  @Json<PERSON>ey(name: 'verification_text', defaultValue: '')
  final String verificationText;

  Badge({
    required this.id,
    required this.active,
    required this.badgeIconUrl,
    required this.badgeRole,
    required this.badgeRing,
    required this.badgeBanner,
    required this.verificationStatus,
    required this.description,
    required this.verificationText,
  });

  factory Badge.fromJson(Map<String, dynamic> json) => _$BadgeFromJson(json);

  Map<String, dynamic> toJson() => _$BadgeToJson(this);

  @override
  String toString() {
    return "Badge{id: $id, active: $active, badgeIconUrl: $badgeIconUrl, badgeRole: $badgeRole, badgeBanner: $badgeBanner, badgeRing: $badgeRing, description: $description, verificationText: $verificationText}";
  }
}

enum BadgeBanner {
  @JsonValue("GOLD")
  gold,
  @JsonValue("SILVER")
  silver,
  @JsonValue("WHITE")
  white,
  @JsonValue("NONE")
  none
}

enum BadgeRing {
  @JsonValue("GOLD_RING")
  goldRing,
  @JsonValue("SILVER_RING")
  silverRing,
  @JsonValue("NO_RING")
  noRing
}

enum VerificationStatus {
  @JsonValue('unverified')
  unverified,
  @JsonValue('verified')
  verified
}

extension BadgeX on Badge {
  PosterBadge toPosterBadge() {
    return PosterBadge(
      id: id,
      active: active,
      badgeIconUrl: badgeIconUrl,
      badgeRole: badgeRole,
      badgeBanner: badgeBanner.toPosterBadgeBanner(),
      badgeRing: badgeRing.toPosterBadgeRing(),
      description: description,
    );
  }
}

extension BadgeBannerX on BadgeBanner {
  PosterBadgeBanner toPosterBadgeBanner() {
    switch (this) {
      case BadgeBanner.gold:
        return PosterBadgeBanner.gold;
      case BadgeBanner.silver:
        return PosterBadgeBanner.silver;
      case BadgeBanner.white:
        return PosterBadgeBanner.white;
      case BadgeBanner.none:
        return PosterBadgeBanner.none;
    }
  }
}

extension BadgeRingX on BadgeRing {
  PosterBadgeRing toPosterBadgeRing() {
    switch (this) {
      case BadgeRing.goldRing:
        return PosterBadgeRing.goldRing;
      case BadgeRing.silverRing:
        return PosterBadgeRing.silverRing;
      case BadgeRing.noRing:
        return PosterBadgeRing.noRing;
    }
  }
}
