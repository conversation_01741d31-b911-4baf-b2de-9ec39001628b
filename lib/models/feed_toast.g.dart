// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'feed_toast.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FeedToast _$FeedToastFromJson(Map<String, dynamic> json) => FeedToast(
      feedToastType: $enumDecodeNullable(
              _$FeedToastTypeEnumEnumMap, json['feed_toast_type']) ??
          FeedToastTypeEnum.none,
      imageUrl: json['image_url'] as String?,
      imageHeight: (json['image_height'] as num?)?.toDouble() ?? 180,
      imageWidth: (json['image_width'] as num?)?.toDouble() ?? 360,
      header: json['header'] as String? ?? '',
      message: json['message'] as String? ?? '',
      toastBgColor: (json['toast_color'] as num?)?.toInt() ?? 4293586930,
      iconColor: (json['icon_color'] as num?)?.toInt() ?? 4281487528,
      headerFontColor:
          (json['header_font_color'] as num?)?.toInt() ?? 4279762944,
      messageFontColor:
          (json['message_font_color'] as num?)?.toInt() ?? 4279762944,
      headerFontSize: (json['header_font_size'] as num?)?.toDouble() ?? 16.0,
      messageFontSize: (json['message_font_size'] as num?)?.toDouble() ?? 14.0,
      isRemovable: json['is_removable'] as bool? ?? false,
      ctaText: json['cta_text'] as String? ?? '',
      ctaUrl: json['cta_url'] as String?,
      ctaTextColor: (json['cta_text_color'] as num?)?.toInt() ?? 4279762944,
      isShareable: json['is_shareable'] as bool? ?? false,
      shareText: json['share_text'] as String? ?? '',
      shareImage: json['share_image'] as bool? ?? true,
      liveConfig: json['live_config'] == null
          ? null
          : LiveConfig.fromJson(json['live_config'] as Map<String, dynamic>),
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$FeedToastToJson(FeedToast instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['feed_toast_type'] = _$FeedToastTypeEnumEnumMap[instance.feedToastType]!;
  writeNotNull('image_url', instance.imageUrl);
  val['image_height'] = instance.imageHeight;
  val['image_width'] = instance.imageWidth;
  val['header'] = instance.header;
  val['message'] = instance.message;
  val['toast_color'] = instance.toastBgColor;
  val['icon_color'] = instance.iconColor;
  val['header_font_color'] = instance.headerFontColor;
  val['message_font_color'] = instance.messageFontColor;
  val['header_font_size'] = instance.headerFontSize;
  val['message_font_size'] = instance.messageFontSize;
  val['is_removable'] = instance.isRemovable;
  val['cta_text'] = instance.ctaText;
  writeNotNull('cta_url', instance.ctaUrl);
  val['cta_text_color'] = instance.ctaTextColor;
  val['is_shareable'] = instance.isShareable;
  val['share_text'] = instance.shareText;
  val['share_image'] = instance.shareImage;
  writeNotNull('live_config', instance.liveConfig?.toJson());
  return val;
}

const _$FeedToastTypeEnumEnumMap = {
  FeedToastTypeEnum.info: 'info',
  FeedToastTypeEnum.tip: 'tip',
  FeedToastTypeEnum.warning: 'warning',
  FeedToastTypeEnum.error: 'error',
  FeedToastTypeEnum.announcement: 'announcement',
  FeedToastTypeEnum.none: 'none',
};
