# Page View Tracking

All pages that extend `BasePage` from `/core/ui/page.dart`, automatically inherit the ability to track their page views, which tracks whether the current page is the top most page in the stack and if the app is in foreground to report `viewed_<page_name>_page` event when ever the page is viewed and the app is in foreground.

To allow for the page content's to report extra properties in the view event and to wait for page load to report the page view, we have `pageLoadRequiredForTracking` property on `BasePage` that you can override to be `true` and `PageViewTracker` that you can access from within the content of the page using

```dart
Provider.of<PageViewTracker>(context, listen:false).onPageLoaded(params)
```
